# Token Management Implementation

This document describes the token management system implemented for the React application, designed to work with a backend that provides both access and refresh tokens in the login response but doesn't have a separate refresh token endpoint.

## Overview

The implementation provides:
- ✅ Centralized token management
- ✅ Automatic token attachment to API requests
- ✅ Token expiration detection and user warnings
- ✅ Graceful logout when tokens expire
- ✅ Secure token storage in localStorage
- ✅ Redux integration for state management
- ✅ Backward compatibility with existing authentication flow

## Architecture

### Core Services

#### 1. Token Service (`src/services/tokenService.js`)
- Centralized token storage, retrieval, and validation
- JWT token parsing and expiration checking
- Secure localStorage operations
- Token format validation

#### 2. Auth Service (`src/services/authService.js`)
- Authentication state management
- Logout functionality
- Token validation on app startup
- Graceful handling of expired tokens

#### 3. API Service (`src/services/apiService.js`)
- Centralized axios instances with interceptors
- Automatic token attachment to requests
- 401/403 error handling with automatic logout
- Request/response logging for debugging

### React Hooks

#### 1. useApi Hook (`src/hooks/useApi.js`)
- Simplified API calls with automatic error handling
- Loading states management
- Consistent error messaging
- Token expiration handling

#### 2. useAuthInit Hook (`src/hooks/useAuthInit.js`)
- Authentication initialization on app startup
- Token validation and cleanup
- Loading states for app initialization

### Components

#### 1. Token Expiration Warning (`src/components/Common/TokenExpirationWarning.jsx`)
- Monitors token expiration in real-time
- Shows warning notifications when token is about to expire
- Automatic logout when token expires
- User-friendly expiration handling

## Usage Examples

### Making API Calls

#### Using the API Service Directly
```javascript
import { productsApiClient } from '../services/apiService';

// GET request
const response = await productsApiClient.get('/products');

// POST request
const response = await productsApiClient.post('/products', data);
```

#### Using the useApi Hook
```javascript
import { useApi } from '../hooks/useApi';

const MyComponent = () => {
  const { loading, error, execute } = useApi({
    showErrorMessage: true,
    errorContext: 'Products API'
  });

  const fetchProducts = async () => {
    try {
      const data = await execute(() => 
        productsApiClient.get('/products')
      );
      // Handle success
    } catch (error) {
      // Error is automatically handled by the hook
    }
  };

  return (
    <div>
      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      <button onClick={fetchProducts}>Fetch Products</button>
    </div>
  );
};
```

### Authentication State Management

#### Redux Actions
```javascript
import { setTokens, clearAuth } from '../redux/Slice/authSlice';

// Set tokens after login
dispatch(setTokens({
  accessToken: 'jwt_access_token',
  refreshToken: 'jwt_refresh_token',
  user: userObject
}));

// Clear authentication
dispatch(clearAuth());
```

#### Token Utilities
```javascript
import { 
  isAuthenticated, 
  isTokenExpired, 
  getTimeUntilExpiration 
} from '../services/tokenService';

// Check if user is authenticated
if (isAuthenticated()) {
  // User is logged in with valid token
}

// Check token expiration
const timeLeft = getTimeUntilExpiration(accessToken);
console.log(`Token expires in ${timeLeft} minutes`);
```

## Configuration

### Environment Variables
```env
VITE_APP_API_BASE_URL="http://localhost:8081/api/v1/public"
VITE_APP_AUTH_BASE_URL="http://localhost:8081/api/auth"
VITE_APP_KEYCLOAK_URL="http://localhost:8081/api/auth/login"
VITE_APP_API_BASE_PROFILE_URL="http://localhost:8080/api/profiles"
VITE_APP_API_PRODUCTS="http://localhost:8086/api/public/products"
```

### Token Storage
Tokens are stored in localStorage with the following keys:
- `accessToken`: JWT access token
- `refreshToken`: JWT refresh token (stored but not used for refresh)
- `user`: Parsed user data from JWT payload
- `profileComplete`: User profile completion status

## Security Considerations

### Current Implementation
- ✅ Tokens stored in localStorage (accessible to JavaScript)
- ✅ Automatic token expiration detection
- ✅ Secure token validation
- ✅ Automatic logout on token expiration
- ✅ User warnings before token expires

### Future Enhancements (Optional)
- 🔄 Consider httpOnly cookies for enhanced security
- 🔄 Implement refresh token endpoint in backend
- 🔄 Add token rotation for enhanced security
- 🔄 Implement remember me functionality

## Error Handling

### Token Expiration
1. **Warning Phase**: User gets notification 10 minutes before expiration
2. **Grace Period**: User can continue working with warnings
3. **Expiration**: Automatic logout and redirect to login page

### API Errors
- **401/403**: Automatic logout and redirect to login
- **Network Errors**: User-friendly error messages
- **Server Errors**: Graceful error handling with retry options

## Backward Compatibility

The implementation maintains full backward compatibility:
- ✅ Existing login flow unchanged
- ✅ Existing Redux store structure preserved
- ✅ Existing components work without modification
- ✅ Gradual migration path for API calls

## Migration Guide

### For Existing Components
1. Replace direct axios calls with API service:
   ```javascript
   // Before
   const response = await axios.get(url, { headers: { Authorization: `Bearer ${token}` } });
   
   // After
   const response = await apiClient.get(url); // Token automatically attached
   ```

2. Use the useApi hook for better error handling:
   ```javascript
   // Before
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState(null);
   
   // After
   const { loading, error, execute } = useApi();
   ```

## Testing

### Manual Testing
1. Login with valid credentials
2. Verify tokens are stored in localStorage
3. Make API calls and verify automatic token attachment
4. Wait for token to approach expiration (or modify token exp time)
5. Verify warning notifications appear
6. Verify automatic logout when token expires

### Integration Testing
- Test API interceptors with expired tokens
- Test authentication initialization on app startup
- Test token expiration warning component
- Test graceful error handling

## Troubleshooting

### Common Issues
1. **Tokens not attached to requests**: Check API service configuration
2. **Infinite redirect loops**: Verify token validation logic
3. **Warnings not showing**: Check token expiration times
4. **API calls failing**: Verify environment variables and endpoints

### Debug Mode
Enable debug logging by checking browser console for:
- Token validation messages
- API request/response logs
- Authentication state changes
- Error handling flows
