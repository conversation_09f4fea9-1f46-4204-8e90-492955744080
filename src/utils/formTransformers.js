import dayjs from '../utils/dayjsConfig';

export const transformProfileData = (data) => {
  const transformedData = {};
  console.log('Backend data to transform:', data);

  // Backend to frontend field mapping for vendor profiles
  const backendToFormFieldMapping = {
    // Vendor fields - map camelCase backend fields to snake_case form fields
    'businessName': 'business_name',
    'businessLogo': 'upload_logo',
    'businessEmail': 'business_email',
    'businessPhone': 'business_phone',
    'streetAddress': 'street_address',
    'city': 'city',
    'state': 'state',
    'zipCode': 'zip_code',
    'country': 'country',
    'businessCategory': 'business_category',
    'businessSubcategory': 'business_subcategory',
    'businessWebsite': 'business_website',
    'yearsOfExperience': 'years_of_experience',
    'certificate': 'upload_certificate',
    'productName': 'product_name',
    'productDescription': 'product_description',
    'productCategory': 'product_category',
    'registrationNumber': 'registration_number',
    'regionsSupported': 'regions_supported',
    'licenseDetails': 'license_details',
    // Customer fields - keep as is for now
    'firstName': 'firstName',
    'lastName': 'lastName',
    'emailAddress': 'emailAddress',
    'phoneNumber': 'phoneNumber',
    'dateOfBirth': 'dateOfBirth',
    'profilePicture': 'profilePicture',
    'primaryStreetAddress': 'primaryStreetAddress',
    'primaryCity': 'primaryCity',
    'primaryState': 'primaryState',
    'primaryZipCode': 'primaryZipCode',
    'designationOrRole': 'designationOrRole',
    'industryOrDomain': 'industryOrDomain',
    'companyName': 'companyName',
    'preferredVendorType': 'preferredVendorType',
    'subcategoriesOfInterest': 'subcategoriesOfInterest',
    'categoriesOfServicesNeeded': 'categoriesOfServicesNeeded',
    'receiveNotificationsFromVendors': 'receiveNotificationsFromVendors',
    'visibleToVendors': 'visibleToVendors',
    'profileComplete': 'profileComplete'
  };

  Object.keys(data).forEach(key => {
    if (key === 'id' || key === 'userId') {
      return;
    }

    // Map backend field name to form field name
    const formFieldName = backendToFormFieldMapping[key] || key;

    if (key === 'profilePicture' || key === 'businessLogo' || key === 'certificate') {
      transformedData[formFieldName] = data[key];
    } else if (key === 'dateOfBirth' && data[key]) {
      // Ensure we create a valid dayjs object
      try {
        const dateValue = dayjs(data[key]);
        transformedData[formFieldName] = dateValue.isValid() ? dateValue : null;
        console.log('Date transformation:', data[key], '->', transformedData[formFieldName]);
      } catch (error) {
        console.warn('Error parsing date for', key, ':', data[key], error);
        transformedData[formFieldName] = null;
      }
    } else if (key === 'receiveNotificationsFromVendors' || key === 'visibleToVendors' || key === 'profileComplete') {
      transformedData[formFieldName] = Boolean(data[key]);
    } else {
      transformedData[formFieldName] = data[key];
    }
  });

  console.log('Transformed data for form:', transformedData);
  console.log('Field mappings applied:', Object.keys(data).map(key => `${key} -> ${backendToFormFieldMapping[key] || key}`));
  return transformedData;
};

export const transformFormDataForBackend = (formData, isCustomer) => {
  console.log('Transforming form data for backend:', formData);
  console.log('Is customer:', isCustomer);

  if (isCustomer) {
    // Customer transformation
    const transformedData = {
      firstName: formData.firstName || '',
      lastName: formData.lastName || '',
      emailAddress: formData.emailAddress || '',
      phoneNumber: formData.phoneNumber || '',
      dateOfBirth: formData.dateOfBirth && dayjs(formData.dateOfBirth).isValid() ? dayjs(formData.dateOfBirth).format('YYYY-MM-DD') : null,
      profilePicture: formData.profilePicture || null,
      primaryStreetAddress: formData.primaryStreetAddress || '',
      primaryCity: formData.primaryCity || '',
      primaryState: formData.primaryState || '',
      primaryZipCode: formData.primaryZipCode || '',
      country: formData.country || '',
      secondaryStreetAddress: formData.secondaryStreetAddress || '',
      secondaryCity: formData.secondaryCity || '',
      secondaryState: formData.secondaryState || '',
      secondaryZipCode: formData.secondaryZipCode || '',
      designationOrRole: formData.designationOrRole || '',
      industryOrDomain: formData.industryOrDomain || '',
      companyName: formData.companyName || '',
      preferredVendorType: formData.preferredVendorType || '',
      subcategoriesOfInterest: formData.subcategoriesOfInterest || [],
      categoriesOfServicesNeeded: formData.categoriesOfServicesNeeded || [],
      receiveNotificationsFromVendors: formData.receiveNotificationsFromVendors || false,
      visibleToVendors: formData.visibleToVendors || false,
      userId: formData.userId,
      profileComplete: formData.profileComplete || false
    };

    console.log('Transformed customer data:', transformedData);
    return transformedData;
  } else {
    // Vendor transformation - using the actual field names from getFieldName function
    const transformedData = {
      userId: formData.userId,
      // Business basic details
      businessName: formData.business_name || formData['Business Name'] || formData.businessName || '',
      businessLogo: formData.upload_logo || formData['Upload Logo'] || formData.businessLogo || null,
      businessEmail: formData.business_email || formData['Business Email'] || formData.businessEmail || '',
      businessPhone: formData.business_phone || formData['Business Phone'] || formData.businessPhone || '',
      // Business address
      streetAddress: formData.street_address || formData['Street Address'] || formData.streetAddress || '',
      city: formData.city || formData['City'] || formData.city || '',
      state: formData.state || formData['State'] || formData.state || '',
      zipCode: formData.zip_code || formData['Zip Code'] || formData.zipCode || '',
      country: formData.country || formData['Country'] || formData.country || '',
      // Business details
      businessCategory: formData.business_category || formData['Business Category'] || formData.businessCategory || '',
      businessSubcategory: formData.business_subcategory || formData['Business Subcategory'] || formData.businessSubcategory || '',
      businessWebsite: formData.business_website || formData['Business Website'] || formData.businessWebsite || '',
      yearsOfExperience: formData.years_of_experience || formData['Years of Experience'] || formData.yearsOfExperience || '',
      certificate: formData.upload_certificate || formData['Upload Certificate'] || formData.certificate || null,
      // Product details
      productName: formData.product_name || formData['Product Name'] || formData.productName || '',
      productDescription: formData.product_description || formData['Product Description'] || formData.productDescription || '',
      productCategory: formData.product_category || formData['Product Category'] || formData.productCategory || '',
      // Preferences
      registrationNumber: formData.registration_number || formData['Registration Number'] || formData.registrationNumber || '',
      regionsSupported: formData.regions_supported || formData['Regions Supported'] || formData.regionsSupported || '',
      licenseDetails: formData.license_details || formData['License Details'] || formData.licenseDetails || '',
      profileComplete: formData.profileComplete || false
    };

    console.log('Transformed vendor data:', transformedData);
    return transformedData;
  }
};

const mapCustomerFields = (key, formData, transformedData) => {
  const fieldMappings = {
    'street_address': 'primaryStreetAddress',
    'city': 'primaryCity',
    'state': 'primaryState',
    'zip_code': 'primaryZipCode',
    'country': 'country',
    'email': 'emailAddress',
    'phone_number': 'phoneNumber',
    'date_of_birth': 'dateOfBirth',
    'receive_notifications_from_vendors': 'receiveNotificationsFromVendors',
    'visible_to_vendors': 'visibleToVendors',
    'designation/role': 'designationOrRole',
    'industry/domain': 'industryOrDomain'
  };

  if (fieldMappings[key]) {
    transformedData[fieldMappings[key]] = formData[key];
  } else {
    const backendFieldName = key.replace(/_([a-z])/g, (_, p1) => p1.toUpperCase());
    transformedData[backendFieldName] = formData[key];
  }
};

const mapVendorFields = (key, formData, transformedData) => {
  const fieldMappings = {
    'business_name': 'businessName',
    'business_email': 'businessEmail',
    'business_phone': 'businessPhone',
    'street_address': 'streetAddress',
    'city': 'city',
    'state': 'state',
    'zip_code': 'zipCode',
    'country': 'country',
    'business_category': 'businessCategory',
    'business_subcategory': 'businessSubcategory',
    'business_website': 'businessWebsite',
    'years_of_experience': 'yearsOfExperience',
    'product_name': 'productName',
    'product_description': 'productDescription',
    'product_category': 'productCategory',
    'registration_number': 'registrationNumber',
    'regions_supported': 'regionsSupported',
    'license_details': 'licenseDetails'
  };

  if (fieldMappings[key]) {
    transformedData[fieldMappings[key]] = formData[key];
  } else {
    const backendFieldName = key.replace(/_([a-z])/g, (_, p1) => p1.toUpperCase());
    transformedData[backendFieldName] = formData[key];
  }
};

export const validateRequiredFields = (data, isCustomer) => {
  console.log('Validating required fields:', data);
  console.log('Is customer:', isCustomer);

  if (isCustomer) {
    const missingFields = [];
    if (!data.firstName) missingFields.push('First Name');
    if (!data.lastName) missingFields.push('Last Name');
    if (!data.emailAddress) missingFields.push('Email Address');

    if (missingFields.length > 0) {
      throw new Error(`Missing required personal information fields: ${missingFields.join(', ')}`);
    }
  } else {
    const missingFields = [];
    if (!data.businessName) missingFields.push('Business Name');
    if (!data.businessEmail) missingFields.push('Business Email');

    if (missingFields.length > 0) {
      throw new Error(`Missing required business information fields: ${missingFields.join(', ')}`);
    }
  }

  console.log('Validation passed');
};