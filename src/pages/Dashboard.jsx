import React, { useState } from 'react';
import {
  PlusOutlined,
  ArrowRightOutlined,
  CalendarOutlined,
  Bar<PERSON><PERSON>Outlined,
  BellOutlined,
  UserOutlined,
  SearchOutlined,
  DownOutlined,
  RiseOutlined,
  FallOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  InfoCircleOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Table, 
  Button, 
  Progress, 
  Select, 
  Tag, 
  Divider, 
  Avatar, 
  Dropdown, 
  Menu, 
  DatePicker, 
  Input, 
  Space, 
  Typography, 
  Badge, 
  Tooltip as AntTooltip,
  Segmented
} from 'antd';
import CustomSider from '../components/Pages/CustomSider';
import PageHeader from '../components/Common/PageHeader';
import { useTranslation } from 'react-i18next';
import '../styles/Dashboard.css';
import ReduxDebugger from '../components/Debug/ReduxDebugger';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  <PERSON>A<PERSON>s, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  AreaChart, 
  Area, 
  LineChart, 
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const { Content } = Layout;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const Dashboard = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { t } = useTranslation();
  //redux 
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  // Mock data for dashboard statistics
  const statsCards = [
    {
      title: t('Total Projects/Orders'),
      value: '29,300',
      change: '*****%',
      isPositive: true,
      subtitle: t('Updated Just Now'),
      progressColor: '#6366f1',
      progressPercent: 72,
      icon: '📊',
      trend: [65, 59, 80, 81, 56, 55, 40, 45, 60, 70, 75, 80]
    },
    {
      title: t('Open/Pending Orders'),
      value: '18,722',
      change: '*****%',
      isPositive: true,
      subtitle: t('Today, 10:00'),
      progressColor: '#f59e0b',
      progressPercent: 85,
      icon: '⏳',
      trend: [28, 48, 40, 19, 86, 27, 90, 75, 60, 65, 70, 85]
    },
    {
      title: t('Completed'),
      value: '10,578',
      change: '+1.09%',
      isPositive: true,
      subtitle: t('This Month'),
      progressColor: '#10b981',
      progressPercent: 92,
      icon: '✅',
      trend: [35, 40, 45, 50, 49, 60, 70, 75, 80, 85, 90, 92]
    },
  ];

  // Mock data for deals chart
  const dealsData = [
    { name: t('Jan'), negotiation: 10, closing: 15, lead: 5 },
    { name: t('Feb'), negotiation: 15, closing: 20, lead: 8 },
    { name: t('Mar'), negotiation: 8, closing: 12, lead: 10 },
    { name: t('Apr'), negotiation: 12, closing: 18, lead: 5 },
    { name: t('May'), negotiation: 20, closing: 10, lead: 15 },
    { name: t('Jun'), negotiation: 15, closing: 8, lead: 12 },
    { name: t('Jul'), negotiation: 10, closing: 15, lead: 8 },
    { name: t('Aug'), negotiation: 18, closing: 12, lead: 10 },
    { name: t('Sep'), negotiation: 12, closing: 20, lead: 5 },
    { name: t('Oct'), negotiation: 8, closing: 15, lead: 12 },
    { name: t('Nov'), negotiation: 15, closing: 8, lead: 20 },
    { name: t('Dec'), negotiation: 20, closing: 12, lead: 15 },
  ];

  // Mock data for recent activities
  const recentActivities = [
    {
      date: t('Today, 10:30 AM'),
      action: t('Project Completed'),
      details: t('Mobile App UI Design marked as Closed'),
      user: 'David Brown'
    },
    {
      date: t('Yesterday, 5:45 PM'),
      action: t('Order Status Updated'),
      details: t('Order #12345 changed to Completed'),
      user: 'System'
    },
    {
      date: t('Yesterday, 3:15 PM'),
      action: t('Payment Received'),
      details: t('$500 for Order #67890'),
      user: 'Finance Team'
    },
    {
      date: t('March 6, 2:00 PM'),
      action: t('New User Registered'),
      details: t('New User Registered'),
      user: 'Admin'
    },
    {
      date: t('March 1, 4:30 PM'),
      action: t('New Project Created'),
      details: t('E-commerce Website created'),
      user: 'Alice Smith'
    },
  ];

  // Mock data for notifications
  const notifications = [
    {
      title: t('Your Project #12345 has been Completed'),
      time: t('Today, 9:15 AM')
    },
    {
      title: t('Scheduled for March 6, 12:00 AM to 02:00 AM'),
      time: t('Yesterday, 2:00 PM')
    },
    {
      title: t('Check out our latest product collection!'),
      time: t('March 5, 10:30 AM')
    },
    {
      title: t('Account Verification Needed'),
      time: t('Today, 9:30 AM')
    },
    {
      title: t('Scheduled for March 6, 12:00 AM to 02:00 AM'),
      time: t('Yesterday, 2:00 PM')
    },
    {
      title: t('Check out our latest product collection!'),
      time: t('March 4, 10:30 AM')
    },
    {
      title: t('Check out our latest product collection!'),
      time: t('March 3, 10:30 AM')
    },
    {
      title: t('Your Project #12345 has been Completed'),
      time: t('Today, 9:30 AM')
    }
  ];



  // Column definitions for the activity table
  const columns = [
    {
      title: t('Date'),
      dataIndex: 'date',
      key: 'date',
      width: '20%',
    },
    {
      title: t('Action'),
      dataIndex: 'action',
      key: 'action',
      width: '20%',
      render: (text) => <span style={{ color: '#1890ff' }}>{text}</span>,
    },
    {
      title: t('Details'),
      dataIndex: 'details',
      key: 'details',
      width: '40%',
    },
    {
      title: t('User'),
      dataIndex: 'user',
      key: 'user',
      width: '20%',
    },
  ];

  const { Option } = Select;

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout className="dashboard-container" style={{
        width: '100%',
        maxWidth: '100%',
        margin: '0 auto',
        marginLeft: collapsed ? '80px' : '220px',
        transition: 'all 0.3s ease',
        backgroundColor: '#f8fafc'
      }}>
        {/* Modern Header Section */}
        <div className="dashboard-header" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '24px 24px 0',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <div>
            <Title level={3} style={{ margin: 0, color: '#1e293b', fontWeight: 700,textAlign: 'left' }}>
              {t("Dashboard")}
            </Title>
            <Text style={{ fontSize: '14px', color: '#64748b', marginTop: '4px', display: 'block' }}>
              {t("Welcome back, {{name}}! Here's what's happening today.", { name: user?.given_name || t('User') })}
            </Text>
          </div>
          
          <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
            <RangePicker 
              placeholder={[t('Start date'), t('End date')]}
              style={{ 
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
              }}
              suffixIcon={<CalendarOutlined style={{ color: '#64748b' }} />}
            />
            
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              style={{
                background: '#9e3ca2',
                borderColor: '#9e3ca2',
                borderRadius: '8px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontWeight: 500
              }}
                    onClick={() => navigate('/order')}

            >
              {t('New Order')}
              <ArrowRightOutlined style={{ fontSize: '12px' }} />
            </Button>
            
            <Dropdown overlay={
              <Menu>
                <Menu.Item key="1" icon={<UserOutlined />}>Profile</Menu.Item>
                <Menu.Item key="2" icon={<UserOutlined />}>Settings</Menu.Item>
                <Menu.Divider />
                <Menu.Item key="3" danger>Logout</Menu.Item>
              </Menu>
            } trigger={['click']}>
              <Avatar 
                style={{ cursor: 'pointer', border: '2px solid #e2e8f0' }}
                src="https://xsgames.co/randomusers/avatar.php?g=pixel"
              />
            </Dropdown>
          </div>
        </div>
        
        {/* Dashboard Filters */}
        <div style={{ 
          padding: '16px 24px', 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '12px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Segmented 
              options={[t('Overview'), t('Analytics'), t('Projects'), t('Team')]} 
              defaultValue={t('Overview')}
              style={{
                backgroundColor: '#f1f5f9',
                padding: '4px',
                borderRadius: '8px'
              }}
            />
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Input 
              prefix={<SearchOutlined style={{ color: '#94a3b8' }} />}
              placeholder={t("Search...")} 
              style={{ 
                borderRadius: '8px',
                width: '200px',
                border: '1px solid #e2e8f0'
              }} 
            />
            
            <Button 
              icon={<FilterOutlined />} 
              style={{ 
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                padding: 0
              }}
            />
            
            <Button 
              icon={<ReloadOutlined />} 
              style={{ 
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                padding: 0
              }}
            />
          </div>
        </div>

        <Content style={{ padding: '0 24px 24px' }}>
          {/* Stats Cards */}
          <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
            {statsCards.map((stat, index) => (
              <Col xs={24} sm={12} lg={8} key={index}>
                <Card 
                  className="stats-card"
                  style={{ 
                    borderRadius: '12px',
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    height: '100%',
                    borderLeft: `4px solid ${stat.progressColor}`
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                    <div className="stats-title" style={{ 
                      fontSize: '14px', 
                      color: '#64748b', 
                      marginBottom: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <span style={{ fontSize: '20px', lineHeight: 1 }}>{stat.icon}</span>
                      {stat.title}
                    </div>
                    <AntTooltip title="More information">
                      <InfoCircleOutlined style={{ color: '#94a3b8', cursor: 'pointer' }} />
                    </AntTooltip>
                  </div>
                  
                  <div style={{ display: 'flex', alignItems: 'flex-end', gap: '12px', marginBottom: '16px' }}>
                    <span className="stats-value" style={{ 
                      fontSize: '28px', 
                      fontWeight: '700', 
                      color: '#1e293b',
                      lineHeight: 1.2
                    }}>
                      {stat.value}
                    </span>
                    <span className="stats-change" style={{
                      fontSize: '13px',
                      fontWeight: '600',
                      color: stat.isPositive ? '#10b981' : '#ef4444',
                      backgroundColor: stat.isPositive ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
                      padding: '4px 10px',
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}>
                      {stat.isPositive ? <RiseOutlined /> : <FallOutlined />} {stat.change}
                    </span>
                  </div>
                  
                  <div style={{ marginBottom: '16px' }}>
                    <ResponsiveContainer width="100%" height={40}>
                      <AreaChart data={stat.trend.map((value, i) => ({ name: i, value }))} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id={`colorGradient${index}`} x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={stat.progressColor} stopOpacity={0.3}/>
                            <stop offset="95%" stopColor={stat.progressColor} stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Area 
                          type="monotone" 
                          dataKey="value" 
                          stroke={stat.progressColor} 
                          strokeWidth={2}
                          fill={`url(#colorGradient${index})`} 
                          dot={false}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div className="stats-subtitle" style={{ 
                      fontSize: '12px', 
                      color: '#94a3b8',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}>
                      <ClockCircleOutlined /> {stat.subtitle}
                    </div>
                    <Progress
                      type="circle"
                      percent={stat.progressPercent}
                      width={36}
                      strokeColor={stat.progressColor}
                      trailColor="#f1f5f9"
                      strokeWidth={10}
                      format={() => `${stat.progressPercent}%`}
                      style={{ fontSize: '10px' }}
                    />
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Deals and Notifications Section */}
          <Row gutter={[24, 24]}>
            {/* Deals Chart */}
            <Col xs={24} lg={16}>
              <Card
                className="chart-card"
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <BarChartOutlined style={{ color: '#4f46e5' }} />
                      <span style={{ fontWeight: 600, color: '#1e293b' }}>{t('Sales Performance')}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', fontSize: '12px', color: '#64748b', gap: '4px' }}>
                      <ClockCircleOutlined />
                      {t('Updated Just Now')}
                    </div>
                  </div>
                }
                extra={
                  <Space>
                    <Select 
                      defaultValue="month" 
                      style={{ width: 120 }} 
                      size="middle"
                      bordered={false}
                      suffixIcon={<DownOutlined style={{ fontSize: '12px', color: '#64748b' }} />}
                      dropdownStyle={{ borderRadius: '8px', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}
                    >
                      <Option value="month">{t('This Month')}</Option>
                      <Option value="quarter">{t('This Quarter')}</Option>
                      <Option value="year">{t("This Year")}</Option>
                    </Select>
                    <Button 
                      type="text" 
                      icon={<ReloadOutlined style={{ color: '#64748b' }} />} 
                      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    />
                  </Space>
                }
                style={{ 
                  borderRadius: '12px', 
                  height: '100%', 
                  border: 'none', 
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  overflow: 'hidden'
                }}
                bodyStyle={{ padding: '0 20px 20px' }}
              >
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '16px', marginTop: '8px' }}>
                  <Tag 
                    color="#a5b4fc" 
                    style={{ 
                      borderRadius: '20px', 
                      padding: '4px 12px', 
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      fontWeight: 500,
                      border: 'none',
                      color: '#4338ca'
                    }}
                  >
                    <span style={{ display: 'inline-block', width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#4338ca', opacity: 0.8 }}></span>
                    {t('150 Proposal Sent')}
                  </Tag>
                  <Tag 
                    color="#fcd34d" 
                    style={{ 
                      borderRadius: '20px', 
                      padding: '4px 12px', 
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      fontWeight: 500,
                      border: 'none',
                      color: '#92400e'
                    }}
                  >
                    <span style={{ display: 'inline-block', width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#92400e', opacity: 0.8 }}></span>
                    {t('93 Negotiation')}
                  </Tag>
                  <Tag 
                    color="#a7f3d0" 
                    style={{ 
                      borderRadius: '20px', 
                      padding: '4px 12px', 
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      fontWeight: 500,
                      border: 'none',
                      color: '#065f46'
                    }}
                  >
                    <span style={{ display: 'inline-block', width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#065f46', opacity: 0.8 }}></span>
                    {t('36 Closing')}
                  </Tag>
                  <Tag 
                    color="#ddd6fe" 
                    style={{ 
                      borderRadius: '20px', 
                      padding: '4px 12px', 
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      fontWeight: 500,
                      border: 'none',
                      color: '#5b21b6'
                    }}
                  >
                    <span style={{ display: 'inline-block', width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#5b21b6', opacity: 0.8 }}></span>
                    {t('10 Lead')}
                  </Tag>
                </div>
                
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={dealsData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 0,
                      bottom: 5,
                    }}
                    barSize={20}
                    barGap={8}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e2e8f0" />
                    <XAxis 
                      dataKey="name" 
                      axisLine={false} 
                      tickLine={false} 
                      tick={{ fill: '#64748b', fontSize: 12 }}
                    />
                    <YAxis 
                      axisLine={false} 
                      tickLine={false} 
                      tick={{ fill: '#64748b', fontSize: 12 }}
                    />
                    <Tooltip 
                      cursor={{ fill: 'rgba(224, 231, 255, 0.2)' }}
                      contentStyle={{ 
                        borderRadius: '8px', 
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', 
                        border: 'none', 
                        padding: '12px' 
                      }}
                    />
                    <Legend 
                      iconType="circle" 
                      iconSize={8}
                      wrapperStyle={{ paddingTop: '16px' }}
                    />
                    <Bar 
                      dataKey="negotiation" 
                      stackId="a" 
                      fill="#a5b4fc"
                      radius={[4, 4, 0, 0]}
                      name="Negotiation"
                      animationDuration={1500}
                    />
                    <Bar 
                      dataKey="closing" 
                      stackId="a" 
                      fill="#a7f3d0"
                      radius={[4, 4, 0, 0]}
                      name="Closing"
                      animationDuration={1500}
                      animationDelay={300}
                    />
                    <Bar 
                      dataKey="lead" 
                      stackId="a" 
                      fill="#ddd6fe"
                      radius={[4, 4, 0, 0]}
                      name="Lead"
                      animationDuration={1500}
                      animationDelay={600}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>

            {/* Notifications */}
            <Col xs={24} lg={8}>
              <Card
                className="notifications-card"
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Badge count={notifications.length} size="small" offset={[4, 0]}>
                        <BellOutlined style={{ color: '#4f46e5', fontSize: '16px' }} />
                      </Badge>
                      <span style={{ fontWeight: 600, color: '#1e293b' }}>{t("Notifications")}</span>
                    </div>
                    <Button 
                      type="text" 
                      size="small"
                      style={{ color: '#4f46e5', fontWeight: 500, fontSize: '12px' }}
                    >
                      {t("Mark all as read")}
                    </Button>
                  </div>
                }
                style={{
                  borderRadius: '12px',
                  height: '100%',
                  border: 'none',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
                bodyStyle={{ padding: '0' }}
              >
                <div className="notifications-container" style={{ 
                  height: '400px', 
                  overflowY: 'auto',
                  padding: '0 20px'
                }}>
                  {notifications.map((notification, index) => (
                    <div 
                      key={index} 
                      style={{ 
                        padding: '16px 0', 
                        borderBottom: '1px solid #f1f5f9',
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '12px'
                      }}
                    >
                      <div style={{ 
                        width: '36px', 
                        height: '36px', 
                        borderRadius: '8px', 
                        backgroundColor: '#e0e7ff', 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <BellOutlined style={{ color: '#4f46e5', fontSize: '16px' }} />
                      </div>
                      <div style={{ flex: 1 }}>
                        <div style={{ 
                          fontSize: '14px', 
                          marginBottom: '4px', 
                          color: '#1e293b',
                          fontWeight: 500,
                          lineHeight: 1.4
                        }}>
                          {notification.title}
                        </div>
                        <div style={{ 
                          fontSize: '12px', 
                          color: '#64748b',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}>
                          <ClockCircleOutlined style={{ fontSize: '10px' }} /> {notification.time}
                        </div>
                      </div>
                      <div>
                        <Button 
                          type="text" 
                          size="small" 
                          shape="circle" 
                          icon={<InfoCircleOutlined style={{ color: '#94a3b8' }} />} 
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>

          {/* Recent Activity Table */}
          <Card
            className="activity-card"
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <UserOutlined style={{ color: '#4f46e5', fontSize: '16px' }} />
                  <span style={{ fontWeight: 600, color: '#1e293b' }}>{t("Recent Activity")}</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Select 
                    defaultValue="all" 
                    style={{ width: 120 }} 
                    size="small"
                    bordered={false}
                    suffixIcon={<DownOutlined style={{ fontSize: '12px', color: '#64748b' }} />}
                  >
                    <Option value="all">{t("All Activities")}</Option>
                    <Option value="orders">{t("Orders")}</Option>
                    <Option value="projects">{t("Projects")}</Option>
                    <Option value="users">{t("Users")}</Option>
                  </Select>
                  <Button 
                    type="text" 
                    icon={<ReloadOutlined style={{ color: '#64748b' }} />} 
                    size="small"
                  />
                </div>
              </div>
            }
            style={{
              marginTop: '24px',
              borderRadius: '12px',
              border: 'none',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              overflow: 'hidden'
            }}
            bodyStyle={{ padding: '0' }}
          >
            <Table
              className="activity-table"
              columns={[
                {
                  title: t('Date'),
                  dataIndex: 'date',
                  key: 'date',
                  width: '20%',
                  render: (text) => (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <div style={{ 
                        width: '36px', 
                        height: '36px', 
                        borderRadius: '8px', 
                        backgroundColor: '#e0e7ff', 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center' 
                      }}>
                        <CalendarOutlined style={{ color: '#4f46e5', fontSize: '16px' }} />
                      </div>
                      <div>
                        <div style={{ color: '#1e293b', fontWeight: 500 }}>{text.split(',')[0]}</div>
                        <div style={{ fontSize: '12px', color: '#64748b' }}>{text.split(',')[1]}</div>
                      </div>
                    </div>
                  )
                },
                {
                  title: t('Action'),
                  dataIndex: 'action',
                  key: 'action',
                  width: '20%',
                  render: (text) => (
                    <Tag 
                      color="#4f46e5" 
                      style={{ 
                        borderRadius: '20px', 
                        padding: '4px 12px', 
                        fontSize: '12px',
                        fontWeight: 500,
                        border: 'none'
                      }}
                    >
                      {text}
                    </Tag>
                  ),
                },
                {
                  title: t('Details'),
                  dataIndex: 'details',
                  key: 'details',
                  width: '40%',
                  render: (text) => <span style={{ color: '#1e293b' }}>{text}</span>,
                },
                {
                  title: t('User'),
                  dataIndex: 'user',
                  key: 'user',
                  width: '20%',
                  render: (text) => (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Avatar size="small" style={{ backgroundColor: '#4f46e5' }}>{text.charAt(0)}</Avatar>
                      <span style={{ color: '#1e293b' }}>{text}</span>
                    </div>
                  ),
                },
              ]}
              dataSource={recentActivities}
              pagination={false}
              rowKey={(record) => record.date + record.action}
              style={{ marginBottom: '0' }}
            />
            <div className="view-all-btn" style={{
              textAlign: 'center',
              padding: '16px',
              borderTop: '1px solid #f1f5f9'
            }}>
              <Button 
                type="primary" 
                icon={<ArrowRightOutlined />}
                style={{
                  background: '#4f46e5',
                  borderColor: '#4f46e5',
                  borderRadius: '8px',
                  padding: '0 20px',
                  height: '36px',
                  fontWeight: 500,
                  boxShadow: '0 2px 8px rgba(79, 70, 229, 0.2)'
                }}
              >
                {t("View All Activities")}
              </Button>
            </div>
          </Card>

          {/* Debug Card - Only visible in development */}
          {process.env.NODE_ENV !== 'production' && (
            <div style={{ marginTop: '16px' }}>
              <ReduxDebugger />
            </div>
          )}
        </Content>


      </Layout>
    </Layout>
  );
};

export default Dashboard;