import React from 'react';
import { Layout, Typography, Row, Col } from 'antd';
import ChatDebug from '../components/ChatDebug/ChatDebug';
import ChatBot from '../components/Chatbot/ChatBot';

const { Content } = Layout;
const { Title, Paragraph } = Typography;

const ChatTestPage = () => {
  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '20px' }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <div style={{ textAlign: 'center', marginBottom: '30px' }}>
            <Title level={1} style={{ color: '#9e3ca2' }}>
              🤖 Bond AI Integration Test Suite
            </Title>
            <Paragraph style={{ fontSize: '16px', color: '#666' }}>
              Test the enhanced Bond AI chatbot with B2B marketplace training data integration
            </Paragraph>
          </div>
          
          <Row gutter={[24, 24]} align="top">
            {/* Debug Panel */}
            <Col xs={24} lg={14}>
              <ChatDebug />
            </Col>
            
            {/* Chat Component */}
            <Col xs={24} lg={10}>
              <div style={{ position: 'sticky', top: '20px' }}>
                <ChatBot 
                  title="Bond AI (Enhanced)"
                  height={600}
                  width="100%"
                />
              </div>
            </Col>
          </Row>
          
          {/* Test Scenarios */}
          <div style={{ marginTop: '40px', padding: '20px', background: 'white', borderRadius: '8px' }}>
            <Title level={3}>🧪 Test Scenarios</Title>
            <Paragraph>
              Try these sample queries to test Bond AI's enhanced B2B marketplace knowledge:
            </Paragraph>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px', marginTop: '20px' }}>
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>🏢 Service Discovery</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "I need cloud infrastructure services for my SaaS startup"
                </Paragraph>
              </div>
              
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>💰 Budget Planning</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "What's the typical budget for ERP implementation in EdTech?"
                </Paragraph>
              </div>
              
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>🔒 Security Services</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "I need cybersecurity audit services for my finance company"
                </Paragraph>
              </div>
              
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>⚙️ DevOps Solutions</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "Help me find DevOps automation services for fintech"
                </Paragraph>
              </div>
              
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>📊 Analytics & BI</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "I want to create business analytics dashboards for my SaaS"
                </Paragraph>
              </div>
              
              <div style={{ padding: '16px', background: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                <Title level={5} style={{ color: '#9e3ca2', marginBottom: '8px' }}>🏥 Healthcare Tech</Title>
                <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                  "What ML deployment services are available for healthcare?"
                </Paragraph>
              </div>
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default ChatTestPage;
