import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Badge, Card, List, Avatar, Typography, Button } from 'antd';
import { BellOutlined, MailOutlined, CheckOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import CustomSider from '../components/Pages/CustomSider';
import PageHeader from '../components/Common/PageHeader';
import { EmailClientPage } from '../components/Email/pages/EmailClientPage';
import { fetchEmails } from '../components/Email/service/emailService';

const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const NotificationsPage = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('1');
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [emails, setEmails] = useState([]);
  const [emailCount, setEmailCount] = useState(0);

  // Mock notifications data
  useEffect(() => {
    const mockNotifications = [
      {
        id: 1,
        title: 'Your Project #12345 has been Completed',
        description: 'The vendor has marked your project as completed. Please review and confirm.',
        time: 'Today, 9:15 AM',
        read: false,
        type: 'project'
      },
      {
        id: 2,
        title: 'New Quote Received',
        description: 'You have received a new quote for your project request.',
        time: 'Yesterday, 2:00 PM',
        read: false,
        type: 'quote'
      },
      {
        id: 3,
        title: 'Payment Confirmation',
        description: 'Your payment for Order #67890 has been processed successfully.',
        time: 'March 5, 10:30 AM',
        read: true,
        type: 'payment'
      },
      {
        id: 4,
        title: 'Account Verification Needed',
        description: 'Please verify your account to access all features.',
        time: 'Today, 9:30 AM',
        read: false,
        type: 'account'
      },
      {
        id: 5,
        title: 'System Maintenance',
        description: 'Scheduled for March 6, 12:00 AM to 02:00 AM',
        time: 'Yesterday, 2:00 PM',
        read: true,
        type: 'system'
      },
      {
        id: 6,
        title: 'New Product Collection',
        description: 'Check out our latest product collection!',
        time: 'March 4, 10:30 AM',
        read: true,
        type: 'marketing'
      },
      {
        id: 7,
        title: 'Order Status Update',
        description: 'Your order #54321 has been shipped.',
        time: 'March 3, 10:30 AM',
        read: true,
        type: 'order'
      }
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.read).length);

    // Fetch emails
    const getEmails = async () => {
      try {
        const data = await fetchEmails();
        setEmails(data);
        setEmailCount(data.length);
      } catch (error) {
        console.error('Error fetching emails:', error);
      }
    };

    getEmails();
  }, []);

  const markAsRead = (id) => {
    setNotifications(
      notifications.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'project':
        return <Avatar style={{ backgroundColor: '#1890ff' }}>P</Avatar>;
      case 'quote':
        return <Avatar style={{ backgroundColor: '#52c41a' }}>Q</Avatar>;
      case 'payment':
        return <Avatar style={{ backgroundColor: '#faad14' }}>$</Avatar>;
      case 'account':
        return <Avatar style={{ backgroundColor: '#722ed1' }}>A</Avatar>;
      case 'system':
        return <Avatar style={{ backgroundColor: '#f5222d' }}>S</Avatar>;
      case 'marketing':
        return <Avatar style={{ backgroundColor: '#13c2c2' }}>M</Avatar>;
      case 'order':
        return <Avatar style={{ backgroundColor: '#eb2f96' }}>O</Avatar>;
      default:
        return <Avatar style={{ backgroundColor: '#1890ff' }}>N</Avatar>;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout style={{
        padding: '0 16px',
        width: '100%',
        maxWidth: 'auto',
        margin: '0 auto',
        marginLeft: collapsed ? '80px' : '220px',
        transition: 'margin-left 0.3s',
        backgroundColor: '#f5f7fa'
      }}>
        <PageHeader
          title={t("Notifications & Messages")}
          timestamp={`Last Updated: ${new Date().toLocaleString()}`}
          onSearch={(value) => console.log('Search:', value)}
          showSearch={true}
          userAvatar="https://xsgames.co/randomusers/avatar.php?g=pixel"
        />

        <Content style={{ margin: '16px 0' }}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              activeTab === '1' && unreadCount > 0 ? (
                <Button
                  type="text"
                  onClick={markAllAsRead}
                  icon={<CheckOutlined />}
                >
                  Mark all as read
                </Button>
              ) : null
            }
          >
            <TabPane
              tab={
                <span>
                  <Badge count={unreadCount} size="small">
                    <BellOutlined style={{ fontSize: '18px', marginRight: '8px' }} />
                    Notifications
                  </Badge>
                </span>
              }
              key="1"
            >
              <Card>
                <List
                  itemLayout="horizontal"
                  dataSource={notifications}
                  renderItem={item => (
                    <List.Item
                      actions={[
                        !item.read && (
                          <Button
                            type="text"
                            size="small"
                            onClick={() => markAsRead(item.id)}
                          >
                            Mark as read
                          </Button>
                        )
                      ]}
                      style={{
                        backgroundColor: item.read ? 'transparent' : 'rgba(24, 144, 255, 0.05)',
                        padding: '12px',
                        borderRadius: '8px',
                        marginBottom: '8px'
                      }}
                    >
                      <List.Item.Meta
                        avatar={getNotificationIcon(item.type)}
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Text strong={!item.read}>{item.title}</Text>
                            <Text type="secondary" style={{ fontSize: '12px' }}>{item.time}</Text>
                          </div>
                        }
                        description={item.description}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </TabPane>
            <TabPane
              tab={
                <span>
                  <Badge count={emailCount} size="small">
                    <MailOutlined style={{ fontSize: '18px', marginRight: '8px' }} />
                    Email
                  </Badge>
                </span>
              }
              key="2"
            >
              <div style={{
                height: 'calc(100vh - 200px)',
                overflow: 'hidden',
                borderRadius: '8px',
                backgroundColor: '#fff'
              }}>
                <EmailClientPage />
              </div>
            </TabPane>
          </Tabs>
        </Content>
      </Layout>
    </Layout>
  );
};

export default NotificationsPage;
