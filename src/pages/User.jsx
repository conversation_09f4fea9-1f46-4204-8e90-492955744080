import React, { useState } from 'react';
import { Layout } from 'antd';
import CustomSider from '../components/Pages/CustomSider';
import PageHeader from '../components/Common/PageHeader';
import UserTable from '../components/Managed-User/UserTable';

const { Content } = Layout;

const User = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (value) => {
    setSearchTerm(value); // Pass this to UserTable via props if filtering is handled there
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout
        style={{
          marginLeft: collapsed ? '80px' : '220px',
          transition: 'margin-left 0.3s',
        }}
      >
        <Content className="user-container" style={{ padding: '24px' }}>
          <PageHeader
            title="Manage Users"
            timestamp="Last updated on Today"
            onSearch={handleSearch}
            showSearch={true}
            userAvatar="https://xsgames.co/randomusers/avatar.php?g=pixel"
          />
          <div className="user-table-wrapper">
            <UserTable searchTerm={searchTerm} />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default User;
