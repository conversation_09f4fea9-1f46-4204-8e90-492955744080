/**
 * Custom hook for making authenticated API calls
 * Provides automatic error handling, loading states, and retry logic
 */

import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import apiClient, { makeApiRequest } from '../services/apiService';
import { setRefreshing, setRefreshError, refreshTokenSuccess } from '../redux/Slice/authSlice';
import { refreshAccessToken } from '../services/authService';

/**
 * Custom hook for API operations
 * @param {Object} options - Configuration options
 * @param {boolean} options.showSuccessMessage - Show success message on successful requests
 * @param {boolean} options.showErrorMessage - Show error message on failed requests
 * @param {string} options.successMessage - Custom success message
 * @param {string} options.errorContext - Context for error messages
 * @returns {Object} API utilities and state
 */
export const useApi = (options = {}) => {
  const {
    showSuccessMessage = false,
    showErrorMessage = true,
    successMessage = 'Operation completed successfully',
    errorContext = 'API request'
  } = options;

  const dispatch = useDispatch();
  const { isRefreshing } = useSelector((state) => state.auth);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  /**
   * Execute an API request with automatic error handling
   * @param {Function} requestFn - Function that returns an axios request
   * @param {Object} requestOptions - Additional options for this request
   * @returns {Promise} API response data
   */
  const execute = useCallback(async (requestFn, requestOptions = {}) => {
    const {
      showSuccess = showSuccessMessage,
      showError = showErrorMessage,
      successMsg = successMessage,
      errorCtx = errorContext
    } = requestOptions;

    setLoading(true);
    setError(null);

    try {
      const response = await makeApiRequest(requestFn, errorCtx);
      
      setData(response.data);
      
      if (showSuccess) {
        message.success(successMsg);
      }
      
      return response.data;
      
    } catch (err) {
      setError(err.message);
      
      if (showError) {
        message.error(err.message);
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [showSuccessMessage, showErrorMessage, successMessage, errorContext]);

  /**
   * Refresh authentication token
   * @returns {Promise<boolean>} Success status
   */
  const refreshToken = useCallback(async () => {
    if (isRefreshing) {
      return false;
    }

    dispatch(setRefreshing(true));

    try {
      const { accessToken, refreshToken: newRefreshToken, user } = await refreshAccessToken();
      
      dispatch(refreshTokenSuccess({
        accessToken,
        refreshToken: newRefreshToken,
        user
      }));
      
      return true;
    } catch (error) {
      dispatch(setRefreshError(error.message));
      return false;
    }
  }, [dispatch, isRefreshing]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Reset hook state
   */
  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    // State
    loading,
    error,
    data,
    isRefreshing,
    
    // Actions
    execute,
    refreshToken,
    clearError,
    reset,
    
    // Direct API client access for advanced usage
    apiClient
  };
};

/**
 * Hook for making GET requests
 * @param {string} url - API endpoint URL
 * @param {Object} options - Request options
 * @returns {Object} Request utilities and state
 */
export const useGet = (url, options = {}) => {
  const api = useApi(options);
  
  const get = useCallback((params = {}, requestOptions = {}) => {
    return api.execute(() => apiClient.get(url, { params }), {
      errorCtx: `GET ${url}`,
      ...requestOptions
    });
  }, [api, url]);

  return {
    ...api,
    get
  };
};

/**
 * Hook for making POST requests
 * @param {string} url - API endpoint URL
 * @param {Object} options - Request options
 * @returns {Object} Request utilities and state
 */
export const usePost = (url, options = {}) => {
  const api = useApi(options);
  
  const post = useCallback((data = {}, requestOptions = {}) => {
    return api.execute(() => apiClient.post(url, data), {
      errorCtx: `POST ${url}`,
      ...requestOptions
    });
  }, [api, url]);

  return {
    ...api,
    post
  };
};

/**
 * Hook for making PUT requests
 * @param {string} url - API endpoint URL
 * @param {Object} options - Request options
 * @returns {Object} Request utilities and state
 */
export const usePut = (url, options = {}) => {
  const api = useApi(options);
  
  const put = useCallback((data = {}, requestOptions = {}) => {
    return api.execute(() => apiClient.put(url, data), {
      errorCtx: `PUT ${url}`,
      ...requestOptions
    });
  }, [api, url]);

  return {
    ...api,
    put
  };
};

/**
 * Hook for making DELETE requests
 * @param {string} url - API endpoint URL
 * @param {Object} options - Request options
 * @returns {Object} Request utilities and state
 */
export const useDelete = (url, options = {}) => {
  const api = useApi(options);
  
  const del = useCallback((requestOptions = {}) => {
    return api.execute(() => apiClient.delete(url), {
      errorCtx: `DELETE ${url}`,
      ...requestOptions
    });
  }, [api, url]);

  return {
    ...api,
    delete: del
  };
};

/**
 * Hook for file upload requests
 * @param {string} url - Upload endpoint URL
 * @param {Object} options - Request options
 * @returns {Object} Upload utilities and state
 */
export const useUpload = (url, options = {}) => {
  const api = useApi(options);
  
  const upload = useCallback((formData, requestOptions = {}) => {
    return api.execute(() => apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }), {
      errorCtx: `UPLOAD ${url}`,
      ...requestOptions
    });
  }, [api, url]);

  return {
    ...api,
    upload
  };
};

export default useApi;
