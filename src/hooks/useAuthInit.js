/**
 * Authentication Initialization Hook
 * Handles authentication state initialization and token validation on app startup
 */

import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { initializeAuth } from '../services/authService';
import { clearAuth } from '../redux/Slice/authSlice';

/**
 * Hook to initialize authentication state on app startup
 * @returns {Object} Initialization state and utilities
 */
export const useAuthInit = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState(null);

  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsInitializing(true);
        setInitError(null);
        
        console.log('Initializing authentication...');
        
        const isValid = await initializeAuth(dispatch);
        
        if (!isValid) {
          console.log('Authentication initialization failed, user not authenticated');
        } else {
          console.log('Authentication initialized successfully');
        }
        
      } catch (error) {
        console.error('Error during auth initialization:', error);
        setInitError(error.message);
        
        // Clear auth state on initialization error
        dispatch(clearAuth());
      } finally {
        setIsInitializing(false);
      }
    };

    initAuth();
  }, [dispatch]);

  /**
   * Retry authentication initialization
   */
  const retryInit = async () => {
    try {
      setIsInitializing(true);
      setInitError(null);
      
      const isValid = await initializeAuth(dispatch);
      
      if (!isValid) {
        setInitError('Authentication validation failed');
      }
    } catch (error) {
      setInitError(error.message);
      dispatch(clearAuth());
    } finally {
      setIsInitializing(false);
    }
  };

  return {
    isInitializing,
    initError,
    isAuthenticated,
    retryInit
  };
};

export default useAuthInit;
