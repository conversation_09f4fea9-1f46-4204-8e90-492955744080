/**
 * Token Management Configuration
 * Centralized configuration for token management behavior
 */

export const TOKEN_CONFIG = {
  // Warning timings (in minutes) - Set to very long times to trigger immediately
  FIRST_WARNING_TIME: 1440,    // Show first warning 24 hours before expiration (will trigger for any token)
  URGENT_WARNING_TIME: 720,    // Show urgent warning 12 hours before expiration

  // Behavior settings
  AUTO_LOGOUT_ON_EXPIRY: false,  // Set to false to prevent automatic logout
  SHOW_SESSION_EXTENSION: true,  // Allow users to extend session
  MIN_WARNING_INTERVAL: 0.1,    // Minimum 6 seconds between warnings (for testing)
  
  // UI settings
  WARNING_PLACEMENT: 'topRight', // Notification placement
  URGENT_WARNING_PERSISTENT: true, // Keep urgent warnings open until dismissed
  
  // Development settings
  ENABLE_TOKEN_DEBUG: true,      // Enable token debugging features
  LOG_TOKEN_EVENTS: true,        // Log token-related events to console
};

/**
 * Get warning time in seconds
 * @param {string} warningType - 'first' or 'urgent'
 * @returns {number} Warning time in seconds
 */
export const getWarningTimeInSeconds = (warningType) => {
  const minutes = warningType === 'urgent' 
    ? TOKEN_CONFIG.URGENT_WARNING_TIME 
    : TOKEN_CONFIG.FIRST_WARNING_TIME;
  return minutes * 60;
};

/**
 * Check if automatic logout is enabled
 * @returns {boolean} True if auto logout is enabled
 */
export const isAutoLogoutEnabled = () => {
  return TOKEN_CONFIG.AUTO_LOGOUT_ON_EXPIRY;
};

/**
 * Check if session extension is enabled
 * @returns {boolean} True if session extension is enabled
 */
export const isSessionExtensionEnabled = () => {
  return TOKEN_CONFIG.SHOW_SESSION_EXTENSION;
};

/**
 * Get minimum warning interval in milliseconds
 * @returns {number} Interval in milliseconds
 */
export const getMinWarningInterval = () => {
  return TOKEN_CONFIG.MIN_WARNING_INTERVAL * 60 * 1000;
};

export default TOKEN_CONFIG;
