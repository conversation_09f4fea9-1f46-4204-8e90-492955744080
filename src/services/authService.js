/**
 * Authentication Service
 * Handles authentication operations including token refresh
 */

import axios from 'axios';
import { 
  getRefreshToken, 
  setTokens, 
  clearTokens, 
  isTokenExpired,
  hasValidRefreshToken 
} from './tokenService';

/**
 * Since there's no refresh token endpoint in the backend,
 * we'll implement a fallback strategy that redirects to login
 * when tokens expire
 */

/**
 * Check if refresh token functionality is available
 * @returns {boolean} False since no refresh endpoint exists
 */
export const isRefreshTokenSupported = () => {
  return false; // No refresh endpoint available
};

/**
 * Attempt to refresh access token (not supported in current backend)
 * This function exists for compatibility but will always fail
 * @returns {Promise<{accessToken: string, refreshToken: string}>} New tokens
 * @throws {Error} Always throws since refresh is not supported
 */
export const refreshAccessToken = async () => {
  console.warn('Token refresh not supported by backend - redirecting to login');
  throw new Error('Token refresh not supported. Please log in again.');
};

/**
 * Check if token refresh is needed and perform it
 * Since refresh is not supported, this will always return false
 * @returns {Promise<boolean>} Always false since refresh is not supported
 */
export const checkAndRefreshToken = async () => {
  console.log('Token refresh not supported - cannot refresh tokens');
  return false;
};

/**
 * Logout user by clearing all tokens
 * @param {Function} dispatch - Redux dispatch function (optional)
 */
export const logout = (dispatch = null) => {
  try {
    clearTokens();
    
    if (dispatch) {
      // Import clearAuth action dynamically to avoid circular dependencies
      import('../redux/Slice/authSlice').then(({ clearAuth }) => {
        dispatch(clearAuth());
      });
    }
    
    console.log('User logged out successfully');
  } catch (error) {
    console.error('Error during logout:', error);
  }
};

/**
 * Handle authentication errors (401/403)
 * Since refresh is not supported, this will always logout the user
 * @param {Function} dispatch - Redux dispatch function (optional)
 * @returns {Promise<boolean>} Always false since refresh is not supported
 */
export const handleAuthError = async (dispatch = null) => {
  console.log('Handling authentication error - logging out user (no refresh available)');
  logout(dispatch);
  return false;
};

/**
 * Validate current authentication state
 * Checks if access token exists and is not expired
 * @returns {Promise<boolean>} True if user is properly authenticated
 */
export const validateAuthState = async () => {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      console.log('No access token found');
      return false;
    }

    // Check if access token is expired
    if (isTokenExpired(accessToken)) {
      console.log('Access token is expired, clearing tokens');
      clearTokens();
      return false;
    }

    console.log('Authentication state is valid');
    return true;
  } catch (error) {
    console.error('Error validating auth state:', error);
    clearTokens();
    return false;
  }
};

/**
 * Initialize authentication state on app startup
 * Validates current tokens and clears them if invalid
 * @param {Function} dispatch - Redux dispatch function
 * @returns {Promise<boolean>} True if authentication is valid
 */
export const initializeAuth = async (dispatch) => {
  try {
    console.log('Initializing authentication state...');

    const isValid = await validateAuthState();

    if (!isValid) {
      console.log('Authentication state is invalid, clearing tokens');
      logout(dispatch);
      return false;
    }

    console.log('Authentication initialization successful');
    return true;

  } catch (error) {
    console.error('Error initializing auth:', error);
    logout(dispatch);
    return false;
  }
};

export default {
  refreshAccessToken,
  checkAndRefreshToken,
  logout,
  handleAuthError,
  validateAuthState,
  initializeAuth
};
