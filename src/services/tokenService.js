/**
 * Token Management Service
 * Centralized service for handling access tokens and refresh tokens
 * Provides secure storage, retrieval, and validation utilities
 */

import { getWarningTimeInSeconds } from '../config/tokenConfig';

// Token storage keys
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER: 'user',
  PROFILE_COMPLETE: 'profileComplete'
};

/**
 * Get access token from localStorage
 * @returns {string|null} Access token or null if not found
 */
export const getAccessToken = () => {
  try {
    return localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
  } catch (error) {
    console.error('Error getting access token:', error);
    return null;
  }
};

/**
 * Get refresh token from localStorage
 * @returns {string|null} Refresh token or null if not found
 */
export const getRefreshToken = () => {
  try {
    return localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
  } catch (error) {
    console.error('Error getting refresh token:', error);
    return null;
  }
};

/**
 * Get user data from localStorage
 * @returns {object|null} User object or null if not found
 */
export const getUser = () => {
  try {
    const userJson = localStorage.getItem(TOKEN_KEYS.USER);
    return userJson ? JSON.parse(userJson) : null;
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};

/**
 * Store tokens and user data in localStorage
 * @param {string} accessToken - Access token
 * @param {string} refreshToken - Refresh token
 * @param {object} user - User data object
 */
export const setTokens = (accessToken, refreshToken, user = null) => {
  try {
    if (accessToken) {
      localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
    }
    if (refreshToken) {
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
    }
    if (user) {
      localStorage.setItem(TOKEN_KEYS.USER, JSON.stringify(user));
    }
  } catch (error) {
    console.error('Error storing tokens:', error);
    throw new Error('Failed to store authentication tokens');
  }
};

/**
 * Clear all authentication data from localStorage
 */
export const clearTokens = () => {
  try {
    localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.USER);
    localStorage.removeItem(TOKEN_KEYS.PROFILE_COMPLETE);
  } catch (error) {
    console.error('Error clearing tokens:', error);
  }
};

/**
 * Check if user is authenticated (has valid access token)
 * @returns {boolean} True if authenticated
 */
export const isAuthenticated = () => {
  const accessToken = getAccessToken();
  return !!accessToken && !isTokenExpired(accessToken);
};

/**
 * Check if a JWT token is expired
 * @param {string} token - JWT token to check
 * @returns {boolean} True if token is expired
 */
export const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // Decode JWT token (without verification - just for expiration check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    // Check if token has exp claim and if it's expired
    return payload.exp && payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired if we can't parse it
  }
};

/**
 * Check if access token is about to expire (within 5 minutes)
 * @returns {boolean} True if token expires soon
 */
export const isTokenExpiringSoon = () => {
  const accessToken = getAccessToken();
  if (!accessToken) return false; // Changed from true to false

  try {
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const fiveMinutesFromNow = currentTime + (5 * 60); // 5 minutes in seconds

    return payload.exp && payload.exp < fiveMinutesFromNow;
  } catch (error) {
    console.error('Error checking token expiration time:', error);
    return false; // Changed from true to false
  }
};

/**
 * Check if access token is about to expire (configurable warning time)
 * @returns {boolean} True if token expires within the configured warning time
 */
export const shouldShowExpirationWarning = () => {
  const accessToken = getAccessToken();
  if (!accessToken) return false;

  try {
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const warningTime = getWarningTimeInSeconds('first');
    const warningThreshold = currentTime + warningTime;

    const result = payload.exp && payload.exp < warningThreshold;

    // Debug logging
    console.log('shouldShowExpirationWarning:', {
      currentTime,
      tokenExp: payload.exp,
      warningTime,
      warningThreshold,
      timeUntilExp: payload.exp - currentTime,
      result
    });

    return result;
  } catch (error) {
    console.error('Error checking token expiration time:', error);
    return false;
  }
};

/**
 * Check if access token is about to expire (configurable urgent warning time)
 * @returns {boolean} True if token expires within the configured urgent warning time
 */
export const shouldShowUrgentWarning = () => {
  const accessToken = getAccessToken();
  if (!accessToken) return false;

  try {
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const urgentWarningTime = getWarningTimeInSeconds('urgent');
    const urgentThreshold = currentTime + urgentWarningTime;

    const result = payload.exp && payload.exp < urgentThreshold;

    // Debug logging
    console.log('shouldShowUrgentWarning:', {
      currentTime,
      tokenExp: payload.exp,
      urgentWarningTime,
      urgentThreshold,
      timeUntilExp: payload.exp - currentTime,
      result
    });

    return result;
  } catch (error) {
    console.error('Error checking token expiration time:', error);
    return false;
  }
};

/**
 * Get token expiration time
 * @param {string} token - JWT token
 * @returns {Date|null} Expiration date or null if invalid
 */
export const getTokenExpiration = (token) => {
  if (!token) return null;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp ? new Date(payload.exp * 1000) : null;
  } catch (error) {
    console.error('Error getting token expiration:', error);
    return null;
  }
};

/**
 * Validate token format (basic JWT structure check)
 * @param {string} token - Token to validate
 * @returns {boolean} True if token has valid JWT format
 */
export const isValidTokenFormat = (token) => {
  if (!token || typeof token !== 'string') return false;
  
  const parts = token.split('.');
  return parts.length === 3;
};

/**
 * Get time until token expires in minutes
 * @param {string} token - JWT token
 * @returns {number} Minutes until expiration, -1 if expired or invalid
 */
export const getTimeUntilExpiration = (token) => {
  if (!token) return -1;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    if (!payload.exp) return -1;

    const currentTime = Math.floor(Date.now() / 1000);
    const timeLeft = payload.exp - currentTime;
    const minutesLeft = timeLeft > 0 ? Math.floor(timeLeft / 60) : -1;

    // Debug logging
    console.log('getTimeUntilExpiration:', {
      currentTime,
      tokenExp: payload.exp,
      timeLeftSeconds: timeLeft,
      minutesLeft
    });

    return minutesLeft;
  } catch (error) {
    console.error('Error calculating time until expiration:', error);
    return -1;
  }
};

/**
 * Check if refresh token exists and is valid format
 * @returns {boolean} True if refresh token is available
 */
export const hasValidRefreshToken = () => {
  const refreshToken = getRefreshToken();
  return !!refreshToken && isValidTokenFormat(refreshToken);
};

export default {
  getAccessToken,
  getRefreshToken,
  getUser,
  setTokens,
  clearTokens,
  isAuthenticated,
  isTokenExpired,
  isTokenExpiringSoon,
  shouldShowExpirationWarning,
  shouldShowUrgentWarning,
  getTokenExpiration,
  isValidTokenFormat,
  getTimeUntilExpiration,
  hasValidRefreshToken
};
