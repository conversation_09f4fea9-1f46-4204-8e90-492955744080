/**
 * API Service with Automatic Token Management
 * Centralized axios instance with request/response interceptors
 * Handles automatic token attachment and refresh
 */

import axios from 'axios';
import {
  getAccessToken,
  isTokenExpired,
  isTokenExpiringSoon
} from './tokenService';
import { handleAuthError, isRefreshTokenSupported } from './authService';
import { isAutoLogoutEnabled } from '../config/tokenConfig';

// Create axios instance for main API
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Track ongoing refresh requests to prevent multiple simultaneous refreshes
let isRefreshing = false;
let failedQueue = [];

/**
 * Process failed request queue after token refresh
 * @param {Error|null} error - Error if refresh failed
 * @param {string|null} token - New access token if refresh succeeded
 */
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

/**
 * Request interceptor - Automatically attach access token
 */
apiClient.interceptors.request.use(
  async (config) => {
    const accessToken = getAccessToken();

    if (accessToken && !isTokenExpired(accessToken)) {
      config.headers.Authorization = `Bearer ${accessToken}`;

      // Warn if token is expiring soon (since we can't refresh)
      if (isTokenExpiringSoon()) {
        console.warn('Access token is expiring soon. User will need to re-login when it expires.');
      }
    } else if (accessToken && isTokenExpired(accessToken)) {
      console.warn('Access token is expired. Request may fail with 401.');
    }

    // Log request for debugging (remove in production)
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
      headers: config.headers,
      data: config.data
    });

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

/**
 * Response interceptor - Handle 401/403 errors and retry with refreshed token
 */
apiClient.interceptors.response.use(
  (response) => {
    // Log successful response for debugging (remove in production)
    console.log(`API Response: ${response.status} ${response.config.url}`, {
      data: response.data
    });
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Log error for debugging
    console.error(`API Error: ${error.response?.status} ${originalRequest?.url}`, {
      error: error.response?.data,
      message: error.message
    });

    // Handle 401/403 errors (unauthorized/forbidden)
    if (error.response?.status === 401 || error.response?.status === 403) {

      // Avoid infinite loops
      if (originalRequest._retry) {
        console.error('Authentication already attempted, failing request');
        return Promise.reject(error);
      }

      originalRequest._retry = true;

      console.log('Authentication error detected');

      if (isAutoLogoutEnabled()) {
        console.log('Auto-logout enabled - handling logout since refresh is not supported');

        // Handle auth error (logout user)
        await handleAuthError();

        // Redirect to login page
        if (typeof window !== 'undefined') {
          console.log('Redirecting to login page...');
          window.location.href = '/signin';
        }

        return Promise.reject(new Error('Session expired. Please log in again.'));
      } else {
        console.log('Auto-logout disabled - returning auth error without redirect');

        // Don't logout automatically, just return the error
        // The UI will handle showing appropriate warnings
        return Promise.reject(new Error('Authentication required. Please log in to continue.'));
      }
    }

    // For other errors, just pass them through
    return Promise.reject(error);
  }
);

/**
 * Create axios instance for authentication endpoints (no interceptors)
 */
export const authApiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_AUTH_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Create axios instance for profile endpoints
 */
export const profileApiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_PROFILE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to profile API requests
profileApiClient.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken && !isTokenExpired(accessToken)) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * Create axios instance for products API
 */
export const productsApiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_PRODUCTS,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to products API requests
productsApiClient.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken && !isTokenExpired(accessToken)) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * Generic API request wrapper with error handling
 * @param {Function} requestFn - Axios request function
 * @param {string} errorContext - Context for error messages
 * @returns {Promise} API response
 */
export const makeApiRequest = async (requestFn, errorContext = 'API request') => {
  try {
    const response = await requestFn();
    return response;
  } catch (error) {
    console.error(`${errorContext} failed:`, error);
    
    // Provide user-friendly error messages
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.message || error.response.data?.error;
      
      switch (status) {
        case 400:
          throw new Error(message || 'Invalid request. Please check your input.');
        case 401:
          throw new Error('Authentication required. Please log in.');
        case 403:
          throw new Error('Access denied. You don\'t have permission for this action.');
        case 404:
          throw new Error('Requested resource not found.');
        case 500:
          throw new Error('Server error. Please try again later.');
        default:
          throw new Error(message || `Request failed with status ${status}`);
      }
    } else if (error.request) {
      throw new Error('Network error. Please check your internet connection.');
    } else {
      throw new Error(error.message || 'An unexpected error occurred.');
    }
  }
};

// Export the main API client as default
export default apiClient;
