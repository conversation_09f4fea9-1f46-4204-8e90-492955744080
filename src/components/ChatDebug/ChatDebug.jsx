import React, { useState } from 'react';
import { Card, Button, Typography, Space, Alert, Spin, Divider } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined, RobotOutlined } from '@ant-design/icons';
import { sendMessageToAI, checkChatServiceHealth, checkEnhancedChatHealth, getPlatformInfo, getChatServiceConfig } from '../../services/chatService';

const { Title, Text, Paragraph } = Typography;

const ChatDebug = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [healthStatus, setHealthStatus] = useState(null);
  const [enhancedHealthStatus, setEnhancedHealthStatus] = useState(null);
  const [testResponse, setTestResponse] = useState(null);
  const [platformInfo, setPlatformInfo] = useState(null);
  const [error, setError] = useState(null);

  const config = getChatServiceConfig();

  const runHealthCheck = async () => {
    setIsChecking(true);
    setError(null);
    setTestResponse(null);
    setPlatformInfo(null);
    
    try {
      console.log('🔍 Running health checks...');
      
      // Check both original and enhanced endpoints
      const [isHealthy, isEnhancedHealthy] = await Promise.all([
        checkChatServiceHealth(),
        checkEnhancedChatHealth()
      ]);
      
      setHealthStatus(isHealthy);
      setEnhancedHealthStatus(isEnhancedHealthy);
      
      if (isHealthy || isEnhancedHealthy) {
        // Test with a B2B marketplace specific message
        const response = await sendMessageToAI('I need help finding cloud infrastructure services for my SaaS startup');
        setTestResponse(response);
        
        // Get platform information if enhanced endpoint works
        if (isEnhancedHealthy) {
          try {
            const info = await getPlatformInfo();
            setPlatformInfo(info);
          } catch (err) {
            console.warn('Could not get platform info:', err.message);
          }
        }
      }
    } catch (err) {
      console.error('Health check failed:', err);
      setError(err.message);
      setHealthStatus(false);
      setEnhancedHealthStatus(false);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Card title={
      <Space>
        <RobotOutlined />
        <span>Bond AI Integration Debug Panel</span>
      </Space>
    } style={{ margin: '20px', maxWidth: '800px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* Configuration Display */}
        <div>
          <Title level={4}>
            <InfoCircleOutlined /> Configuration
          </Title>
          <Paragraph>
            <Text strong>Base URL:</Text> <Text code>{config.baseURL}</Text><br />
            <Text strong>Original Endpoint:</Text> <Text code>{config.endpoint}</Text><br />
            <Text strong>Enhanced Endpoint:</Text> <Text code>{config.enhancedEndpoint}</Text><br />
            <Text strong>Full URLs:</Text><br />
            <Text code style={{ fontSize: '12px' }}>{config.fullURL}</Text><br />
            <Text code style={{ fontSize: '12px' }}>{config.enhancedFullURL}</Text>
          </Paragraph>
        </div>

        {/* Health Check Button */}
        <div>
          <Title level={4}>Health Check</Title>
          <Button 
            type="primary" 
            onClick={runHealthCheck} 
            loading={isChecking}
            icon={isChecking ? <Spin /> : <CheckCircleOutlined />}
            size="large"
          >
            {isChecking ? 'Testing Bond AI Integration...' : 'Test Bond AI Chat System'}
          </Button>
        </div>

        {/* Health Status Results */}
        {(healthStatus !== null || enhancedHealthStatus !== null) && (
          <div>
            <Title level={4}>Connection Status</Title>
            
            {/* Original Endpoint Status */}
            <Alert
              message="Original Chat Endpoint"
              description={
                healthStatus 
                  ? 'Basic chat functionality is working correctly.'
                  : 'Original endpoint is not responding.'
              }
              type={healthStatus ? 'success' : 'warning'}
              icon={healthStatus ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
              showIcon
              style={{ marginBottom: '10px' }}
            />
            
            {/* Enhanced Endpoint Status */}
            <Alert
              message="Enhanced Chat Endpoint (with Training Data)"
              description={
                enhancedHealthStatus 
                  ? 'Enhanced Bond AI with B2B marketplace knowledge is active!'
                  : 'Enhanced endpoint not available - using basic functionality.'
              }
              type={enhancedHealthStatus ? 'success' : 'info'}
              icon={enhancedHealthStatus ? <CheckCircleOutlined /> : <InfoCircleOutlined />}
              showIcon
            />
          </div>
        )}

        {/* Platform Information */}
        {platformInfo && (
          <div>
            <Title level={4}>Platform Information</Title>
            <Alert
              message="B2B Marketplace Data Loaded"
              description={
                <div>
                  <Text strong>Platform Stats:</Text>
                  <pre style={{ 
                    marginTop: '8px', 
                    padding: '12px', 
                    background: '#f5f5f5', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {platformInfo.platformStats}
                  </pre>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        )}

        {/* Test Response */}
        {testResponse && (
          <div>
            <Title level={4}>AI Response Test</Title>
            <Alert
              message="Bond AI Response"
              description={
                <div>
                  <Text strong>Test Query:</Text> "I need help finding cloud infrastructure services for my SaaS startup"
                  <Paragraph style={{ 
                    marginTop: '8px', 
                    padding: '12px', 
                    background: '#f0f9ff', 
                    borderRadius: '4px',
                    border: '1px solid #e0f2fe'
                  }}>
                    <Text strong>Bond AI:</Text> {testResponse}
                  </Paragraph>
                </div>
              }
              type="success"
              showIcon
            />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert
            message="Error Details"
            description={error}
            type="error"
            showIcon
          />
        )}

        <Divider />

        {/* Troubleshooting Tips */}
        <div>
          <Title level={4}>Integration Status & Troubleshooting</Title>
          <Paragraph>
            <Text strong>What's Been Implemented:</Text>
            <ul>
              <li>✅ Training data integration with B2B marketplace knowledge</li>
              <li>✅ RAG (Retrieval-Augmented Generation) system</li>
              <li>✅ Enhanced system prompts for business context</li>
              <li>✅ Fallback to original endpoint if enhanced fails</li>
              <li>✅ Domain-specific responses for IT services marketplace</li>
            </ul>
          </Paragraph>
          
          <Paragraph>
            <Text strong>If connections fail:</Text>
            <ul>
              <li>Make sure the Spring Boot backend is running on port 8084</li>
              <li>Check that training data files are in: <Text code>src/main/resources/training-data/</Text></li>
              <li>Verify Azure OpenAI credentials in application.properties</li>
              <li>Check browser console for detailed error messages</li>
              <li>Ensure CORS settings allow requests from your frontend</li>
            </ul>
          </Paragraph>
        </div>

      </Space>
    </Card>
  );
};

export default ChatDebug;
