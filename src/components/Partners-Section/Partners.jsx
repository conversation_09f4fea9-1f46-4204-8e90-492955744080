import React from "react";
import { useTranslation } from "react-i18next";
import partners from "../Data/Partners.js";
import { useState } from "react";
import './Partners.css'

const Partners = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "english", label: "English" },
        { code: "arabic", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        console.log('selected language:', language);
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <div className="partner-container">
            <div className="partner-header">
                <h1 className="text" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>{t("Partners")}</h1>
                <p className="subtext" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >
                    {t("Building Strong Alliances for Exceptional Results")}
                </p>
            </div>
            <div className="grid4">
                {partners.map((partner, index) => (
                    <div key={index} className="card4" style={{ backgroundColor: partner.bgColor }}>
                        <img src={partner.logo} alt={partner.name} className="logo4" />
                    </div>
                ))};
            </div>
        </div>

    );
};

export default Partners;
