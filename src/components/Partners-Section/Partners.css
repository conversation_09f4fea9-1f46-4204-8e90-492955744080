/* Partners CSS */
.partner-container {
    text-align: center;
    /* margin: 0px 40px; */
    margin-bottom: 20px;
}


.text {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    text-align: left;
}

.subtext {
    font-size: 16px;
    color: #7d6e6e;
    margin-bottom: 30px;
    text-align: left;
    /* margin-left: 40px; */
}

[dir='rtl'] .text,
.subtext {
    margin-right: 40px;
}

[dir='ltr'] .text,
.subtext {
    margin-left: 40px;
}

[dir='ltr'] .grid4 {
    margin-left: 50px;
}

[dir='rtl'] .grid4 {
    margin-right: 50px;
}

.grid4 {
    display: flex;
    justify-content: center;
    gap: 35px;
    flex-wrap: wrap;
}

.card4 {
    width: 200px;
    height: 110px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card4:hover {
    transform: translateY(-5px);
}

.logo4 {
    width: 200px;
    height: 110px;
    border-radius: 12px;
}