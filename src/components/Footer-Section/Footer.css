/* Footer CSS */

.footer {
    background: #ffffff;
    padding: 60px 0 20px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: 40px;
    padding: 0 20px;
}

.footer-left {
    max-width: 280px;
}

.footer-logo {
    margin-bottom: 20px;
    display: flex;
    justify-content: center; /* Centers the logo horizontally */
    align-items: center; /* Centers the logo vertically */
}

.footer-logo img {
    width: 100px;
    height: auto;
}

/* Left section text alignment */
.footer-text {
    font-size: 14px;
    color: #666;
    margin: 16px 0;
    line-height: 1.5;
    max-width: 250px; /* Control the width of lorem ipsum text */
}

.social-icons {
    display: flex;
    gap: 16px;
    font-size: 20px;
}

.footer-links {
    display: flex;
    gap: 60px;
}

.footer-column h4 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column li {
    color: #666;
    font-size: 16px;
    margin-bottom: 12px;
    cursor: pointer;
}

.footer-column li:hover {
    color: #333;
}

.footer-right {
    max-width: 280px;
}

.footer-right h4 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

/* Contact section styles */
.footer-right ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-right li {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    color: #666;
    font-size: 14px;
}

.contact-icon {
    flex-shrink: 0;
    width: 20px; /* Fixed width for icons */
    display: inline-flex;
    align-items: center;
}

/* Address specific styling */
.footer-right li:last-child {
    align-items: flex-start; /* Align to top for multi-line address */
}

.footer-right li:last-child span {
    margin-top: 3px; /* Adjust icon position for address */
}

.footer-bottom {
    max-width: 1200px;
    margin: 40px auto 0;
    padding: 20px 20px 0;
    border-top: 1px solid #eee;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.footer-bottom a {
    color: #666;
    text-decoration: none;
    margin: 0 8px;
}

.footer-bottom a:hover {
    color: #333;
}

@media (max-width: 1024px) {
    .footer-container {
        flex-wrap: wrap;
    }
    
    .footer-left,
    .footer-column,
    .footer-right {
        flex: 1 1 250px;
    }
}

@media (max-width: 768px) {
    .footer-container {
        flex-direction: column;
        gap: 30px;
    }
    
    .footer-left,
    .footer-column,
    .footer-right {
        max-width: 100%;
    }
}

/* Add media query for responsive layout */
@media screen and (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 32px;
  }

  .footer-left {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-column {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-right {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-right ul {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .footer-right li {
    justify-content: center;
    text-align: center;
  }
}

/* RTL Support in mobile view */
@media screen and (max-width: 768px) {
  [dir="rtl"] .footer-container,
  [dir="rtl"] .footer-left,
  [dir="rtl"] .footer-column,
  [dir="rtl"] .footer-right {
    align-items: center;
  }
}