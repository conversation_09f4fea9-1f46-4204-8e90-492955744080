import React from "react";
import logo from '../../assets/arabic-logo.svg';
import { useTranslation } from "react-i18next";
import './Footer.css';

const Footer = () => {
    const { t } = useTranslation();
    return (
        <footer className="footer">
            <div className="footer-container">
                {/* Left Section */}
                <div className="footer-left">
                    <div className="footer-logo">
                        <img src={logo} alt="Bond" />
                    </div>
                    <p className="footer-text">
                        {t("Lorem ipsum dolor sit amet consectetur adipiscing elit aliquam")}
                    </p>
                    <div className="social-icons">
                        <span>📘</span>
                        <span>🐦</span>
                        <span>📸</span>
                        <span>🔗</span>
                        <span>▶️</span>
                    </div>
                </div>

                {/* Product Section */}
                <div className="footer-column">
                    <h4>{t("Product")}</h4>
                    <ul>
                        <li>{t("Features")}</li>
                        <li>{t("Pricing")}</li>
                        <li>{t("Case studies")}</li>
                        <li>{t("Reviews")}</li>
                        <li>{t("Updates")}</li>
                    </ul>
                </div>

                {/* Company Section */}
                <div className="footer-column">
                    <h4>{t("Company")}</h4>
                    <ul>
                        <li>{t("About")}</li>
                        <li>{t("Contact us")}</li>
                        <li>{t("Careers")}</li>
                        <li>{t("Culture")}</li>
                        <li>{t("Blog")}</li>
                    </ul>
                </div>

                {/* Support Section */}
                <div className="footer-column">
                    <h4>{t("Support")}</h4>
                    <ul>
                        <li>{t("Getting started")}</li>
                        <li>{t("Help center")}</li>
                        <li>{t("Server status")}</li>
                        <li>{t("Report a bug")}</li>
                        <li>{t("Chat support")}</li>
                    </ul>
                </div>

                {/* Contact Section */}
                <div className="footer-right">
                    <h4>{t("Contacts us")}</h4>
                    <ul>
                        <li>
                            <span className="contact-icon">✉️</span>
                            <EMAIL>
                        </li>
                        <li>
                            <span className="contact-icon">📞</span>
                            (414) 687 - 5892
                        </li>
                        <li>
                            <span className="contact-icon">📍</span>
                            <div className="address-text">
                            <span>794 Mcallister St,</span>
                            <span>San Francisco, 94102</span>
            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div className="footer-bottom">
                <p>
                    {t("All Rights Reserved")} | 
                    <a href="/">{t("Terms and Conditions")}</a> | 
                    <a href="/">{t("Privacy Policy")}</a>
                </p>
            </div>
        </footer>
    );
};

export default Footer;
