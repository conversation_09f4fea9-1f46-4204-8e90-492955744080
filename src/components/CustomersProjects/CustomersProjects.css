/* CustomersProjects CSS */

.section-container {
    display: flex;
    justify-content: space-between;
    padding: 40px;
    font-family: 'Segoe UI', sans-serif;
    gap: 40px;
    background-color: #f4f7f2;
    flex-wrap: wrap;
}

.customers,
.projects {
    flex: 1;
    min-width: 300px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.btn-purple {
    color: #9933a2;
    background-color: white;
    padding: 6px 16px;
    border: 2px solid #9933a2;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-purple:hover {
    background-color: #9933a2;
    color: white;
}

.desc {
    font-size: 14px;
    color: #666;
    margin: 10px 0 20px;
    line-height: 1.4;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    min-width: 600px;
}

.customers-table th,
.customers-table td,
.projects-table th,
.projects-table td {
    padding: 12px 16px;
    text-align: left;
}

.customers-table thead {
    background-color: #b942b0;
    color: white;
}

.customers-table tbody tr:nth-child(even) {
    background-color: #9933a2;
    color: white;
}

.customers-table tbody tr:nth-child(odd) {
    background-color: #b942b0;
    color: white;
}

.customers-table tbody tr:hover {
    color: #9933a2;
    background-color: white;
    cursor: pointer;
}

.projects-table thead {
    background-color: #eae7ec;
}

.projects-table tbody tr:nth-child(odd) {
    background-color: #f8f8f8;
}

.projects-table tbody tr:nth-child(even) {
    background-color: #ffffff;
}

.projects-table tbody tr:hover {
    background-color: #9933a2;
    color: white;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 992px) {
    .section-container {
        flex-direction: column;
        padding: 20px;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .btn-purple {
        margin-top: 10px;
    }

    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        min-width: unset;
    }

    .desc {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .btn-purple {
        font-size: 13px;
        padding: 6px 12px;
    }

    .desc {
        font-size: 12px;
    }

    th,
    td {
        font-size: 13px;
    }
}