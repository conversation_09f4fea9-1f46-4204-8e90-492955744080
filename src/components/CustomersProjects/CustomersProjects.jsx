import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import customers from '../Data/CustomersTable';
import projects from '../Data/Projects';
import './CustomersProjects.css';

const CustomerProjects = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "en", label: "English" },
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <div className="section-container">
            <div className="customers">
                <div className="header">
                    <h2>{t("Top Customers")}</h2>
                    <button className="btn-purple">{t("Explore Top Customers")}</button>
                </div>
                <p className="desc">
                    {t("Our top customers—valued partners in our journey of growth, success, and innovation—whose trust and loyalty.")}
                </p>
                <table className="customers-table">
                    <thead>
                        <tr>
                            <th>{t("Customer Name")}</th>
                            <th>{t("Industry")}</th>
                            <th>{t("Projects Posted")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {customers.map((cust) => (
                            <tr key={cust.id}>
                                <td>{t(cust.nameKey)}</td>
                                <td>{t(cust.industryKey)}</td>
                                <td>{cust.projects}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div className="projects">
                <div className="header">
                    <h2>{t("Recent Projects")}</h2>
                    <button className="btn-purple">{t("Explore Projects")}</button>
                </div>
                <p className="desc">
                    {t("Our top customers—valued partners in our journey of growth, success, and innovation—whose trust and loyalty.")}
                </p>
                <table className="projects-table">
                    <thead>
                        <tr>
                            <th>{t("Project Title")}</th>
                            <th>{t("Budget Range")}</th>
                            <th>{t("Category")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {projects.map((proj) => (
                            <tr key={proj.id}>
                                <td>{t(proj.titleKey)}</td>
                                <td>{t(proj.budgetKey)}</td>
                                <td>{t(proj.categoryKey)}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default CustomerProjects;