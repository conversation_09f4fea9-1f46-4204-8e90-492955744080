import React from 'react';
import { useTranslation } from 'react-i18next';
import vendors from '../Data/VendorsTable';
import './VendorsTable.css';

const VendorsTable = () => {
    const { t } = useTranslation();

    const renderStars = (rating) => {
        return Array.from({ length: 5 }, (_, i) => (
            <span
                key={i}
                style={{
                    color: i < rating ? '#FFD700' : '#ddd',
                    fontSize: '16px',
                    filter: i < rating ? 'drop-shadow(0 0 2px rgba(255, 215, 0, 0.5))' : 'none'
                }}
            >
                ★
            </span>
        ));
    };

    return (
        <div className="table-wrapper">
            <div className="header">
                <div>
                    <h1 className="title">{t("Top Vendors in the Market")}</h1>
                    <p className="subtitle">
                        {t("Ratings and reviews ensure transparency, helping you choose best service providers with confidence.")}
                    </p>
                </div>
                <button className="explore-btn">{t("Explore all Deals")}</button>
            </div>
            <table className="vendors-table">
                <thead>
                    <tr>
                        <th>{t("Vendor Name")}</th>
                        <th>{t("Business Type")}</th>
                        <th>{t("Ratings")}</th>
                        <th>{t("Completed Projects")}</th>
                    </tr>
                </thead>
                <tbody>
                    {vendors.map((vendor) => (
                        <tr key={vendor.id}>
                            <td>{t(vendor.nameKey)}</td>
                            <td>{t(vendor.typeKey)}</td>
                            <td>
                                <div className="rating-container">
                                    {renderStars(vendor.rating)}
                                    <span className="rating-text">({vendor.rating}.0)</span>
                                </div>
                            </td>
                            <td>{vendor.projects}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default VendorsTable;