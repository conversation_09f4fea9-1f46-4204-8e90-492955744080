/* Vendors Table CSS */
.table-wrapper {
    background-color: #f2f7f1;
    padding: 20px 40px;
    font-family: 'Segoe UI', sans-serif;
    overflow-x: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
}

.subtitle {
    font-size: 14px;
    color: #555;
    max-width: 600px;
}

.explore-btn {
    background-color: #9ca4ae;
    color: black;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    margin-top: 10px;
}

.explore-btn:hover {
    background-color: #56054e;
    color: white;
}

/* Table Styles */
.vendors-table {
    width: 100%;
    min-width: 600px;
    border-collapse: collapse;
    overflow-x: auto;
}

.vendors-table thead {
    background-color: #e8e1ea;
}

.vendors-table th,
.vendors-table td {
    padding: 16px 20px;
    text-align: left;
    white-space: nowrap;
}

.vendors-table tbody tr:nth-child(even) {
    background-color: #f0eaf4;
}

.vendors-table tbody tr:nth-child(odd) {
    background-color: #ffffff;
}

.vendors-table tbody tr:hover {
    background-color: #9933a2;
    color: white;
    cursor: pointer;
}

.rating-container {
    display: flex;
    align-items: center;
    gap: 4px;
}

.rating-text {
    margin-left: 5px;
    font-size: 14px;
    color: #555;
}

/* Responsive Styling */
@media (max-width: 768px) {
    .table-wrapper {
        padding: 10px 20px;
    }

    .title {
        font-size: 20px;
    }

    .subtitle {
        font-size: 13px;
    }

    .explore-btn {
        font-size: 13px;
        padding: 8px 16px;
    }

    .vendors-table {
        min-width: unset;
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .vendors-table th,
    .vendors-table td {
        padding: 12px 16px;
    }
}