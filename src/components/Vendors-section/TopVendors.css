.vendor-container {
    background-color: #ffffff;
    padding: 40px 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.vendor-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.vendor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.vendor-card {
    background: #ffffff;
    padding: 25px;
    border-radius: 16px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    min-height: 200px;
}

.vendor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.vendor-logo img {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.vendor-info {
    flex: 1;
    text-align: left;
    min-width: 0;
}

.vendor-name {
    margin: 0 0 8px 0;
    font-size: clamp(16px, 3vw, 20px);
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.vendor-category {
    font-size: 14px;
    color: #888;
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.vendor-projects {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    margin: 10px 0;
    flex-wrap: wrap;
    gap: 10px;
}

.projects-text {
    color: #666;
}

.vendor-rating {
    color: #27ae60;
    font-size: 18px;
    line-height: 1;
}

.vendor-description {
    font-size: 13px;
    color: #666;
    margin: 12px 0 15px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.vendor-profile-btn {
    padding: 10px 18px;
    background-color: #9ca4ae;
    color: #333;
    border: none;
    cursor: pointer;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
}

.vendor-profile-btn:hover {
    background-color: #56054e;
    color: white;
    transform: translateY(-1px);
}

/* ========== RESPONSIVE STYLES ========== */

/* Large tablets and small desktops */
@media (max-width: 1200px) {
    .deal-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }
    
    .vendor-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

/* Tablets */
@media (max-width: 1024px) {
    .deal-container,
    .vendor-container {
        padding: 30px 15px;
    }
    
    .d-header,
    .vendor-header {
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .deal-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 20px;
    }
    
    .vendor-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }
    
    .deal-card {
        min-height: 260px;
    }
    
    .vendor-card {
        padding: 20px;
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    .d-header,
    .vendor-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 20px;
    }
    
    .header-content {
        min-width: unset;
    }
    
    .explore-btn {
        align-self: center;
        width: fit-content;
        margin: 0 auto;
    }
    
    .deal-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .vendor-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .deal-body {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .deal-category {
        text-align: left;
        align-self: flex-end;
    }
    
    .vendor-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 25px;
    }
    
    .vendor-info {
        text-align: center;
    }
    
    .vendor-projects {
        justify-content: center;
        flex-direction: column;
        gap: 8px;
    }
    
    .vendor-profile-btn {
        width: 100%;
        margin-top: 10px;
    }
}

/* Mobile portrait */
@media (max-width: 480px) {
    .deal-container,
    .vendor-container {
        padding: 20px 10px;
    }
    
    .title {
        font-size: 22px;
    }
    
    .subtitle {
        font-size: 14px;
    }
    
    .deal-card {
        min-height: 240px;
        margin: 0 5px;
    }
    
    .deal-header,
    .deal-body,
    .deal-footer {
        padding: 15px;
    }
    
    .deal-discount {
        font-size: 28px;
    }
    
    .deal-footer {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .view-deal {
        width: 100%;
        padding: 12px;
    }
    
    .vendor-card {
        padding: 20px 15px;
    }
    
    .vendor-logo img {
        width: 50px;
        height: 50px;
    }
    
    .vendor-description {
        font-size: 12px;
        -webkit-line-clamp: 2;
    }
    
    .explore-btn {
        width: 100%;
        padding: 14px 20px;
        font-size: 15px;
    }
}

/* Extra small screens */
@media (max-width: 360px) {
    .deal-grid,
    .vendor-grid {
        gap: 15px;
    }
    
    .deal-card,
    .vendor-card {
        margin: 0;
    }
    
    .title {
        font-size: 20px;
    }
    
    .subtitle {
        font-size: 13px;
    }
}