import React from "react";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import vendorsData from "../Data/Vendors.js";
import './TopVendors.css';

const TopVendors = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "en", label: "English" },
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <div className="vendor-container">
            <div className="vendor-header">
                <div>
                    <h1 className="title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>{t("Top Vendors in the Market")}</h1>
                    <p className="subtitle" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
                        {t("Ratings and reviews ensure transparency, helping you choose best service providers with confidence.")}
                    </p>
                </div>
                <button className="explore-btn">{t("Explore all Deals")}</button>
            </div>
            <div className="vendor-grid">
                {vendorsData.map((vendor) => (
                    <div key={vendor.id} className="vendor-card">
                        <div className="vendor-logo">
                            <img src={vendor.logo} alt={t(vendor.nameKey)} />
                        </div>
                        <div className="vendor-info">
                            <h3 className="vendor-name">{t(vendor.nameKey)}</h3>
                            <p className="vendor-category">{t(vendor.categoryKey)}</p>
                            <div className="vendor-projects">
                                {t("Completed Projects")}: <strong>{vendor.projects}</strong>
                                <span className="vendor-rating">
                                    {"★".repeat(vendor.rating)}
                                    {"☆".repeat(5 - vendor.rating)}
                                </span>
                            </div>
                            <p className="vendor-description">{t(vendor.descriptionKey)}</p>
                            <button className="vendor-profile-btn">{t("View Profile")}</button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TopVendors;