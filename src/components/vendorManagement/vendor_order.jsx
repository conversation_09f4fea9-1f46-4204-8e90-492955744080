import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import Header from '../Header-Section/Header';
import './VenderOrder.css';
import CustomSider from '../../components/Pages/CustomSider';

const VendorOrder = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);

  // Get order data passed from the orders table
  const orderData = location.state?.orderData || {
    id: 'ORD00125',
    customerName: 'Omar',
    customerEmail: '<EMAIL>',
    productName: 'Enterprise Cloud Hosting',
    category: 'Cloud Services',
    status: 'pending',
    budget: '$2,500',
    requestDate: '2025-04-28',
    expiryDate: '2025-05-05',
    description: 'Need enterprise-level cloud hosting solution'
  };
  
  const [selectedPrice, setSelectedPrice] = useState('actual');
  const [messageText, setMessageText] = useState('');
  const [additionalMessage, setAdditionalMessage] = useState('');
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'customer',
      text: 'Need confirmation on the delivery timeline',
      time: '04:22 PM'
    },
    {
      id: 2,
      type: 'vendor',
      text: 'Can you upgrade to the premium plan?',
      time: '04:23 PM'
    },
    {
      id: 3,
      type: 'customer',
      text: 'Order is ready for delivery. Waiting for final payment.',
      time: '04:24 PM'
    }
  ]);

  const handleSendMessage = () => {
    if (messageText.trim()) {
      const newMessage = {
        id: messages.length + 1,
        type: 'vendor',
        text: messageText,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setMessages([...messages, newMessage]);
      setMessageText('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleApproveOrder = () => setShowApproveModal(true);
  const handleRejectOrder = () => setShowRejectModal(true);
  const handleApproveConfirm = () => setShowApproveModal(false);
  const handleRejectConfirm = () => setShowRejectModal(false);
  const handleModalCancel = () => {
    setShowApproveModal(false);
    setShowRejectModal(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const Modal = ({ isOpen, onClose, title, message, onConfirm, confirmText, isReject }) => {
    if (!isOpen) return null;
    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <h3 className="modal-title">{title}</h3>
          <p className="modal-message">{message}</p>
          <div className="modal-buttons">
            <button className="modal-cancel-button" onClick={onClose}>
              {t('Cancel')}
            </button>
            <button 
              className={`modal-confirm-button ${isReject ? 'modal-confirm-reject' : 'modal-confirm-approve'}`}
              onClick={onConfirm}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Header />
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      
      <div className={`vendor-order-container ${collapsed ? 'sidebar-collapsed' : ''}`}>
        {/* Top Bar */}
        <div className="top-bar">
          <div className="top-bar-left">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/orders')}
              className="back-button"
            >
              {t('Back to Orders')}
            </Button>
            <div className="order-header-info">
              <h2 className="order-title">{t('Order')} #{orderData.id}</h2>
              <span className="order-customer">{t('Customer')}: {orderData.customerName}</span>
            </div>
          </div>
          <div className="button-group">
            <button className="reject-button" onClick={handleRejectOrder}>
              {t('Reject Order')}
            </button>
            <button className="approve-button" onClick={handleApproveOrder}>
              {t('Approve Order')}
            </button>
          </div>
        </div>

        {/* Main Row */}
        <div className="main-row">
          {/* Price Details */}
          <div className="price-details-section">
            <div className="section-header">
              <div className="section-title">{t('Price Details')}</div>
              <div className="order-date">{t('Order Date')}: 4:41 PM 29 Apr 2025</div>
            </div>

            <div className="radio-group">
              <label className="radio-label">
                <input
                  type="radio"
                  name="price"
                  value="actual"
                  checked={selectedPrice === 'actual'}
                  onChange={(e) => setSelectedPrice(e.target.value)}
                />
                {t('Use Your Actual Price')}
              </label>
              <label className="radio-label">
                <input
                  type="radio"
                  name="price"
                  value="offer"
                  checked={selectedPrice === 'offer'}
                  onChange={(e) => setSelectedPrice(e.target.value)}
                />
                {t('Accept the Offer Price')}
              </label>
            </div>

            <div className="price-inputs">
              <div className="price-input-group">
                <div className="price-label">{t('Actual Price')}</div>
                <input type="text" value="$4.8M" readOnly className="price-input" />
              </div>
              <div className="price-input-group">
                <div className="price-label">{t('Offer Price')}</div>
                <input type="text" value="$3.0M" readOnly className="price-input" />
              </div>
            </div>

            <textarea
              placeholder={t('Add Message')}
              value={additionalMessage}
              onChange={(e) => setAdditionalMessage(e.target.value)}
              className="message-textarea"
            />
          </div>

          {/* Message Center */}
          <div className="message-center-section">
            <div className="section-title">{t('Message Center')}</div>

            <div className="messages-container">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`message-bubble ${message.type === 'customer' ? 'message-customer' : 'message-vendor'}`}
                >
                  <div>{message.text}</div>
                  <div className="message-time">{message.time}</div>
                </div>
              ))}
            </div>

            <div className="message-input-container">
              <input
                placeholder={t('Type Message')}
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                onKeyPress={handleKeyPress}
                className="message-input"
              />
              <button className="send-button" onClick={handleSendMessage}>&gt;</button>
            </div>
          </div>
        </div>

        {/* Order Details */}
        <div className="details-row">
          <div className="details-section">
            <div className="section-title">{t('Order Details')}</div>
            {[
              { label: t('User Name:'), value: orderData.customerName },
              { label: t('Product Name:'), value: orderData.productName },
              { label: t('Order ID:'), value: orderData.id },
              { label: t('Order Status:'), value: orderData.status?.replace('_', ' ').toUpperCase(), isPending: orderData.status === 'pending' },
              { label: t('Order Type:'), value: orderData.orderType || 'New' },
              { label: t('Budget:'), value: orderData.budget },
              { label: t('Category:'), value: orderData.category },
              { label: t('Request Date:'), value: formatDate(orderData.requestDate) },
              { label: t('Expiry Date:'), value: formatDate(orderData.expiryDate), isExpiry: true },
              { label: t('Due Date:'), value: formatDate(orderData.dueDate) }
            ].map((item, index) => (
              <div key={index} className="detail-item">
                <span className="detail-label">{item.label}</span>
                <span className={
                  item.isPending ? 'detail-value-pending' :
                  item.isExpiry ? 'detail-value-expiry' :
                  'detail-value'
                }>
                  {item.value}
                </span>
              </div>
            ))}
          </div>

          {/* Order Timeline */}
          <div className="details-section">
            <div className="timeline-header">
              {t('Order Timeline')}
              <span className="status-badge">{t('In Progress')}</span>
            </div>
            {[
              { label: t('Order Placed Date:'), value: '29 April 2025' },
              { label: t('Quotation Submitted:'), value: '29 April 2025' },
              { label: t('Awarded Date:'), value: '29 April 2025' },
              { label: t('Expected Delivery:'), value: '5 May 2025' },
              { label: t('Order Completion Date:'), value: 'TBO' }
            ].map((item, index) => (
              <div key={index} className="timeline-item">
                <span className="timeline-label">{item.label}</span>
                <span>{item.value}</span>
              </div>
            ))}
          </div>

          {/* Customer Details */}
          <div className="details-section">
            <div className="section-title">{t('Customer Details')}</div>
            {[
              { label: t('Customer Name:'), value: orderData.customerName },
              { label: t('Customer Email:'), value: orderData.customerEmail },
              { label: t('Customer Phone:'), value: orderData.customerPhone || '****** 888 9888' },
              { label: t('Customer Address:'), value: orderData.customerAddress || '1 Address' },
              { label: t('Company Name:'), value: orderData.companyName || 'Techno Tip Technologies' },
              { label: t('Website:'), value: orderData.website || 'www.ttt.com' },
              { label: t('Description:'), value: orderData.description }
            ].map((item, index) => (
              <div key={index} className="detail-item">
                <span className="detail-label">{item.label}</span>
                <span className="detail-value">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Modals */}
        <Modal
          isOpen={showApproveModal}
          onClose={handleModalCancel}
          title={t('Approve Order')}
          message={t('Are you sure you want to approve this order? This action will confirm the order and proceed with the next steps.')}
          onConfirm={handleApproveConfirm}
          confirmText={t('Approve')}
          isReject={false}
        />

        <Modal
          isOpen={showRejectModal}
          onClose={handleModalCancel}
          title={t('Reject Order')}
          message={t('Are you sure you want to reject this order? This action cannot be undone and the customer will be notified.')}
          onConfirm={handleRejectConfirm}
          confirmText={t('Reject')}
          isReject={true}
        />
      </div>
    </>
  );
};

export default VendorOrder;