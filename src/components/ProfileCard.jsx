import React from 'react'
import { Card, Descriptions, Typography, Avatar, Row, Col, Tag } from 'antd'
import { useSelector } from 'react-redux'
import { UserOutlined, PhoneOutlined, MailOutlined, TeamOutlined, CheckCircleOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const ProfileCard = () => {
  const user = useSelector((state) => state.auth.user)

  const getStatusColor = (status) => {
    if (!status) return 'default'
    const statusMap = {
      active: 'success',
      inactive: 'error',
      pending: 'warning',
      suspended: 'default'
    }
    return statusMap[status.toLowerCase()] || 'default'
  }

  return (
    <Card
      className="profile-card"
      style={{
        maxWidth: 800,
        margin: '0 auto',
        borderRadius: 16,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        overflow: 'hidden'
      }}
    >
      <Row gutter={[24, 24]} align="middle">
        <Col xs={24} sm={8} md={6} style={{ textAlign: 'center' }}>
          <Avatar 
            size={120} 
            icon={<UserOutlined />} 
            src={user?.profilePicture}
            style={{ 
              backgroundColor: '#10b981', 
              boxShadow: '0 4px 14px rgba(16, 185, 129, 0.2)',
              margin: '0 auto 16px'
            }} 
          />
          <Title level={4} style={{ margin: '8px 0 4px' }}>
            {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : 'User'}
          </Title>
          {user?.userType && (
            <Tag color="blue" style={{ borderRadius: 12, padding: '0 8px' }}>
              {user.userType}
            </Tag>
          )}
        </Col>
        
        <Col xs={24} sm={16} md={18}>
          <Title level={4} style={{ marginBottom: 24, borderBottom: '1px solid #f0f0f0', paddingBottom: 12 }}>
            Profile Information
          </Title>
          
          <Descriptions column={{ xs: 1, sm: 1, md: 2 }} layout="vertical" bordered={false} size="small">
            <Descriptions.Item 
              label={<Text strong style={{ fontSize: 15 }}>Email Address</Text>}
              labelStyle={{ color: '#4b5563' }}
              contentStyle={{ fontSize: 15 }}
            >
              <Row align="middle" gutter={8}>
                <Col><MailOutlined style={{ color: '#10b981' }} /></Col>
                <Col>{user?.emailAddress || 'N/A'}</Col>
              </Row>
            </Descriptions.Item>
            
            <Descriptions.Item 
              label={<Text strong style={{ fontSize: 15 }}>Phone Number</Text>}
              labelStyle={{ color: '#4b5563' }}
              contentStyle={{ fontSize: 15 }}
            >
              <Row align="middle" gutter={8}>
                <Col><PhoneOutlined style={{ color: '#3b82f6' }} /></Col>
                <Col>{user?.phoneNumber || 'N/A'}</Col>
              </Row>
            </Descriptions.Item>
            
            <Descriptions.Item 
              label={<Text strong style={{ fontSize: 15 }}>Status</Text>}
              labelStyle={{ color: '#4b5563' }}
              contentStyle={{ fontSize: 15 }}
            >
              <Tag color={getStatusColor(user?.status)} style={{ borderRadius: 12, padding: '2px 10px' }}>
                {user?.status || 'N/A'}
              </Tag>
            </Descriptions.Item>
            
            <Descriptions.Item 
              label={<Text strong style={{ fontSize: 15 }}>Sub-Users Allowed</Text>}
              labelStyle={{ color: '#4b5563' }}
              contentStyle={{ fontSize: 15 }}
            >
              <Row align="middle" gutter={8}>
                <Col><TeamOutlined style={{ color: '#8b5cf6' }} /></Col>
                <Col>{user?.numSubUsersAllowed || '0'}</Col>
              </Row>
            </Descriptions.Item>
          </Descriptions>
          
          {user?.status === 'Active' && (
            <div style={{ marginTop: 16 }}>
              <Tag icon={<CheckCircleOutlined />} color="success">
                Verified Account
              </Tag>
            </div>
          )}
        </Col>
      </Row>
    </Card>
  )
}

export default ProfileCard