import React from 'react';
import dealstable from '../Data/DealsTable';
import { useTranslation } from 'react-i18next';
import './DealsTable.css';

const DealsTable = () => {
    const { t, i18n } = useTranslation();
    return (
        <div className="table-container">
            <div className="header">
                <div>
                    <h1 className="title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >{t("Top Deals in the Market")}</h1>
                    <p className="subtitle" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >
                        {t("Discover the best deals and unbeatable discounts on top products and services in the market today!")}
                    </p>
                </div>
                <button className="explore-btn">{t("Explore all Deals")}</button>
            </div>
            <table className="deals-table">
                <thead>
                    <tr>
                        <th>{t("Deal Title")}</th>
                        <th>{t("Vendor")}</th>
                        <th>{t("Category")}</th>
                        <th>{t("Expiry Date")}</th>
                    </tr>
                </thead>
                <tbody>
                    {dealstable.map((deal) => (
                        <tr key={deal.id}>
                            <td>{t(deal.titleKey)}</td>
                            <td>{t(deal.vendorKey)}</td>
                            <td>{t(deal.categoryKey)}</td>
                            <td>{t(deal.expiryKey)}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default DealsTable;