import React, { useState } from 'react';
import dealsData from '../Data/Deals';
import { useTranslation } from 'react-i18next';
import './TopDeals.css'

const Deals = () => {
    const { t, i18n } = useTranslation();
    return (
        <div className="deal-container">
            <div className="d-header">
                <div>
                    <h1 className="title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >{t("Top Deals in the Market")}</h1>
                    <p className="subtitle" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >
                        {t("Discover the best deals and unbeatable discounts on top products and services in the market today!")}
                    </p>
                </div>
                <button className="explore-btn">{t("Explore all Deals")}</button>
            </div>

            <div className="deal-grid">
                {dealsData.map((deal) => (
                    <div key={deal.id} className="deal-card">
                        <div className="deal-header">{t(deal.companyKey)}</div>
                        <div className="deal-body">
                            <div className="deal-discount">
                                {deal.discount} <span className="off-text">{t("off")}</span>
                            </div>
                            <div className="deal-category">
                                {t("Category")} <br />
                                <span className="deal-category-bold">{t(deal.categoryKey)}</span>
                            </div>
                        </div>
                        {/* <hr /> */}
                        <div className="deal-footer">
                            <div className="offer-end">
                                {t("Offer Ends")} <br />
                                {t(deal.offerEndsKey)}
                            </div>
                            <button className="view-deal">{t("View Deal")}</button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Deals;