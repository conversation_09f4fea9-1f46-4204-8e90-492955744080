/* ============ TOP DEALS CSS ============ */

.deal-container {
    background-color: white;
    padding: 40px 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.d-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.header-content {
    flex: 1;
    min-width: 300px;
}

.title {
    font-size: clamp(24px, 4vw, 32px);
    margin: 0 0 10px 0;
    font-weight: 700;
    color: #333;
    line-height: 1.2;
}

.subtitle {
    font-size: clamp(14px, 2.5vw, 18px);
    color: #666;
    margin: 0;
    max-width: 600px;
    line-height: 1.5;
}

.explore-btn {
    background-color: #9e3ca2;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 160px;
    white-space: nowrap;
}

.explore-btn:hover {
    background-color: #7a2e7f;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(158, 60, 162, 0.3);
}

.deal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.deal-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    text-align: left;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.deal-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.deal-header {
    background: linear-gradient(135deg, #9D3CA20F, #9D3CA225);
    padding: 20px;
    font-weight: 600;
    font-size: clamp(18px, 3vw, 22px);
    color: #333;
    border-bottom: 1px solid #f0f0f0;
}

.deal-body {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    gap: 15px;
}

.deal-discount {
    font-size: clamp(32px, 6vw, 42px);
    font-weight: 700;
    color: #9e3ca2;
    line-height: 1;
}

.off-text {
    font-size: clamp(14px, 2.5vw, 18px);
    font-weight: 400;
    color: #666;
}

.deal-category {
    text-align: right;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.deal-category-bold {
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.deal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    gap: 15px;
}

.offer-end {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.view-deal {
    background-color: #9ca4ae;
    color: #333;
    padding: 10px 18px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 100px;
}

.view-deal:hover {
    background-color: #56054e;
    color: white;
    transform: translateY(-1px);
}
