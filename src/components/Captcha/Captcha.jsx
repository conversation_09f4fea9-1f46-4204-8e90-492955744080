import { useEffect, useRef } from "react";

const TurnstileWidget = ({ siteKey, onVerify }) => {
  const widgetRef = useRef(null);
  const widgetIdRef = useRef(null); // Store widget ID for cleanup

  useEffect(() => {
    if (window.turnstile && widgetRef.current) {
      // Only render if not already rendered
      widgetIdRef.current = window.turnstile.render(widgetRef.current, {
        sitekey: siteKey,
        callback: (token) => {
          onVerify(token);
        },
      });
    }

    // Clean up widget on unmount
    return () => {
      if (window.turnstile && widgetIdRef.current !== null) {
        window.turnstile.remove(widgetIdRef.current);
      }
    };
  }, []);

  return <div ref={widgetRef} className="cf-turnstile" />;
};

export default TurnstileWidget;
