/* TopTechnologies.css - Responsive Version */

/* Container */
.tech-container {
    text-align: center;
    margin-bottom: 40px;
    background-color: white;
    padding: 20px 15px;
    width: 100%;
    box-sizing: border-box;
}

.tech-head {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.title {
    font-size: clamp(20px, 4vw, 32px);
    font-weight: bold;
    margin-bottom: 10px;
    text-align: left;
    line-height: 1.3;
}

.subtitle {
    font-size: clamp(12px, 2.5vw, 16px);
    color: #666;
    margin-bottom: 30px;
    text-align: left;
    line-height: 1.5;
}

/* RTL Adjustments for Title and Subtitle */
[dir="rtl"] .title,
[dir="rtl"] .subtitle {
    text-align: right;
}

[dir="ltr"] .title,
[dir="ltr"] .subtitle {
    text-align: left;
}

/* Grid Layout - More Responsive */
.grid3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    justify-content: center;
    margin-top: 30px;
    padding: 0 20px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

[dir="rtl"] .grid3 {
    direction: rtl;
}

/* Card - More Flexible */
.card3 {
    display: flex;
    background: white;
    width: 100%;
    max-width: 450px;
    min-height: 120px;
    gap: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 20px;
    align-items: center;
    transition: all 0.3s ease;
    margin: 0 auto;
    box-sizing: border-box;
}

.card3:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Card Image */
.card-img3 {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    flex-shrink: 0;
    object-fit: cover;
}

[dir="rtl"] .card3 {
    flex-direction: row-reverse;
}

/* Card Content */
.card-content3 {
    text-align: left;
    flex: 1;
    min-width: 0; /* Prevents text overflow issues */
}

[dir="rtl"] .card-content3 {
    text-align: right;
}

.card-title3 {
    font-size: clamp(16px, 2.5vw, 20px);
    font-weight: bold;
    margin-bottom: 5px;
    line-height: 1.3;
}

.card-subtitle3 {
    font-size: clamp(12px, 2vw, 14px);
    color: #666;
    margin-bottom: 5px;
    line-height: 1.4;
}

.card-description3 {
    font-size: clamp(12px, 2vw, 14px);
    color: #333;
    margin-top: 5px;
    line-height: 1.4;
}

/* Tablet Responsiveness */
@media (max-width: 768px) {
    .tech-container {
        padding: 15px 10px;
    }
    
    .tech-head {
        padding: 0 10px;
    }
    
    .grid3 {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
        padding: 0 10px;
    }
    
    .card3 {
        min-height: 100px;
        padding: 15px;
        gap: 12px;
    }
    
    .card-img3 {
        width: 50px;
        height: 50px;
    }
    
    .title {
        margin-bottom: 8px;
    }
    
    .subtitle {
        margin-bottom: 25px;
    }
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
    .tech-container {
        padding: 10px 5px;
        margin-bottom: 20px;
    }
    
    .tech-head {
        padding: 0 5px;
    }
    
    .grid3 {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 0 5px;
        margin-top: 20px;
    }
    
    .card3 {
        flex-direction: column;
        text-align: center;
        min-height: auto;
        padding: 15px;
        gap: 10px;
        align-items: center;
        max-width: 100%;
    }
    
    [dir="rtl"] .card3 {
        flex-direction: column;
    }
    
    .card-img3 {
        width: 45px;
        height: 45px;
        margin: 0;
    }
    
    .card-content3 {
        text-align: center;
        width: 100%;
    }
    
    [dir="rtl"] .card-content3 {
        text-align: center;
    }
    
    .card-title3 {
        margin-bottom: 3px;
    }
    
    .card-subtitle3 {
        margin-bottom: 3px;
    }
    
    .title {
        margin-bottom: 5px;
    }
    
    .subtitle {
        margin-bottom: 15px;
    }
}

/* Extra Small Mobile */
@media (max-width: 320px) {
    .tech-container {
        padding: 8px 3px;
    }
    
    .grid3 {
        padding: 0 3px;
        gap: 8px;
    }
    
    .card3 {
        padding: 12px;
        gap: 8px;
    }
    
    .card-img3 {
        width: 40px;
        height: 40px;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1200px) {
    .grid3 {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }
    
    .card3 {
        max-width: 500px;
        min-height: 140px;
        padding: 25px;
        gap: 20px;
    }
    
    .card-img3 {
        width: 70px;
        height: 70px;
    }
}