import React from "react";
import { useTranslation } from "react-i18next";
import categories from "../Data/Technology";
import './TopTechnologies.css';

const TechnologyCategories = () => {
    const { t, i18n } = useTranslation();
    const isRTL = i18n.language === 'ar';

    return (
        <div className="tech-container">
            <div className="tech-head">
                <h1 className="title" dir={isRTL ? 'rtl' : 'ltr'}>
                    {t("Top High-Demand Technology Categories")}
                </h1>
                <p className="subtitle" dir={isRTL ? 'rtl' : 'ltr'}>
                    {t("Ratings and reviews ensure transparency, helping you choose the best service providers with confidence.")}
                </p>
            </div>
            <div className="grid3" dir={isRTL ? 'rtl' : 'ltr'}>
                {categories.map((category) => (
                    <div key={category.id} className="card3">
                        <img
                            src={category.image}
                            alt={t(category.titleKey)}
                            className="card-img3"
                            loading="lazy" // Performance optimization
                        />
                        <div className="card-content3">
                            <h2 className="card-title3">
                                {t(category.titleKey)}
                            </h2>
                            <p className="card-subtitle3">
                                {t(category.subtitleKey)}
                            </p>
                            <p className="card-description3">
                                {t(category.descriptionKey)}
                            </p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TechnologyCategories;