// Data/Deals.js
const dealsData = [
    {
        id: 1,
        companyKey: "ABC Cloud Technologies",
        discount: "22%", // Can remain as is since it's numeric
        categoryKey: "Cloud Service",
        offerEndsKey: "Mar 31 2025"
    },
    {
        id: 2,
        companyKey: "Net Tech Consulting",
        discount: "19%",
        categoryKey: "IT Consulting",
        offerEndsKey: "Mar 31 2025"
    },
    {
        id: 3,
        companyKey: "NextGen Software Development Services",
        discount: "50%", // Can remain as is since it's numeric
        categoryKey: "Software Development",
        offerEndsKey: "Mar 31 2025"
    },
    {
        id: 4,
        companyKey: "Net Tech Consulting",
        discount: "25%",
        categoryKey: "IT Consulting",
        offerEndsKey: "Mar 31 2025"
    },
    {
        id: 5,
        companyKey: "Net Tech Consulting",
        discount: "25%",
        categoryKey: "IT Consulting",
        offerEndsKey: "Mar 31 2025"
    },
  {
        id: 6,
        companyKey: "ABC Cloud Technologies",
        discount: "30%", // Can remain as is since it's numeric
        categoryKey: "Cloud Service",
        offerEndsKey: "Mar 31 2025"
    }
];

export default dealsData;