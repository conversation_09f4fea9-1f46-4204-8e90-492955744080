/* Floating Chat Container */
.floating-chat-container {
  position: fixed;
  z-index: 1000;
  pointer-events: none; /* Allow clicks to pass through container */
}

.floating-chat-container * {
  pointer-events: auto; /* Re-enable clicks for child elements */
}

/* Floating Action Button */
.floating-chat-button {
  width: 56px !important;
  height: 56px !important;
  background: linear-gradient(135deg, #9e3ca2 0%, #b84bb8 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.floating-chat-button:hover {
  background: linear-gradient(135deg, #8a3491 0%, #a63ba6 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(158, 60, 162, 0.5) !important;
}

.floating-chat-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 8px rgba(158, 60, 162, 0.4) !important;
}

.floating-chat-button:focus {
  outline: 2px solid #51ae52 !important;
  outline-offset: 2px !important;
}

/* Open state styling */
.floating-chat-button.open {
  background: linear-gradient(135deg, #51ae52 0%, #6bc46d 100%) !important;
  box-shadow: 0 4px 12px rgba(81, 174, 82, 0.4) !important;
}

.floating-chat-button.open:hover {
  background: linear-gradient(135deg, #459a46 0%, #5fb161 100%) !important;
  box-shadow: 0 6px 16px rgba(81, 174, 82, 0.5) !important;
}

/* Pulse animation for new messages */
.floating-chat-button.pulse {
  animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4);
  }
  50% {
    box-shadow: 0 4px 20px rgba(158, 60, 162, 0.8), 0 0 0 8px rgba(158, 60, 162, 0.1);
  }
  100% {
    box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4);
  }
}

/* Badge styling */
.floating-chat-badge .ant-badge-count {
  background: #ff4d4f !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Chat Window */
.floating-chat-window {
  position: absolute;
  bottom: 70px;
  right: 0;
  transform-origin: bottom right;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
}

.floating-chat-window.closed {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8) translateY(20px);
  pointer-events: none;
}

.floating-chat-window.open {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
  pointer-events: auto;
}

/* Floating chatbot specific styling */
.floating-chatbot {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden !important;
}

/* Overlay for mobile */
.floating-chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
  backdrop-filter: blur(2px);
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-chat-container {
    bottom: 16px !important;
    right: 16px !important;
  }

  .floating-chat-window {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    bottom: auto;
    transform-origin: center;
    width: calc(100vw - 32px);
    max-width: 400px;
    height: 70vh;
    max-height: 600px;
  }

  .floating-chat-window.closed {
    transform: translate(-50%, -50%) scale(0.8);
  }

  .floating-chat-window.open {
    transform: translate(-50%, -50%) scale(1);
  }

  .floating-chatbot {
    width: 100% !important;
    height: 100% !important;
  }

  /* Smaller button on mobile */
  .floating-chat-button {
    width: 48px !important;
    height: 48px !important;
    font-size: 18px !important;
  }
}

@media (max-width: 480px) {
  .floating-chat-window {
    width: calc(100vw - 16px);
    height: 80vh;
    top: 50%;
    left: 50%;
  }

  .floating-chat-container {
    bottom: 12px !important;
    right: 12px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .floating-chat-button {
    border: 2px solid white !important;
  }

  .floating-chat-button:focus {
    outline: 3px solid yellow !important;
    outline-offset: 2px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-chat-button,
  .floating-chat-window,
  .floating-chat-overlay {
    transition: none !important;
    animation: none !important;
  }

  .floating-chat-button.pulse {
    animation: none !important;
  }
}

/* Focus management */
.floating-chat-window:focus-within {
  outline: none;
}

/* Ensure proper stacking */
.floating-chat-container {
  isolation: isolate;
}

/* Smooth entrance animation */
.floating-chat-button {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effect for the entire container */
.floating-chat-container:hover .floating-chat-button:not(.open) {
  transform: translateY(-2px);
}

/* Loading state */
.floating-chat-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .floating-chat-overlay {
    background: rgba(0, 0, 0, 0.6);
  }
  
  .floating-chatbot {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
  }
}
