import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Badge, Tooltip } from 'antd';
import { MessageOutlined, CloseOutlined } from '@ant-design/icons';
//bot icon
import { RobotOutlined } from '@ant-design/icons';
import ChatBot from '../Chatbot/ChatBot';
import './FloatingChatWidget.css';

const FloatingChatWidget = ({ 
  position = { bottom: 24, right: 24 },
  showBadge = false,
  badgeCount = 0,
  disabled = false,
  title = "Bond AI"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible] = useState(true);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [tooltipText, setTooltipText] = useState('');
  // Handle escape key to close chat
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when chat is open on mobile
      document.body.style.overflow = window.innerWidth <= 768 ? 'hidden' : 'auto';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  // Handle click outside to close on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && window.innerWidth <= 768) {
        const chatWidget = document.querySelector('.floating-chat-window');
        const chatButton = document.querySelector('.floating-chat-button');
        
        if (chatWidget && !chatWidget.contains(event.target) && 
            chatButton && !chatButton.contains(event.target)) {
          handleClose();
        }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen]);

  const handleToggle = () => {
    if (disabled) return;
    
    setIsOpen(!isOpen);
    setHasNewMessage(false);
    
    // Announce to screen readers
    const announcement = !isOpen ? 'Chat window opened' : 'Chat window closed';
    const ariaLive = document.createElement('div');
    ariaLive.setAttribute('aria-live', 'polite');
    ariaLive.setAttribute('aria-atomic', 'true');
    ariaLive.style.position = 'absolute';
    ariaLive.style.left = '-10000px';
    ariaLive.textContent = announcement;
    document.body.appendChild(ariaLive);
    setTimeout(() => document.body.removeChild(ariaLive), 1000);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleMinimize = () => {
    setIsOpen(false);
  };

  // Simulate new message notification (for demo purposes)
  const simulateNewMessage = () => {
    if (!isOpen) {
      setHasNewMessage(true);
      setTimeout(() => setHasNewMessage(false), 5000);
    }
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="floating-chat-overlay"
          onClick={handleClose}
          aria-hidden="true"
        />
      )}

      {/* Floating Chat Button */}
      <div 
        className="floating-chat-container"
        style={{
          bottom: `${position.bottom}px`,
          right: `${position.right}px`
        }}
      >
        {/* Chat Window */}
        <div className={`floating-chat-window ${isOpen ? 'open' : 'closed'}`}>
          <ChatBot 
            title={title}
            height={500}
            width={350}
            onMinimize={handleMinimize}
            className="floating-chatbot"
          />
        </div>

        {/* Floating Action Button */}
        <Badge 
          count={showBadge ? badgeCount : 0} 
          offset={[-8, 8]}
          className="floating-chat-badge"
        >
          <Tooltip title="Chat with Bond AI" placement="top">
            <Button
              type="primary"
              shape="circle"
              size="large"
              icon={isOpen ? <CloseOutlined /> : <RobotOutlined />}
              onClick={handleToggle}
              disabled={disabled}
              className={`floating-chat-button ${hasNewMessage ? 'pulse' : ''} ${isOpen ? 'open' : ''}`}
              aria-label={isOpen ? 'Close chat' : 'Open chat with Bond AI'}
              aria-expanded={isOpen}
              aria-controls="floating-chat-window"
              title={isOpen ? 'Close chat' : `Chat with ${title}`}
            />
          </Tooltip>
        </Badge>

      

        {/* Demo button to simulate new message
        {process.env.NODE_ENV === 'development' && (
          <Button
            size="small"
            onClick={simulateNewMessage}
            style={{
              position: 'absolute',
              bottom: '70px',
              right: '0px',
              fontSize: '10px',
              padding: '2px 6px',
              height: 'auto'
            }}
          >
            Bond AI
          </Button>
        )} */}
      </div>
    </>
  );
};

export default FloatingChatWidget;
