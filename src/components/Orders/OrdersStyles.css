.orders-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.orders-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.orders-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.orders-subtitle {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #888;
}

.orders-search {
  width: 250px;
}

.orders-table {
  width: 100%;
  
}

.orders-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  color: #666;
  font-weight: 500;
}

.orders-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.orders-count {
  display: flex;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  color: #666;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 80px;
}

.status-approved {
  background-color: #52c41a;
  color: white;
}

.status-running {
  background-color: #52c41a;
  color: white;
}

.status-pending {
  background-color: #faad14;
  color: white;
}

.status-received {
  background-color: #b37feb;
  color: white;
}

.status-expired {
  background-color: #f5222d;
  color: white;
}

.order-link {
  color: #9d3ca2;
  text-decoration: none;
  font-weight: 500;
}

.order-link:hover {
  text-decoration: underline;
}

.table-row-light {
  background-color: #ffffff;
}

.table-row-dark {
  background-color: #f0eaf4;
}

.orders-table .ant-table-tbody > tr.table-row-light:hover > td,
.orders-table .ant-table-tbody > tr.table-row-dark:hover > td {
  background-color: #9e3ca2;
  color: white;
}