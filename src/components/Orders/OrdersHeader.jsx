import React from 'react';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import './OrdersStyles.css';

const { Search } = Input;

const OrdersHeader = ({ onSearch }) => {
  const today = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="orders-header">
      <div>
        <h1 className="orders-title">My Orders</h1>
        <p className="orders-subtitle">Last Updated on {today}</p>
      </div>
      <Search
        placeholder="Search"
        allowClear
        onSearch={onSearch}
        className="orders-search"
        prefix={<SearchOutlined />}
      />
    </div>
  );
};

export default OrdersHeader;
