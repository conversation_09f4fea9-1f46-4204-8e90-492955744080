import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import { useNavigate } from 'react-router-dom';

import './OrdersStyles.css';


// Utility function to get orders from localStorage
const getOrdersFromStorage = () => {
  try {
    const orders = localStorage.getItem('orders');
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error getting orders from localStorage:', error);
    return [];
  }
};
const OrdersTable = ({ orders: hardcodedOrders = [], loading }) => {
  const [allOrders, setAllOrders] = useState([]);
  const navigate = useNavigate();


  useEffect(() => {
    // Get orders from localStorage
    const storedOrders = getOrdersFromStorage();
    
    // Combine hardcoded orders with stored orders
    const combinedOrders = [...hardcodedOrders, ...storedOrders];
    
    setAllOrders(combinedOrders);
  }, [hardcodedOrders]);

  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'status-approved';
      case 'running':
        return 'status-running';
      case 'pending':
        return 'status-pending';
      case 'received':
        return 'status-received';
      case 'expired':
        return 'status-expired';
      default:
        return '';
    }
  };

  const getRowClassName = (record, index) => {
    return index % 2 === 0 ? 'table-row-light' : 'table-row-dark';
  };
  
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
    },
    {
      title: 'Product Name',
      dataIndex: 'productName',
      key: 'productName',
      width: '20%',
      render: (text, record) => (
  <span
    className="order-link"
    onClick={() => navigate('/vendorOrder')}
    style={{ cursor: 'pointer', color: '#9e3ca2;' }}
  >
    {text}
  </span>
),

    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: '15%',
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor',
      key: 'vendor',
      width: '15%',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status) => (
        <span className={`status-badge ${getStatusClass(status)}`}>
          {status}
        </span>
      ),
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      width: '10%',
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      width: '10%',
    },
    {
      title: 'Order Status',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      width: '10%',
    },
  ];

  return (
    <>
      <div className="orders-count">
        <span>{allOrders.length} Results Found</span>
        <span>04 Products Selected</span>
      </div>
      <Table
        className="orders-table"
        columns={columns}
        dataSource={allOrders} // Changed from `orders` to `allOrders`
        rowKey="id"
        pagination={false}
        loading={loading}
        size="middle"
        rowClassName={getRowClassName}
      />
    </>
  );
};

export default OrdersTable;
