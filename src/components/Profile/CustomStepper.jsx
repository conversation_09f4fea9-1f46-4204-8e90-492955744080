import React from 'react';
import './CustomStepper.css';

const CustomStepper = ({ steps, currentStep }) => {
  return (
    <div className="custom-stepper-container">
      {steps.map((step, index) => {
        let stepClass = "custom-step";
        
        if (index < currentStep) {
          stepClass += " completed";
        } else if (index === currentStep) {
          stepClass += " active";
        } else {
          stepClass += " upcoming";
        }
        
        return (
          <div key={index} className={stepClass}>
            <div className="step-content">
              <div className="step-title">{step.title}</div>
              <div className="step-description">{step.description}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default CustomStepper;
