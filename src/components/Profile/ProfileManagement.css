/* Modern Profile Setup Wizard Styles */

/* Main Container */
.profile-management-container {
  padding: 32px 24px;
  max-width: 1800px;
  width: 100%;
  margin: 0 auto;
  background-color: #f8f9fa;
  min-height: 90vh;
}

/* Header Styles */
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #e0e4e8;
}

.profile-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.profile-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 8px 0 0;
  font-weight: 400;
}

.profile-last-login {
  font-size: 14px;
  color: #94a3b8;
  font-style: italic;
}

/* Steps / Progress Indicator */
.profile-steps-container {
  /* margin-bottom: 36px; */
  background-color: #fff;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Enhanced Steps Styling */
.profile-steps-container .ant-steps .ant-steps-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #64748b;
  padding: 0 8px;
  transition: all 0.3s ease;
}

.profile-steps-container .ant-steps .ant-steps-item-description {
  font-size: 13px;
  color: #94a3b8;
  padding: 0 8px;
}

/* Step states styling */
.profile-steps-container .ant-steps .ant-steps-item-finish .ant-steps-item-icon {
  background-color: #fff;
  border-color: #10b981;
}

.profile-steps-container .ant-steps .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  /* color: #10b981; */
}

/* Current step highlight */
.profile-steps-container .ant-steps .ant-steps-item-process {
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 12px;
  padding:  12px;
  /* width: 100%; */
  /* margin: 10px; */
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
}

.profile-steps-container .ant-steps .ant-steps-item-process .ant-steps-item-icon {
  /* background-color: #10b981; */
  border-color: #10b981;
}

.profile-steps-container .ant-steps .ant-steps-item-process .ant-steps-item-title {
  font-weight: 600;
  color: #0f766e;
}

.profile-steps-container .ant-steps .ant-steps-item-process .ant-steps-item-description {
  color: #0f766e;
}

.profile-steps-container .ant-steps .ant-steps-item-wait .ant-steps-item-icon {
  background-color: #fff;
  border-color: #cbd5e1;
}

.profile-steps-container .ant-steps .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
  color: #cbd5e1;
}

/* Form Container */
.profile-form-container {
  background-color: #fff;
  padding: 32px;
  margin: 0 auto;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.07);
}

.form-fields {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Form Sections */
.form-section {
  margin: 0 auto 32px auto;
  width: 100%;
  max-width: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.form-section-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #2c3e50;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 12px;
  width: 100%;
  text-align: left;
}

/* Form Fields Layout */
.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 28px 32px; /* Vertical gap 28px, horizontal gap 32px */
  width: 100%;
  margin-bottom: 20px;
  align-items: flex-start;
}

/* Responsive adjustment for the form grid */
@media (max-width: 767px) {
  .form-row {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 20px;
  }
}

/* For specific layouts where you want 1 or 3 columns */
.form-row.one-column {
  grid-template-columns: 1fr;
}

.form-row.three-column {
  grid-template-columns: repeat(3, 1fr);
}

@media (min-width: 992px) {
  .form-row.three-column {
    grid-template-columns: repeat(3, 1fr);
  }
}

.form-item-wrapper {
  width: 100%;
  min-height: 82px; /* Consistent height for form items */
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  position: relative; /* For positioning validation messages */
}

.form-item {
  width: 100%;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
}

/* Ensure all form inputs have consistent height and alignment */
.form-item .ant-form-item-control {
  flex: 1;
}

.form-item .ant-form-item-control-input {
  min-height: 42px;
}

/* Ensure input fields take the full width */
.form-item .ant-form-item-control-input-content {
  display: flex;
}

/* Field Labels and Validation */
.form-item .ant-form-item-label > label {
  font-weight: 500;
  color: #334155;
  font-size: 15px;
}

.form-item .ant-form-item-label > label.ant-form-item-required::before {
  display: inline-block;
  margin-right: 4px;
  color: #ef4444;
  font-size: 14px;
  line-height: 1;
  content: '*';
}

.form-item .ant-form-item-explain-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Input Fields Styling */
.form-item .ant-input,
.form-item .ant-select .ant-select-selector,
.form-item .ant-picker,
.form-item .ant-input-number,
.form-item .ant-cascader-picker {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  height: 42px;
  box-shadow: none;
  transition: all 0.2s ease;
}

/* Ensure Select components take full width */
.form-item .ant-select {
  width: 100%;
}

/* Fix the height and alignment of select controls */
.form-item .ant-select-selector {
  display: flex;
  align-items: center;
}

/* Fix alignment of Checkbox and Radio controls */
.form-item .ant-checkbox-wrapper,
.form-item .ant-radio-wrapper {
  height: 42px;
  display: flex;
  align-items: center;
}

.form-item .ant-input:hover,
.form-item .ant-select-selector:hover,
.form-item .ant-picker:hover,
.form-item .ant-input-number:hover,
.form-item .ant-cascader-picker:hover {
  border-color: #94a3b8;
}

.form-item .ant-input:focus,
.form-item .ant-input-focused,
.form-item .ant-select-focused .ant-select-selector,
.form-item .ant-picker-focused,
.form-item .ant-input-number-focused,
.form-item .ant-cascader-picker-focused {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1) !important;
  outline: none;
}

.form-item .ant-input::placeholder {
  color: #a0aec0;
}

/* Checkbox and Radio Styling */
.form-item .ant-checkbox-wrapper,
.form-item .ant-radio-wrapper {
  font-size: 15px;
  color: #334155;
}

.form-item .ant-checkbox-checked .ant-checkbox-inner,
.form-item .ant-radio-checked .ant-radio-inner {
  background-color: #10b981;
  border-color: #10b981;
}

/* File Upload Styling */
.file-upload-container {
  width: 100%;
}

.form-item .ant-upload-list {
  margin-top: 12px;
}

.form-item .ant-form-item-extra {
  font-size: 13px;
  color: #64748b;
  margin-top: 6px;
}

.upload-logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.logo-preview {
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  margin: 0 auto 16px;
  overflow: hidden;
  background-color: #f8fafc;
}

.logo-preview img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

/* Preview Section */
.form-section-preview {
  margin: 0 auto 24px auto;
  width: 100%;
  max-width: auto;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  padding: 24px;
  border-radius: 12px;
}

.form-section-title-preview {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
}

/* Card Styling in Preview */
.form-section-preview .ant-card {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-section-preview .ant-card-head {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.form-section-preview .ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
  color: #334155;
}

.form-section-preview .ant-descriptions-item-label {
  font-weight: 500;
  color: #64748b;
}

.form-section-preview .ant-descriptions-item-content {
  color: #334155;
}

/* Navigation Buttons */
.form-buttons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.form-buttons-left {
  display: flex;
  align-items: center;
}

.form-buttons-right {
  display: flex;
  gap: 12px;
}

.form-buttons button {
  font-size: 15px;
  font-weight: 500;
  border-radius: 24px;
  padding: 10px 24px;
  height: auto;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.form-buttons .cancel-button {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.form-buttons .cancel-button:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

.form-buttons .previous-button {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.form-buttons .previous-button:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

.form-buttons .next-button {
  background-color: #10b981;
  color: white;
  border: 1px solid #10b981;
}

.form-buttons .next-button:hover {
  background-color: #059669;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.form-buttons .save-button {
  background-color: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.form-buttons .save-button:hover {
  background-color: #2563eb;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Alert Styling */
.profile-form-container .ant-alert.ant-alert-success {
  border-radius: 12px;
  margin-bottom: 24px;
  width: 100%;
  border: none;
  background-color: #dcfce7;
}

.profile-form-container .ant-alert-with-description .ant-alert-message {
  font-weight: 600;
  font-size: 16px;
  color: #10b981;
}

.profile-form-container .ant-alert-with-description .ant-alert-description {
  color: #065f46;
}

/* Disabled Input Styling */
.disabled-input {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.65) !important;
  cursor: not-allowed !important;
  border-color: #d9d9d9 !important;
}

.disabled-input:hover {
  border-color: #d9d9d9 !important;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .profile-management-container {
    padding: 24px 16px;
  }
  
  .profile-form-container {
    padding: 24px;
  }
  
  .form-section {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .profile-steps-container {
    padding: 20px 16px;
    margin-bottom: 24px;
  }
  
  .profile-form-container {
    padding: 20px 16px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-section-title {
    font-size: 20px;
  }
  
  .form-buttons {
    flex-direction: column-reverse;
    gap: 16px;
  }
  
  .form-buttons-left,
  .form-buttons-right {
    width: 100%;
  }
  
  .form-buttons-right {
    display: flex;
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .form-buttons button {
    width: 100%;
  }
  
  .profile-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .profile-last-login {
    margin-top: 12px;
  }
}

@media (max-width: 480px) {
  .profile-management-container {
    padding: 16px 12px;
  }
  
  .profile-steps-container .ant-steps-item-icon {
    margin-right: 8px;
  }
  
  .profile-steps-container .ant-steps-item-description {
    display: none; /* Hide description on very small screens */
  }
  
  .profile-steps-container .ant-steps-item-title {
    font-size: 13px;
  }
  
  .form-section-title {
    font-size: 18px;
  }
  
  .form-buttons button {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* RTL Support */
[dir="rtl"] .profile-title,
[dir="rtl"] .profile-subtitle,
[dir="rtl"] .form-section-title {
  text-align: right;
}

[dir="rtl"] .form-item .ant-form-item-label > label.ant-form-item-required::before {
  margin-left: 4px;
  margin-right: 0;
}

[dir="rtl"] .form-buttons {
  flex-direction: row-reverse;
}

[dir="rtl"] .form-buttons-right {
  flex-direction: row-reverse;
}

/* Bond Logo */
.bond-logo {
  width: 100px;
  height: auto;
  margin: 0 auto;
  display: block;
}

.bond-logo img {
  width: 100%;
  height: auto;
}