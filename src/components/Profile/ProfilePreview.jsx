import React from 'react';
import { Card, Descriptions, Typography, Divider, Button } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { CustomerInputs } from './CustomerInputs';
import { VendorInputs } from './VendorInput';

const ProfilePreview = ({
  title,
  fields,
  values,
  stepIndex,
  onEdit,
  userFields,
  formatFieldValue
}) => {
  const { t } = useTranslation();

  console.log('ProfilePreview:', title, 'values:', values);
  console.log('ProfilePreview:', title, 'userFields:', userFields);

  return (
    <Card
      title={t(title)}
      style={{ marginBottom: 16 }}
      extra={
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => onEdit(stepIndex)}
          disabled={fields.every(field => {
            const fieldName = getFieldName(field.label);
            return ['firstName', 'lastName', 'emailAddress', 'phoneNumber'].includes(fieldName) && userFields && (
              (fieldName === 'firstName' && userFields.first_name) ||
              (fieldName === 'lastName' && userFields.last_name) ||
              (fieldName === 'emailAddress' && userFields.email) ||
              (fieldName === 'phoneNumber' && userFields.phone)
            );
          })}
        />
      }
    >
      <Descriptions bordered column={1}>
        {fields.map((field) => {
          const fieldName = getFieldName(field.label);

          // Get value from form values first, then check userFields with correct mapping
          let value = values[fieldName];

          // Check userFields with the correct mapping
          if (!value && userFields) {
            const userFieldMappings = {
              'firstName': userFields.first_name,
              'lastName': userFields.last_name,
              'emailAddress': userFields.email,
              'phoneNumber': userFields.phone
            };
            value = userFieldMappings[fieldName];
          }

          console.log('ProfilePreview field:', field.label, 'fieldName:', fieldName, 'value:', value);

          // Debug address fields specifically
          if (['street_address', 'city', 'state', 'zip_code', 'country'].includes(fieldName)) {
            console.log(`ProfilePreview ADDRESS field ${field.label}:`, {
              fieldName,
              valueFromFormValues: values[fieldName],
              allFormValuesKeys: Object.keys(values),
              hasValue: !!value
            });
          }

          const isAutoFilled = ['firstName', 'lastName', 'emailAddress', 'phoneNumber'].includes(fieldName) && userFields && (
            (fieldName === 'firstName' && userFields.first_name) ||
            (fieldName === 'lastName' && userFields.last_name) ||
            (fieldName === 'emailAddress' && userFields.email) ||
            (fieldName === 'phoneNumber' && userFields.phone)
          );

          return (
            <Descriptions.Item 
              key={field.id} 
              label={
                <span>
                  {t(field.label)}
                  {isAutoFilled && <span style={{ color: '#8c8c8c', marginLeft: '8px' }}></span>}
                </span>
              }
            >
              {formatFieldValue(value, field.type) || t('Not provided')}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </Card>
  );
};

const getFieldName = (label) => {
  // Use the same field mappings as FormFields.jsx to ensure consistency
  const fieldMappings = {
    // Customer fields
    'First Name': 'firstName',
    'Last Name': 'lastName',
    'Email': 'emailAddress',
    'Phone Number': 'phoneNumber',
    'Date of Birth': 'dateOfBirth',
    'Profile Picture': 'profilePicture',
    'Designation/Role': 'designationOrRole',
    'Industry/Domain': 'industryOrDomain',
    'Company Name': 'companyName',
    'Preferred Vendor Type': 'preferredVendorType',
    'Subcategories of Interest': 'subcategoriesOfInterest',
    'Categories of Services Needed': 'categoriesOfServicesNeeded',
    'Receive notifications from vendors': 'receiveNotificationsFromVendors',
    'Visible to Vendors': 'visibleToVendors',
    // Vendor fields - use the same mapping as FormFields.jsx
    'Business Name': 'business_name',
    'Upload Logo': 'upload_logo',
    'Business Email': 'business_email',
    'Business Phone': 'business_phone',
    'Street Address': 'street_address',
    'City': 'city',
    'State': 'state',
    'Zip Code': 'zip_code',
    'Country': 'country',
    'Business Category': 'business_category',
    'Business Subcategory': 'business_subcategory',
    'Business Website': 'business_website',
    'Years of Experience': 'years_of_experience',
    'Upload Certificate': 'upload_certificate',
    'Product Name': 'product_name',
    'Product Description': 'product_description',
    'Product Category': 'product_category',
    'Registration Number': 'registration_number',
    'Regions Supported': 'regions_supported',
    'License Details': 'license_details',
    'Upload Additional Documents': 'upload_additional_documents'
  };

  return fieldMappings[label] || label.toLowerCase().replace(/\s+/g, '_').replace(/\//g, '/');
};

export const CustomerProfilePreview = ({ formValues, userFields, onEdit, formatFieldValue }) => {
  const { t } = useTranslation();

  return (
    <div className="form-section-preview">
      <div className="form-section-title-preview">{t("Review Your Information")}</div>
      <Typography.Paragraph>
        {t("Please review all the information you've provided before submitting.")}
      </Typography.Paragraph>

      <ProfilePreview
        title="Personal Information"
        fields={CustomerInputs.personal_information}
        values={formValues}
        stepIndex={0}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Primary Address"
        fields={CustomerInputs.shipping_information.primary_address}
        values={formValues}
        stepIndex={1}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Business Details"
        fields={CustomerInputs.business_details}
        values={formValues}
        stepIndex={2}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Preferences"
        fields={CustomerInputs.preferences.communication_preferences}
        values={formValues}
        stepIndex={3}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />
    </div>
  );
};

export const VendorProfilePreview = ({ formValues, userFields, onEdit, formatFieldValue, logoUrl }) => {
  const { t } = useTranslation();

  return (
    <div className="form-section-preview">
      <Typography.Paragraph>
        {t("Please review all the information you've provided before submitting.")}
      </Typography.Paragraph>

      <ProfilePreview
        title="Business Basic Information"
        fields={VendorInputs.business_basic_details}
        values={formValues}
        stepIndex={0}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <div className="logo-preview" style={{ margin: '20px 0' }}>
        {logoUrl ? (
          <div>
            <Typography.Text strong>Business Logo:</Typography.Text>
            <img src={logoUrl} alt="Company Logo" style={{ maxWidth: '200px', display: 'block', margin: '10px 0' }} />
          </div>
        ) : (
          <Typography.Text>No logo uploaded</Typography.Text>
        )}
      </div>

      <Divider />

      <ProfilePreview
        title="Business Address"
        fields={VendorInputs.business_address}
        values={formValues}
        stepIndex={1}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Business Details"
        fields={VendorInputs.business_details}
        values={formValues}
        stepIndex={2}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Product Details"
        fields={VendorInputs.product_details}
        values={formValues}
        stepIndex={3}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <ProfilePreview
        title="Business Preferences"
        fields={VendorInputs.preferences}
        values={formValues}
        stepIndex={4}
        onEdit={onEdit}
        userFields={userFields}
        formatFieldValue={formatFieldValue}
      />

      <Divider />

      <Card
        title={t("Additional Documents")}
        style={{ marginBottom: 16 }}
        extra={
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(5)}
          />
        }
      >
        <Typography.Paragraph>
          {t("You can manage your additional documents in the Additional Documents section.")}
        </Typography.Paragraph>
      </Card>
    </div>
  );
}; 