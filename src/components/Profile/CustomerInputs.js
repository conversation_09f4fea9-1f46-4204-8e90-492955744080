//configuration for input fields
export const CustomerInputs = {
    personal_information: [
        {
            id: 'first_name',
            label: 'First Name',
            type: 'text',
            autoFilled: true,
            disabled: true
        },
        {
            id: 'last_name',
            label: 'Last Name',
            type: 'text',
            autoFilled: true,
            disabled: true
        },
       {
                id: 'email',
                label: 'Email',  // Change this to match the field name mapping
                type: 'email',
                autoFilled: true,
                disabled: true
            },
        {
            id: 'phone',
            label: 'Phone Number',
            type: 'tel'
        },
        {
            id: 5,
            label: "Date of Birth",
            type: "date",
        },
        {
            id: 6,
            label: "Profile Picture",
            type: "file",
            accept: "image/*",
            fileType: "profile",
            description: "Upload a profile picture (JPG, PNG, max 5MB)"
        }
    ],
    shipping_information: {
        primary_address: [
            {
                id: 1,
                label: "Street Address",
                type: "text",
            },
            {
                id: 2,
                label: "City",
                type: "text",
            },
            {
                id: 3,
                label: "State",
                type: "text",
            },
            {
                id: 4,
                label: "Zip Code",
                type: "text",
            },
            {
                id: 5,
                label: "Country",
                type: "text",
            }
        ],
        secondary_address: [
            {
                id: 1,
                label: "Street Address",
                type: "text",
            },
            {
                id: 2,
                label: "City",
                type: "text",
            },
            {
                id: 3,
                label: "State",
                type: "text",
            },
            {
                id: 4,
                label: "Zip Code",
                type: "text",
            },
            {
                id: 5,
                label: "Country",
                type: "text",
            }
        ]
    },
    // payment_information: {
    //     // Credit/debit card details, payment methods, billing address.
    //     payment_methods: [
    //         {
    //             id: 1,
    //             label: "Card Number",
    //             type: "text",
    //         },
    //         {
    //             id: 2,
    //             label: "Expiration Date",
    //             type: "text",
    //         },
    //         {
    //             id: 3,
    //             label: "CVV",
    //             type: "text",
    //         },
    //         {
    //             id: 4,
    //             label: "Billing Address",
    //             type: "text",
    //         },
    //     ]
    // },
    //type of vendor
    
    business_details: [
        {
            id: 1,
            label: "Designation/Role",
            type: "text",
        },
        {
            id: 2,
            label: "Industry/Domain",
            type: "text",
        },
        {
            id: 3,
            label: "Company Name",
            type: "text",
        },
        {
            id: 4,
            label: "Preferred Vendor Type",
            type: "text",
        },
        {
            id: 5,
            label: "Subcategories of Interest",
            type: "text",
        },
        {
            id: 6,
            label: "Categories of Services Needed",
            type: "text",
        }
    ],
    preferences: {
        communication_preferences: [
            {
                id: 1,
                label: "Receive notifications from vendors",
                type: "checkbox",
            },
            // {
            //     id: 2,
            //     label: "SMS",
            //     type: "checkbox",
            // },
            {
                id: 2,
                label: "Visible to Vendors",
                type: "checkbox",
            }
        ],
        // loyalty_program: [
        //     {
        //         id: 1,
        //         label: "Enroll in Loyalty Program",
        //         type: "checkbox",
        //     },
        // ],
        // promotional_opt_ins: [
        //     {
        //         id: 1,
        //         label: "Receive Promotions",
        //         type: "checkbox",
        //     },
        // ]
    }
}