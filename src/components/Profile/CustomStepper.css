.custom-stepper-container {
  display: flex;
  width: 100%;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
}

.custom-step {
  flex: 1;
  position: relative;
  height: 70px;
  display: flex;
  align-items: center;
  clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%, 10% 50%);
  margin-right: -20px; /* Negative margin for overlapping effect */
  z-index: 1;
  padding-left: 30px;
  min-width: 200px;
  transition: all 0.3s ease;
}

/* First step has a different clip path (no point on the left) */
.custom-step:first-child {
  clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%);
  padding-left: 15px;
}

/* Last step has a different clip path (no point on the right) */
.custom-step:last-child {
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%, 10% 50%);
}

/* Step status colors */
.custom-step.completed {
  background-color: purple;
  color: white;
  z-index: 3;
}

.custom-step.active {
  background-color: rgba(128, 0, 128, 0.818);
  color: white;
  z-index: 4;
}

.custom-step.upcoming {
  background-color: #e0e0e0;
  color: #666;
  z-index: 2;
}

.step-content {
  width: 100%;
  padding-right: 25px;
}

.step-title {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.step-description {
  font-size: 12px;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive styles */
@media (max-width: 768px) {
  .custom-step {
    min-width: 150px;
  }
  
  .step-description {
    display: none;
  }
}

@media (max-width: 576px) {
  .custom-step {
    min-width: 120px;
    height: 50px;
    padding-left: 20px;
  }
  
  .step-title {
    font-size: 13px;
  }
}
