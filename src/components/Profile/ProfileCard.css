.profile-card-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.profile-card {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
  border: none;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.profile-card:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 24px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 16px 16px 0 0;
}

.profile-avatar {
  margin-right: 32px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-avatar .ant-avatar {
  border: 4px solid #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.avatar-container {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}

.clickable-avatar {
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.view-text {
  color: white;
  font-weight: 500;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-container:hover .clickable-avatar {
  transform: scale(1.05);
}

.avatar-link {
  display: block;
  border-radius: 50%;
  overflow: hidden;
}

.certificate-container {
  /* display: flex; */
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f5f7fa;
  width: 80%;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.certificate-container:hover {
  background-color: #e6f7ff;
}

.certificate-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.certificate-actions {
  display: flex;
  gap: 12px;
}

.file-link {
  color: #1890ff;
  font-size: 16px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(24, 144, 255, 0.1);
  border: none;
  cursor: pointer;
  padding: 0;
  outline: none;
}

.file-link:hover {
  color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.2);
  transform: translateY(-2px);
}

.view-link:hover {
  color: #1890ff;
}

.download-link:hover {
  color: #52c41a;
}

.profile-title {
  flex: 1;
}

.profile-title h3 {
  margin-bottom: 8px;
  font-weight: 600;
  color: #333333;
}

.profile-tags {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.profile-tags .ant-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.profile-actions {
  margin-left: 24px;
}

.profile-actions .ant-btn {
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-actions .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-section-card {
  height: 100%;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  margin-bottom: 8px;
  width: 100%;
}

.profile-section-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.profile-section-card .ant-card-head {
  background-color: #f5f7fa;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  border-radius: 12px 12px 0 0;
}

.profile-section-card .ant-card-head-title {
  font-weight: 600;
  color: #444444;
}

.profile-section-card .ant-card-body {
  padding: 16px;
}

.profile-section-card .ant-descriptions-item-label {
  width: 40%;
  font-weight: 500;
  color: #666666;
  padding-bottom: 12px;
}

.profile-section-card .ant-descriptions-item-content {
  color: #333333;
  padding-bottom: 12px;
}

/* Remove borders from descriptions */
.profile-section-card .ant-descriptions-bordered .ant-descriptions-item-label,
.profile-section-card .ant-descriptions-bordered .ant-descriptions-item-content {
  padding: 12px 16px;
  border: none !important;
  background-color: transparent;
}

/* Add subtle separation between rows */
.profile-section-card .ant-descriptions-row {
  margin-bottom: 8px;
}

.profile-section-card .ant-descriptions-row:not(:last-child) {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
  padding-bottom: 8px;
}

/* Remove table borders */
.profile-section-card .ant-descriptions-bordered,
.profile-section-card .ant-descriptions-bordered .ant-descriptions-view {
  border: none;
}

.profile-section-card .ant-descriptions-bordered .ant-descriptions-row {
  border: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 12px;
  }

  .profile-avatar {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .profile-actions {
    margin-left: 0;
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .profile-actions .ant-btn {
    width: 100%;
    max-width: 200px;
  }

  .profile-tags {
    justify-content: center;
  }

  .profile-section-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .profile-card-container {
    padding: 8px;
  }

  .profile-card {
    border-radius: 12px;
  }

  .profile-header {
    padding: 16px 8px;
  }

  .profile-section-card .ant-descriptions-item-label,
  .profile-section-card .ant-descriptions-item-content {
    padding: 8px 12px;
  }

  .certificate-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .certificate-name {
    margin-bottom: 8px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .certificate-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

/* RTL Support */
[dir="rtl"] .profile-avatar {
  margin-right: 0;
  margin-left: 32px;
}

[dir="rtl"] .profile-actions {
  margin-left: 0;
  margin-right: 24px;
}

@media (max-width: 768px) {
  [dir="rtl"] .profile-avatar {
    margin-left: 0;
  }

  [dir="rtl"] .profile-actions {
    margin-right: 0;
  }
}
