import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import logo from '../../assets/arabic-logo.svg';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from "react-redux";
import './Header.css';

const Header = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const dropdownRef = useRef(null);
    const location = useLocation();

    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 600);

    const languages = useMemo(() => [
        { code: 'en', label: 'English', dir: 'ltr' },
        { code: 'ar', label: 'العربية', dir: 'rtl' }
    ], []);

    const [selectedLanguage, setSelectedLanguage] = useState(() => {
        const savedCode = localStorage.getItem('i18nextLng') || 'en';
        return savedCode === 'ar' ? 'العربية' : 'English';
    });

    // Handle window resize for responsive behavior
    useEffect(() => {
        const handleResize = () => {
            const mobile = window.innerWidth <= 600;
            setIsMobile(mobile);
            
            // Close mobile menu when switching to desktop
            if (!mobile && isMenuOpen) {
                setIsMenuOpen(false);
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [isMenuOpen]);

    // Language and direction setup
    useEffect(() => {
        const lang = i18n.language || 'en';
        const currentLang = languages.find(l => l.code === lang) || languages[0];

        document.documentElement.lang = currentLang.code;
        document.documentElement.dir = currentLang.dir;
        document.body.style.direction = currentLang.dir;

        if (currentLang.dir === 'rtl') {
            document.body.classList.add('rtl');
            document.body.classList.remove('ltr');
        } else {
            document.body.classList.add('ltr');
            document.body.classList.remove('rtl');
        }
    }, [i18n.language, languages]);

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code).then(() => {
            setSelectedLanguage(language.label);
            localStorage.setItem('i18nextLng', language.code);
            setIsLangDropdownOpen(false);
        });
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsLangDropdownOpen(false);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Close mobile menu when route changes
    useEffect(() => {
        setIsMenuOpen(false);
    }, [location.pathname]);

    // Close mobile menu on escape key
    useEffect(() => {
        const handleEscape = (event) => {
            if (event.key === 'Escape') {
                setIsMenuOpen(false);
                setIsLangDropdownOpen(false);
            }
        };

        if (isMenuOpen || isLangDropdownOpen) {
            document.addEventListener('keydown', handleEscape);
            return () => document.removeEventListener('keydown', handleEscape);
        }
    }, [isMenuOpen, isLangDropdownOpen]);

    const isAuthRoute = ['/signin', '/signup', '/set-password', '/forgot-password', '/activate'].includes(location.pathname);
    const isAuthenticated = useSelector(state => state.auth.isAuthenticated);

    const handleLogoClick = () => {
        navigate('/');
        setIsMenuOpen(false);
    };

    const handleMenuToggle = () => {
        setIsMenuOpen(prev => !prev);
    };

    const handleNavigation = (path) => {
        navigate(path);
        setIsMenuOpen(false);
    };

    return (
        <header className="header-nav-bar">
            <div className="header-logo">
                <img 
                    src={logo} 
                    alt="Logo" 
                    onClick={handleLogoClick}
                    style={{ cursor: 'pointer' }} 
                />
            </div>

            {/* Conditional rendering based on route */}
            {!isAuthRoute && (
                <>
                    <input
                        type="text"
                        className="navbar-search-box"
                        placeholder={t("Find Vendors, Services, or Products")}
                        aria-label={t("Search")}
                    />

                    <button 
                        className="navbar-toggle" 
                        onClick={handleMenuToggle}
                        aria-label={t("Toggle menu")}
                        aria-expanded={isMenuOpen}
                    >
                        {isMenuOpen ? '✕' : '☰'}
                    </button>

                    <nav 
                        className={`navbar-menu ${isMenuOpen ? 'show' : ''}`}
                        role="navigation"
                        aria-label={t("Main navigation")}
                    >
                        <button 
                            className='navbar-menu-button'
                            onClick={() => handleNavigation('/')}
                        >
                            {t("Home")}
                        </button>
                        
                        <button 
                            className='navbar-menu-button'
                            onClick={() => handleNavigation('/about')}
                        >
                            {t("About")}
                        </button>
                        
                        <button 
                            className='navbar-menu-button'
                            onClick={() => handleNavigation('/features')}
                        >
                            {t("Features")}
                        </button>
                        
                        <button 
                            className='navbar-menu-button'
                            onClick={() => handleNavigation('/plans')}
                        >
                            {t("Pricing")}
                        </button>

                        {location.pathname === "/" && (
                            <button 
                                className='navbar-btn3' 
                                onClick={() => handleNavigation('/page2')}
                            >
                                {t("Grid View")}
                            </button>
                        )}

                        {!isAuthenticated && (
                            <button 
                                className="navbar-btn1" 
                                onClick={() => handleNavigation('/signup')}
                            >
                                {t("Sign Up")}
                            </button>
                        )}
                    </nav>
                </>
            )}

            {/* Language dropdown - always visible */}
            <div className="dropdown" ref={dropdownRef}>
                <button
                    onClick={() => setIsLangDropdownOpen(prev => !prev)}
                    className="dropdown-toggle"
                    aria-label={t("Language selector")}
                    aria-expanded={isLangDropdownOpen}
                    aria-haspopup="true"
                >
                    {selectedLanguage}
                </button>

                {isLangDropdownOpen && (
                    <div className="dropdown-menu" role="menu">
                        {languages.map((lang) => (
                            <div
                                key={lang.code}
                                onClick={() => handleLanguageChange(lang)}
                                className={`dropdown-item ${lang.label === selectedLanguage ? 'active' : ''}`}
                                role="menuitem"
                            >
                                {lang.label}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </header>
    );
};

export default Header;