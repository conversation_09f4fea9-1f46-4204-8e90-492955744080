@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

.header-nav-bar {
    background: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    height: 70px;
    padding: 0 20px;
    width: 100%;
    z-index: 1000;
    box-sizing: border-box;
}

/* Logo */
.header-logo {
    flex-shrink: 0;
    z-index: 1001;
}

.header-logo img {
    width: 100px;
    height: 40px;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.header-logo img:hover {
    transform: scale(1.05);
}

/* Search Bar */
.navbar-search-box {
    height: 45px;
    padding: 8px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    margin: 0 20px;
    flex: 1;
    max-width: 400px;
    min-width: 200px;
    font-size: 14px;
    transition: all 0.3s ease;
    outline: none;
}

.navbar-search-box:focus {
    border-color: #9e3ca2;
    box-shadow: 0 0 0 3px rgba(158, 60, 162, 0.1);
}

.navbar-search-box::placeholder {
    color: #999;
    font-size: 14px;
}

/* Navigation menu (desktop) */
.navbar-menu {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
}

.navbar-menu-button {
    background: none;
    border: none;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.2s ease;
    font-size: 14px;
    white-space: nowrap;
}

.navbar-menu-button:hover {
    background-color: #f5f5f5;
    color: #9e3ca2;
}

.navbar-btn1 {
    color: white;
    background: #9e3ca2;
    border: 2px solid #9e3ca2;
    font-size: 14px;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
}

.navbar-btn1:hover {
    background: #8a3291;
    border-color: #8a3291;
    transform: translateY(-1px);
}

.navbar-btn2,
.navbar-btn3 {
    color: #9e3ca2;
    background: transparent;
    border: 2px solid #9e3ca2;
    font-size: 13px;
    border-radius: 6px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
}

.navbar-btn2:hover,
.navbar-btn3:hover {
    background: #9e3ca2;
    color: white;
    transform: translateY(-1px);
}

/* Language Dropdown */
.dropdown {
    position: relative;
    margin-left: 15px;
    flex-shrink: 0;
}

.dropdown-toggle {
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    width: 90px;
    height: 35px;
    background-color: transparent;
    cursor: pointer;
    color: #333;
    font-size: 13px;
    transition: all 0.3s ease;
    outline: none;
}

.dropdown-toggle:hover,
.dropdown-toggle:focus {
    border-color: #9e3ca2;
    color: #9e3ca2;
}

.dropdown-menu {
    position: absolute;
    top: 110%;
    right: 0;
    width: 90px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 1002;
    overflow: hidden;
}

.dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover,
.dropdown-item.active {
    background-color: #f0f8ff;
    color: #9e3ca2;
}

/* Hamburger toggle button */
.navbar-toggle {
    display: none;
    font-size: 28px;
    cursor: pointer;
    background: none;
    border: none;
    color: #333;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-left: 10px;
}

.navbar-toggle:hover {
    background-color: #f5f5f5;
    color: #9e3ca2;
}

/* Tablet Responsive - 768px and below */
@media (max-width: 768px) {
    .header-nav-bar {
        height: 65px;
        padding: 0 15px;
    }
    
    .header-logo img {
        width: 85px;
        height: 35px;
    }
    
    .navbar-search-box {
        max-width: 180px;
        min-width: 120px;
        margin: 0 10px;
        font-size: 13px;
        height: 40px;
        padding: 6px 15px;
    }
    
    .navbar-menu {
        gap: 10px;
    }
    
    .navbar-menu-button,
    .navbar-btn1,
    .navbar-btn2,
    .navbar-btn3 {
        font-size: 12px;
        padding: 6px 10px;
    }
    
    .dropdown {
        margin-left: 10px;
    }
    
    .dropdown-toggle {
        width: 80px;
        height: 32px;
        font-size: 12px;
    }
    
    .dropdown-menu {
        width: 80px;
    }
}

/* Mobile Responsive - 600px and below */
@media (max-width: 600px) {
    .header-nav-bar {
        height: 60px;
        padding: 0 10px;
        flex-wrap: wrap;
    }
    
    .header-logo {
        order: 1;
    }
    
    .header-logo img {
        width: 75px;
        height: 30px;
    }
    
    .navbar-search-box {
        order: 2;
        max-width: 140px;
        min-width: 100px;
        margin: 0 8px;
        font-size: 12px;
        height: 35px;
        padding: 5px 12px;
    }
    
    .navbar-toggle {
        display: block;
        order: 3;
        font-size: 24px;
        margin-left: 5px;
    }
    
    .dropdown {
        order: 4;
        margin-left: 8px;
    }
    
    .dropdown-toggle {
        width: 70px;
        height: 30px;
        font-size: 11px;
    }
    
    .dropdown-menu {
        width: 70px;
    }
    
    /* Mobile menu */
    .navbar-menu {
        display: none;
        order: 5;
        flex-direction: column;
        align-items: flex-start;
        background: white;
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        z-index: 999;
        gap: 15px;
    }
    
    .navbar-menu.show {
        display: flex;
    }
    
    .navbar-menu-button,
    .navbar-btn1,
    .navbar-btn2,
    .navbar-btn3 {
        width: 100%;
        text-align: left;
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 8px;
        justify-content: flex-start;
    }
    
    .navbar-menu-button {
        border: 1px solid #e0e0e0;
    }
}

/* Extra Small Mobile - 480px and below */
@media (max-width: 480px) {
    .header-nav-bar {
        height: 55px;
        padding: 0 8px;
    }
    
    .header-logo img {
        width: 65px;
        height: 26px;
    }
    
    .navbar-search-box {
        max-width: 120px;
        min-width: 80px;
        margin: 0 5px;
        font-size: 11px;
        height: 32px;
        padding: 4px 10px;
    }
    
    .navbar-toggle {
        font-size: 22px;
        margin-left: 3px;
    }
    
    .dropdown {
        margin-left: 5px;
    }
    
    .dropdown-toggle {
        width: 60px;
        height: 28px;
        font-size: 10px;
    }
    
    .dropdown-menu {
        width: 60px;
        font-size: 11px;
    }
    
    .dropdown-item {
        padding: 8px 8px;
    }
    
    .navbar-menu {
        top: 55px;
        padding: 15px;
        gap: 12px;
    }
}

/* Extra Small - 360px and below */
@media (max-width: 360px) {
    .header-nav-bar {
        padding: 0 5px;
    }
    
    .navbar-search-box {
        max-width: 100px;
        min-width: 70px;
        margin: 0 3px;
    }
    
    .header-logo img {
        width: 60px;
        height: 24px;
    }
}

/* RTL Styles */
[dir="rtl"] .header-nav-bar {
    flex-direction: row-reverse;
}

[dir="rtl"] .navbar-search-box {
    margin: 0 10px 0 0;
}

[dir="rtl"] .dropdown-menu {
    left: 0;
    right: auto;
    text-align: right;
}

[dir="rtl"] .dropdown {
    margin-left: 0;
    margin-right: 15px;
}

[dir="rtl"] .navbar-toggle {
    margin-left: 0;
    margin-right: 10px;
}

[dir="rtl"] .navbar-menu {
    text-align: right;
}

[dir="rtl"] .navbar-menu-button,
[dir="rtl"] .navbar-btn1,
[dir="rtl"] .navbar-btn2,
[dir="rtl"] .navbar-btn3 {
    text-align: right;
}

/* RTL Mobile Adjustments */
@media (max-width: 600px) {
    [dir="rtl"] .dropdown {
        margin-right: 8px;
        margin-left: 0;
    }
    
    [dir="rtl"] .navbar-toggle {
        margin-right: 5px;
        margin-left: 0;
    }
    
    [dir="rtl"] .navbar-search-box {
        margin: 0 0 0 8px;
    }
}

@media (max-width: 480px) {
    [dir="rtl"] .dropdown {
        margin-right: 5px;
    }
    
    [dir="rtl"] .navbar-toggle {
        margin-right: 3px;
    }
    
    [dir="rtl"] .navbar-search-box {
        margin: 0 0 0 5px;
    }
}

/* Font family for RTL */
body.ltr {
    font-family: 'Arial', sans-serif;
}

body.rtl {
    font-family: 'Cairo', sans-serif;
}

/* Smooth animations */
* {
    box-sizing: border-box;
}

/* Loading state for better UX */
.header-nav-bar.loading {
    opacity: 0.8;
    pointer-events: none;
}