import React from "react";
import { useTranslation } from "react-i18next";
import blogs from "../Data/Blog";
import './BlogSection.css'

const BlogSection = () => {
    const { t, i18n } = useTranslation();

    return (
        <div className="blog-container">
            <div className="blog-header">
                <div>
                    <h2 className="title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>{t("Blog & Insights")}</h2>
                    <p className="subtitle" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
                        {t("Explore the latest trends, expert opinions and industry innovations from our trusted partners")}
                    </p>
                </div>
                <button className="explore-btn">
                    {t("Explore Blogs")}
                </button>
            </div>
            <div className="blog-grid">
                {blogs.map((blog) => (
                    <div key={blog.id} className="blog-card">
                        <img src={blog.image} alt={t(blog.categoryKey)} className="blog-image" />
                        <div className="blog-content">
                            <p className="blog-category">{t(blog.categoryKey)}</p>
                            <h3 className="blog-heading">
                                {t(blog.titleKey)} <a href={blog.link} className="blog-link">↗</a>
                            </h3>
                            <p className="blog-description">{t(blog.descriptionKey)}</p>
                            <p className="blog-date">
                                {t("Updated on")} <br />
                                <span>{blog.date}</span>
                            </p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default BlogSection;