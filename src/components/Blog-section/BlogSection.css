/* Blog CSS - Responsive Version */

.blog-container {
    padding: 20px 0px;
    max-width: 1400px;
    margin: 0 auto;
}

.blog-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 0px 20px 0px 20px;
    gap: 20px;
}

.title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    text-align: left;
}

.subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
}

[dir="rtl"] .title,
[dir="rtl"] .subtitle {
    margin-right: 40px;
    text-align: right;
}

[dir="ltr"] .title,
[dir="ltr"] .subtitle {
    margin-left: 40px;
    text-align: left;
}

.explore-btn {
    padding: 12px 24px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.explore-btn:hover {
    background-color: #0056b3;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    justify-items: center;
    padding: 0 20px;
}

.blog-card {
    width: 100%;
    max-width: 440px;
    background: white;
    border-radius: 18px;
    box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 6px 6px 6px 6px rgba(0, 0, 0, 0.15);
}

.blog-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.blog-content {
    padding: 15px;
}

.blog-category {
    font-size: 14px;
    color: purple;
    font-weight: bold;
    text-align: left;
}

.blog-heading {
    font-size: 18px;
    font-weight: bold;
    margin-top: 5px;
    text-align: left;
    line-height: 1.4;
}

.blog-link {
    text-decoration: none;
    color: black;
    transition: color 0.3s ease;
}

.blog-link:hover {
    color: purple;
}

.blog-description {
    font-size: 16px;
    color: #555;
    margin-top: 5px;
    text-align: left;
    line-height: 1.5;
}

.blog-date {
    font-size: 15px;
    color: rgb(0, 0, 0);
    margin-top: 10px;
    text-align: left;
}

.blog-date span {
    color: #888;
}

/* Tablet Styles */
@media (max-width: 1024px) {
    .blog-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        padding: 0 15px;
    }
    
    .blog-header {
        margin: 0px 15px;
    }
    
    [dir="rtl"] .title,
    [dir="rtl"] .subtitle {
        margin-right: 20px;
    }
    
    [dir="ltr"] .title,
    [dir="ltr"] .subtitle {
        margin-left: 20px;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .blog-container {
        padding: 15px 0px;
    }
    
    .blog-header {
        flex-direction: column;
        align-items: flex-start;
        margin: 0px 10px;
        gap: 15px;
    }
    
    .title {
        font-size: 20px;
        margin-bottom: 8px;
    }
    
    .subtitle {
        font-size: 13px;
        margin-bottom: 15px;
    }
    
    [dir="rtl"] .title,
    [dir="rtl"] .subtitle {
        margin-right: 10px;
    }
    
    [dir="ltr"] .title,
    [dir="ltr"] .subtitle {
        margin-left: 10px;
    }
    
    .explore-btn {
        width: 100%;
        padding: 14px 24px;
        font-size: 16px;
    }
    
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 10px;
    }
    
    .blog-card {
        max-width: 100%;
    }
    
    .blog-image {
        height: 180px;
    }
    
    .blog-content {
        padding: 12px;
    }
    
    .blog-heading {
        font-size: 16px;
    }
    
    .blog-description {
        font-size: 14px;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .blog-container {
        padding: 10px 0px;
    }
    
    .title {
        font-size: 18px;
    }
    
    .subtitle {
        font-size: 12px;
    }
    
    .blog-grid {
        gap: 15px;
        padding: 0 5px;
    }
    
    .blog-card {
        border-radius: 12px;
    }
    
    .blog-image {
        height: 160px;
    }
    
    .blog-content {
        padding: 10px;
    }
    
    .blog-category {
        font-size: 12px;
    }
    
    .blog-heading {
        font-size: 15px;
    }
    
    .blog-description {
        font-size: 13px;
    }
    
    .blog-date {
        font-size: 13px;
    }
}