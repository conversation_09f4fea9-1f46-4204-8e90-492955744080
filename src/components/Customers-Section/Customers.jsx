import React from 'react';
import { useTranslation } from "react-i18next";
import customer from "../Data/Customers.js";
import './Customers.css';
import { useState } from 'react';

const Customers = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "en", label: "English" },  // Changed to standard language codes
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <div>
            <div className="customer-container">
                <div className="customer-head">
                    <div>
                        <h1 className="title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >{t("Top Customers in the Market")}</h1>
                        <p className="subtitle" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >
                            {t("Our top Customers - valued partners in our journey of growth, success, and innovation - whose trust and loyalty.")}
                        </p>
                    </div>
                    <button className="explore-btn">{t("Explore all Deals")}</button>
                </div>

                <div className="customer-grid">
                    {customer.map((deal) => (
                        <div className="customer-card" key={deal.id}>
                            <div className="customer-header" style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                <span>{t(deal.nameKey)}</span>
                                <img className="customer-logo" src={deal.logo} alt={t(deal.nameKey)} />
                            </div>
                            <div className="customer-body">
                                <p className="industry">{t("Industry")} <br></br> <strong>{t(deal.industryKey)}</strong></p>
                                <div>
                                    <p className="projects">
                                        <span className="projects-count" style={{ fontSize: "28px", color: "#000", textAlign: "right" }}>
                                            {deal.projects}
                                        </span> {t("Projects")}
                                    </p>
                                </div>
                            </div>
                            <hr />
                            <div className="customer-footer">
                                <button className="view-details">{t("View Details")}</button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default Customers;