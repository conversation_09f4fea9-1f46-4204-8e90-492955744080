/* Customers.css - Responsive Version */

.customer-container {
    background-color: #ffffff;
    margin-bottom: 35px;
    padding: 20px 15px;
    max-width: 1400px;
    margin: 0 auto 35px auto;
}

.customer-head {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    background-color: #ffffff;
    gap: 20px;
    margin-bottom: 30px;
    padding: 0 20px;
}

.title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 8px;
    line-height: 1.3;
}

.subtitle {
    font-size: 16px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
}

.explore-btn {
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease, transform 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
    height: fit-content;
}

.explore-btn:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}

.customer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    justify-items: center;
    margin: 0 auto;
    padding: 0 20px;
}

.customer-card {
    width: 100%;
    max-width: 440px;
    min-height: 315px;
    background: white;
    border-radius: 12px;
    box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: left;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.customer-card:hover {
    transform: translateY(-5px);
    box-shadow: 6px 6px 6px 6px rgba(0, 0, 0, 0.15);
}

.customer-header {
    background: #9D3CA20F;
    padding: 20px 25px;
    min-height: 50px;
    font-weight: bold;
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.customer-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.customer-body {
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-grow: 1;
}

.customer-footer {
    display: flex;
    justify-content: flex-end;
    padding: 15px 25px 20px 25px;
    border-top: 1px solid #eee;
}

.industry {
    text-align: left;
    font-size: 14px;
    color: #666;
}

.industry strong {
    color: #333;
    font-size: 16px;
}

.projects {
    text-align: right;
    font-size: 14px;
    color: #666;
}

.projects-count {
    font-size: 28px;
    font-weight: bold;
    color: #000;
    display: block;
    line-height: 1.2;
}

.view-details {
    background-color: #9ca4ae;
    color: black;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-details:hover {
    background-color: #56054e;
    color: white;
    transform: translateY(-1px);
}

/* RTL Support */
[dir="rtl"] .customer-header,
[dir="rtl"] .customer-body {
    direction: rtl;
}

[dir="rtl"] .industry {
    text-align: right;
}

[dir="rtl"] .projects {
    text-align: left;
}

[dir="rtl"] .customer-footer {
    justify-content: flex-start;
}

/* Large Desktop */
@media (min-width: 1400px) {
    .customer-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1400px;
    }
}

/* Desktop */
@media (max-width: 1280px) {
    .customer-grid {
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 25px;
    }
    
    .customer-card {
        max-width: 420px;
    }
}

/* Tablet */
@media (max-width: 1024px) {
    .customer-container {
        padding: 15px 10px;
    }
    
    .customer-head {
        padding: 0 15px;
        gap: 15px;
    }
    
    .title {
        font-size: 24px;
    }
    
    .subtitle {
        font-size: 15px;
    }
    
    .customer-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
        padding: 0 15px;
    }
    
    .customer-card {
        max-width: 400px;
    }
    
    .customer-header {
        font-size: 18px;
        padding: 18px 20px;
    }
    
    .customer-logo {
        width: 55px;
        height: 55px;
    }
}

/* Mobile Large */
@media (max-width: 768px) {
    .customer-container {
        padding: 10px 5px;
        margin-bottom: 25px;
    }
    
    .customer-head {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 0 10px;
        gap: 12px;
    }
    
    .title {
        font-size: 22px;
        text-align: center;
        margin-bottom: 5px;
    }
    
    .subtitle {
        font-size: 14px;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .explore-btn {
        width: 100%;
        max-width: 300px;
        padding: 14px 24px;
        font-size: 16px;
    }
    
    .customer-grid {
        grid-template-columns: 1fr;
        gap: 18px;
        padding: 0 10px;
    }
    
    .customer-card {
        max-width: 100%;
        min-height: 280px;
    }
    
    .customer-header {
        font-size: 17px;
        padding: 15px 18px;
    }
    
    .customer-body {
        padding: 15px 18px;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .projects {
        align-self: flex-end;
    }
    
    .customer-footer {
        padding: 12px 18px 15px 18px;
    }
    
    [dir="rtl"] .title,
    [dir="rtl"] .subtitle {
        text-align: center;
    }
    
    [dir="rtl"] .customer-body {
        align-items: flex-start;
    }
    
    [dir="rtl"] .projects {
        align-self: flex-start;
    }
}

/* Mobile Medium */
@media (max-width: 480px) {
    .customer-container {
        padding: 8px 3px;
    }
    
    .customer-head {
        padding: 0 8px;
        gap: 10px;
    }
    
    .title {
        font-size: 20px;
    }
    
    .subtitle {
        font-size: 13px;
    }
    
    .explore-btn {
        padding: 12px 20px;
        font-size: 15px;
    }
    
    .customer-grid {
        gap: 15px;
        padding: 0 8px;
    }
    
    .customer-card {
        min-height: 260px;
        border-radius: 10px;
    }
    
    .customer-header {
        font-size: 16px;
        padding: 12px 15px;
    }
    
    .customer-logo {
        width: 50px;
        height: 50px;
    }
    
    .customer-body {
        padding: 12px 15px;
        gap: 12px;
    }
    
    .industry {
        font-size: 13px;
    }
    
    .industry strong {
        font-size: 15px;
    }
    
    .projects {
        font-size: 13px;
    }
    
    .projects-count {
        font-size: 24px;
    }
    
    .customer-footer {
        padding: 10px 15px 12px 15px;
    }
    
    .view-details {
        padding: 8px 16px;
        font-size: 13px;
    }
}

/* Mobile Small */
@media (max-width: 360px) {
    .customer-container {
        padding: 5px 2px;
    }
    
    .customer-head {
        padding: 0 5px;
    }
    
    .title {
        font-size: 18px;
    }
    
    .subtitle {
        font-size: 12px;
    }
    
    .customer-grid {
        padding: 0 5px;
        gap: 12px;
    }
    
    .customer-card {
        min-height: 240px;
    }
    
    .customer-header {
        font-size: 15px;
        padding: 10px 12px;
    }
    
    .customer-logo {
        width: 45px;
        height: 45px;
    }
    
    .customer-body {
        padding: 10px 12px;
    }
    
    .projects-count {
        font-size: 22px;
    }
    
    .customer-footer {
        padding: 8px 12px 10px 12px;
    }
}