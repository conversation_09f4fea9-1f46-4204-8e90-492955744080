/* Layout container */
.layout-container {
  display: flex;
  min-height: 100vh;
}

/* Main content container with dynamic margin */
.main-content {
  flex: 1;
  transition: margin-left 0.3s ease;
}

/* Adjust margin based on sidebar state */
.main-content.sidebar-expanded {
  margin-left: 200px; /* Adjust this value to match your sidebar width */
}

.main-content.sidebar-collapsed {
  margin-left: 80px; /* Adjust this value to match your collapsed sidebar width */
}

.customer-management {
  padding: 24px;
  background: #ffffff;
}

/* Search bar styling */
.search-bar {
  margin-bottom: 24px;
  text-align: center;
}

.search-input-large {
  width: 100%;
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header section styling */
.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.product-list-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.header-buttons {
  display: flex;
  gap: 12px;
}

/* Export Button - White background with purple text */
.export-button {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  background-color: white;
  color: #9e3ca2;
  border: 1px solid #9e3ca2;
}

.export-button:hover {
  background-color: #f8f4f9;
  color: #9e3ca2;
  border-color: #9e3ca2;
}

/* Compare Button - White background with green text */
.compare-button {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  background-color: white;
  color: #22c55e;
  border: 1px solid #22c55e;
}

.compare-button:hover {
  background-color: #f0fdf4;
  color: #22c55e;
  border-color: #22c55e;
}

/* Create Order Button - Purple background with white text */
.create-button {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #9e3ca2;
  color: white;
  border: none;
}

.create-button:hover {
  background-color: #8b2f92;
  color: white;
  border: none;
}

/* Table styling */
.customer-table {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-table-thead > tr > th {
  background: #f8f9fa;
  color: #1e293b;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f1f5f9;
}

.ant-rate {
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .main-content.sidebar-expanded,
  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
  
  .search-input-large {
    width: 100%;
  }

  .product-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-buttons {
    width: 100%;
    justify-content: space-between;
  }
}