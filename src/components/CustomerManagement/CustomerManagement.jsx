import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Space, Rate, message } from 'antd';
import { SearchOutlined, ExportOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './CustomerManagement.css';
import Header from '../Header-Section/Header';
import { useNavigate } from 'react-router-dom';
import CustomSider from '../../components/Pages/CustomSider';
import { productsApiClient } from '../../services/apiService';
import { useApi } from '../../hooks/useApi';

const CustomerManagement = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [collapsed, setCollapsed] = useState(false);
  const [products, setProducts] = useState([]);

  // Use the new API hook for better error handling and loading states
  const { loading, error, execute } = useApi({
    showErrorMessage: true,
    errorContext: 'Products API'
  });

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await execute(() =>
          productsApiClient.get('/with-vendors')
        );

        // Transform backend data to match frontend structure
        const transformedData = response.map(item => ({
          id: item.product_id,
          service: item.name,
          price: `SAR ${item.price}`,
          ratings: Math.round(item.vendor.rating),
          category: JSON.parse(item.tags)[0],
          vendor: item.vendor.name
        }));

        setProducts(transformedData);
      } catch (error) {
        console.error('Error fetching products:', error);
        // Error is already handled by the useApi hook
      }
    };

    fetchProducts();
  }, [execute]);

  const columns = [
    {
      title: t('Service'),
      dataIndex: 'service',
      key: 'service',
      filteredValue: [searchText],
      onFilter: (value, record) => {
        return String(t(record.service))
          .toLowerCase()
          .includes(value.toLowerCase());
      },
      render: (text) => t(text),
    },
    {
      title: t('Price'),
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: t('Ratings'),
      dataIndex: 'ratings',
      key: 'ratings',
      render: (rating) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: t('Category'),
      dataIndex: 'category',
      key: 'category',
      render: (text) => t(text),
    },
    {
      title: t('Vendor'),
      dataIndex: 'vendor',
      key: 'vendor',
      render: (text) => t(text),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleSearch = (e) => {
    setSearchText(e.target.value);
  };

  // Update the Create Order button click handler
  const handleCreateOrder = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Please select a service first');
      return;
    }
    const selectedServices = products.filter(item => selectedRowKeys.includes(item.id));
    navigate('/createOrder', { state: { selectedServices: selectedServices } });
  };

  return (
    <>
      <Header />
      <div className="layout-container">
        <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
        
        <div className={`main-content ${collapsed ? 'sidebar-collapsed' : 'sidebar-expanded'}`}>
          <div className="customer-management">
            {/* Search Bar */}
            <div className="search-bar">
              <Input
                placeholder={t('Search for a product or service...')}
                prefix={<SearchOutlined />}
                onChange={handleSearch}
                className="search-input-large"
              />
            </div>

            {/* Header Section */}
            <div className="product-list-header">
              <h2 className="product-list-title">{t('Product List')}</h2>
              <Space className="header-buttons">
                <Button icon={<ExportOutlined />} className="export-button">
                  {t('Export')}
                </Button>
                <Button className="compare-button">{t('Compare')}</Button>
                <Button 
                  icon={<PlusOutlined />} 
                  className="create-button" 
                  onClick={handleCreateOrder}
                >
                  {t('Create Order')}
                </Button>
              </Space>
            </div>

            {/* Table Section */}
            {loading ? (
              <div className="loading-state">{t('Loading products...')}</div>
            ) : error ? (
              <div className="error-state">{t('Error loading products')}: {error}</div>
            ) : products.length === 0 ? (
              <div className="empty-state">{t('No products available')}</div>
            ) : (
              <Table
                rowSelection={rowSelection}
                columns={columns}
                dataSource={products}
                className="customer-table"
                rowKey="id"
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CustomerManagement;