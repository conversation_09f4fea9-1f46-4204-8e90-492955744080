.main-layout {
  padding: 10px;
  max-width: 1400px;
  margin: 0 auto;
  /* Add left margin to account for sidebar */
  margin-left: 220px;
  transition: margin-left 0.3s ease;
}

/* When sidebar is collapsed, reduce the left margin */
.main-layout.sidebar-collapsed {
  margin-left: 80px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.breadcrumb {
  font-size: 12px;
  font-weight: 500;
}

.breadcrumb-purple {
  color: #9e3ca2;
}

.breadcrumb-black {
  color: #1e293b;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.products-selected {
  font-size: 12px;
  color: #64748b;
}

.submit-btn {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #8b2d91;
  border-color: #8b2d91;
}

/* Two Column Layout - Compact */
.content-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 15px;
  align-items: start;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

/* Compact Product Box */
.product-box {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  min-height: auto;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.starting-price {
  font-size: 11px;
  color: #9e3ca2;
  font-weight: 600;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.product-details {
  margin-bottom: 8px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 0;
  border-bottom: 1px solid #f1f5f9;
}

.price-row:last-child {
  border-bottom: none;
}

.label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.value {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f1f5f9;
}

.vendor-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.vendor-name {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
}

.rating {
  display: flex;
  gap: 1px;
}

.star {
  color: #d1d5db;
  font-size: 10px;
}

.star.filled {
  color: #fbbf24;
}

/* Right Column - Compact */
.right-column {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.create-order-header {
  margin-bottom: 10px;
}

.order-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.submit-btn-large {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn-large:hover {
  background-color: #8b2d91;
  border-color: #8b2d91;
}

/* Compact Order Containers */
.create-order-container {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
}

.create-order-container:last-child {
  margin-bottom: 0;
}

.selected-products {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.product-card {
  border: none;
  border-radius: 0;
  padding: 0;
  background-color: transparent;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.service-title {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

/* Compact Price Details */
.price-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 0;
  border: none;
  margin-bottom: 12px;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.currency {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  min-width: 20px;
}

.currency.highlight {
  color: #9e3ca2;
}

.price-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  background-color: white;
}

.price-input.highlight {
  color: #9e3ca2;
  border-color: #9e3ca2;
}

/* Locked starting price input styling */
.price-input.locked {
  background-color: #f3f0ff;
  border-color: #c4b5fd;
  color: #6b46c1;
  cursor: not-allowed;
}

.price-input.locked:focus {
  outline: none;
  border-color: #c4b5fd;
  box-shadow: 0 0 0 1px #c4b5fd;
}

/* Compact Date Section */
.date-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.date-picker {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 11px;
  background-color: white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 16px;
  color: #1e293b;
  font-weight: 600;
  margin: 0;
  padding-bottom: 12px;
}

.modal-body {
  padding: 16px;
}

.modal-body p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.order-summary {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
}

.service-summary {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.service-summary:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.service-summary-title {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
}

.summary-value.highlight {
  color: #9e3ca2;
}

.modal-footer {
  padding: 0 16px 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.btn-cancel {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #64748b;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background-color: #f8fafc;
  border-color: #9ca3af;
}

.btn-confirm {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-confirm:hover {
  background-color: #8b2d91;
  border-color: #8b2d91;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .main-layout {
    margin-left: 0;
  }
  
  .content-layout {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .price-details,
  .date-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    padding: 8px;
    margin-left: 0;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .create-order-container {
    padding: 8px;
  }

  .modal-content {
    margin: 16px;
    width: calc(100% - 32px);
  }
}