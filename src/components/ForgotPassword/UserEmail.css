/* Reuse the same styles as SignInPage */
.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.auth-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
}

.auth-row {
    display: flex;
    width: 100%;
    max-width: 1000px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.auth-left, .auth-right {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-left {
    background-size: cover;
    background-position: center;
    color: white;
    position: relative;
}

.auth-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
}

.welcome-box {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 20px;
}

.welcome-box h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.welcome-box p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.auth-right {
    background: #fff;
}

.auth-heading {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 2rem;
}

.auth-form {
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
}

.login-link {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

/* Responsive styles */
@media (max-width: 768px) {
    .auth-row {
        flex-direction: column;
    }
    
    .auth-left, .auth-right {
        padding: 30px 20px;
    }
    
    .auth-left {
        padding: 60px 20px;
    }
}

/* Form item spacing */
.ant-form-item {
    margin-bottom: 24px;
}

/* Button styles */
.ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 4px;
}

/* Input styles */
.ant-input {
    height: 48px;
    border-radius: 4px;
    font-size: 16px;
}

.ant-form-item-label > label {
    font-size: 16px;
    color: #333;
}
