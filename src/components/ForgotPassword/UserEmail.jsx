import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Form, Input, Button, Spin, message } from "antd";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import backgroundImage from "../../assets/Image.png";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";
import "./UserEmail.css";

const UserEmail = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (values) => {
        setLoading(true);
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_APP_AUTH_BASE_URL}/forgot-password`,
                { email: values.email },
                { headers: { "Content-Type": "application/json" } }
            );
            
            message.success(t("Password reset link has been sent to your email"));
            navigate("/signin");
        } catch (error) {
            console.error("Error sending reset email:", error);
            message.error(
                error.response?.data?.message || 
                t("Failed to send reset email. Please try again.")
            );
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Header />
            <div className="page-wrapper">
                <div className="auth-container">
                    <div className="auth-row" dir={i18n.language === "ar" ? "rtl" : "ltr"}>
                        <div className="auth-left" style={{ backgroundImage: `url(${backgroundImage})` }}>
                            <div className="welcome-box">
                                <h2>{t("Reset Password")}</h2>
                                <p>{t("Enter your email address to receive a password reset link")}</p>
                            </div>
                        </div>

                        <div className="auth-right">
                            <h2 className="auth-heading">{t("Forgot Password")}</h2>

                            <Form
                                form={form}
                                layout="vertical"
                                onFinish={handleSubmit}
                                className="auth-form"
                                size="large"
                            >
                                <Form.Item
                                    name="email"
                                    label={t("Email")}
                                    rules={[
                                        { required: true, message: t("Email is required") },
                                        { type: "email", message: t("Please enter a valid email address") },
                                    ]}
                                >
                                    <Input placeholder={t("Enter your email")} disabled={loading} />
                                </Form.Item>

                                <Form.Item>
                                    <Button 
                                        type="primary" 
                                        htmlType="submit" 
                                        block 
                                        loading={loading} 
                                        style={{ marginTop: "20px" }}
                                    >
                                        {loading ? <Spin /> : t("Send Reset Link")}
                                    </Button>
                                </Form.Item>

                                <p className="login-link">
                                    {t("Remember your password?")}{" "}
                                    <span 
                                        onClick={() => navigate("/signin")} 
                                        style={{ cursor: "pointer", color: "#007bff" }}
                                    >
                                        {t("Sign In")}
                                    </span>
                                </p>
                            </Form>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default UserEmail;