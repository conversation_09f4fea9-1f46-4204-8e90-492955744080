.subscription-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.subscription-title {
  text-align: center;
  margin-bottom: 48px;
}

.subscription-title h1 {
  color: #1e293b;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.subscription-title p {
  color: #64748b;
  font-size: 1.1rem;
}

.plans-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 20px;
}

.plan-card {
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
  border: 2px solid transparent;
  background: #ffffff;
}

.plan-card.popular {
  border-color: #dfdfdf;
  transform: scale(1.02);
}

.plan-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.plan-card .ant-card-head {
  border-bottom: 1px solid #e2e8f0;
}

/* Add these new styles to remove blue underline */
.plan-card .ant-card-head-title .ant-ribbon-text {
  text-decoration: none;  /* Removes underline */
}

.plan-card .ant-ribbon {
  text-decoration: none;  /* Ensures no underline on ribbon */
  border: none;  /* Removes any borders */
}

/* Update popular card header styles */
.plan-card.popular .ant-card-head {
  background-color: #9e3ca2;
  text-decoration: none;
      

}

.plan-card.popular .ant-card-head-title {
  color: #ffffff;
  text-decoration: none;
}

.plan-price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 16px 0;
}

.plan-price .currency {
  font-size: 1.5rem;
  font-weight: 500;
  color: #64748b;
}

.plan-price .period {
  font-size: 1rem;
  color: #64748b;
  font-weight: normal;
}

.feature-list {
  margin: 24px 0;
  padding: 0;
  list-style: none;
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #475569;
  font-size: 0.95rem;
}

.feature-list li .anticon {
  color: #020202;
  margin-right: 8px;
}

.select-plan-button {
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  width: 100%;
  border: 2px solid #9e3ca2 !important; /* Add consistent border */
  color: #9e3ca2;
  background: transparent;
}

.select-plan-button:hover {
  background-color: #9e3ca2;
  color: white;
  border-color: #9e3ca2 !important;
}

.select-plan-button.popular {
  background-color: #9e3ca2;
  color: white;
  border-color: #9e3ca2 !important;
}

.select-plan-button.popular:hover {
  background-color: #8a2f8d;
  border-color: #8a2f8d !important;
}

/* Remove any Ant Design default focus styles */
.select-plan-button:focus {
  outline: none;
  border-color: #9e3ca2 !important;
}

@media (max-width: 768px) {
  .plans-container {
    grid-template-columns: 1fr;
  }
  
  .subscription-title h1 {
    font-size: 2rem;
  }
}