import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, Modal, message } from 'antd';
import { CheckOutlined, CrownOutlined, RocketOutlined, TeamOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './Subscription.css';
import { plans } from './planData';
import Header from '../Header-Section/Header';

const Subscription = () => {
  const { t } = useTranslation();
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);
    setIsModalVisible(true);
  };

  const handleConfirm = async () => {
    try {
      // Add your payment integration logic here
      message.success(`Successfully subscribed to ${selectedPlan.title} plan!`);
      setIsModalVisible(false);
    } catch (error) {
      message.error('Subscription failed. Please try again.');
    }
  };

  return (
    <>
                <Header />
    <div className="subscription-container">
      <div className="subscription-title">
        <h1>{t('Choose Your Plan')}</h1>
        <p>{t('Select the perfect plan for your business needs')}</p>
      </div>

      <div className="plans-container">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className={`plan-card ${plan.isPopular ? 'popular' : ''}`}
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {plan.icon}
                {t(plan.title)}
               
              </div>
            }
          >
            <div className="plan-price">
              <span className="currency">SAR </span>
              {plan.price}
              <span className="period">{plan.period}</span>
            </div>
            
            <ul className="feature-list">
              {plan.features.map((feature, index) => (
                <li key={index}>
                  <CheckOutlined /> {t(feature)}
                </li>
              ))}
            </ul>

            <Button
              type={plan.isPopular ? 'primary' : 'default'}
              className={`select-plan-button ${plan.isPopular ? 'popular' : ''}`}
              onClick={() => handleSelectPlan(plan)}
            >
              {t('Select Plan')}
            </Button>
          </Card>
        ))}
      </div>

      <Modal
        title={t('Confirm Subscription')}
        open={isModalVisible}
        onOk={handleConfirm}
        onCancel={() => setIsModalVisible(false)}
        okText={t('Confirm')}
        cancelText={t('Cancel')}
      >
        <p>{t(`Are you sure you want to subscribe to the ${selectedPlan?.title} plan?`)}</p>
        <p>{t(`You will be charged $${selectedPlan?.price} per month.`)}</p>
      </Modal>
    </div>
        </>

  );
};

export default Subscription;