import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Card, Typography, Divider, Button } from 'antd';

const { Title, Text, Paragraph } = Typography;

const ReduxDebugger = () => {
  const auth = useSelector((state) => state.auth);
  
  useEffect(() => {
    console.log("ReduxDebugger mounted, auth state:", auth);
  }, [auth]);

  const checkLocalStorage = () => {
    console.log("Checking localStorage:");
    console.log("- accessToken:", localStorage.getItem("accessToken") ? "exists" : "missing");
    console.log("- refreshToken:", localStorage.getItem("refreshToken") ? "exists" : "missing");
    
    const userJson = localStorage.getItem("user");
    console.log("- user JSON:", userJson);
    
    try {
      if (userJson) {
        const userData = JSON.parse(userJson);
        console.log("- parsed user data:", userData);
      } else {
        console.log("- user data is null or empty");
      }
    } catch (error) {
      console.error("- error parsing user data:", error);
    }
  };

  const clearLocalStorage = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    localStorage.removeItem("profileComplete");
    console.log("LocalStorage cleared");
    window.location.reload();
  };

  return (
    <Card title="Redux State Debugger" style={{ margin: '20px', maxWidth: '800px' }}>
      <Title level={4}>Auth State</Title>
      <Paragraph>
        <Text strong>Is Authenticated:</Text> {auth.isAuthenticated ? 'Yes' : 'No'}
      </Paragraph>
      <Paragraph>
        <Text strong>Profile Complete:</Text> {auth.profileComplete ? 'Yes' : 'No'}
      </Paragraph>
      <Paragraph>
        <Text strong>Access Token:</Text> {auth.accessToken ? 'Present' : 'Missing'}
      </Paragraph>
      <Paragraph>
        <Text strong>Refresh Token:</Text> {auth.refreshToken ? 'Present' : 'Missing'}
      </Paragraph>
      
      <Divider />
      
      <Title level={4}>User Data</Title>
      {auth.user ? (
        <div>
          <Paragraph>
            <Text strong>User ID:</Text> {auth.user.sub || 'N/A'}
          </Paragraph>
          <Paragraph>
            <Text strong>Username:</Text> {auth.user.preferred_username || 'N/A'}
          </Paragraph>
          <Paragraph>
            <Text strong>Email:</Text> {auth.user.email || 'N/A'}
          </Paragraph>
          <Paragraph>
            <Text strong>Roles:</Text> {auth.user.realm_access?.roles?.join(', ') || 'N/A'}
          </Paragraph>
          <Paragraph>
            <Text strong>Full User Object:</Text>
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto', maxHeight: '200px' }}>
            {JSON.stringify(auth.user, null, 2)}
          </pre>
        </div>
      ) : (
        <Paragraph>User data is null or undefined</Paragraph>
      )}
      
      <Divider />
      
      <div style={{ display: 'flex', gap: '10px' }}>
        <Button type="primary" onClick={checkLocalStorage}>
          Check LocalStorage
        </Button>
        <Button type="danger" onClick={clearLocalStorage}>
          Clear LocalStorage
        </Button>
      </div>
    </Card>
  );
};

export default ReduxDebugger;
