/**
 * Token Debugger Component
 * Displays current token information and provides testing utilities
 * Useful for development and debugging token management
 */

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Card, Descriptions, Button, Space, Tag, Alert, Divider } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { 
  getAccessToken, 
  getRefreshToken, 
  getUser,
  isAuthenticated,
  isTokenExpired,
  isTokenExpiringSoon,
  shouldShowExpirationWarning,
  getTokenExpiration,
  getTimeUntilExpiration
} from '../../services/tokenService';
import { useApi } from '../../hooks/useApi';
import { productsApiClient } from '../../services/apiService';

const TokenDebugger = () => {
  const auth = useSelector((state) => state.auth);
  const [tokenInfo, setTokenInfo] = useState({});
  const [refreshInterval, setRefreshInterval] = useState(null);
  
  const { loading, error, execute } = useApi({
    showErrorMessage: true,
    errorContext: 'Token Test API'
  });

  const updateTokenInfo = () => {
    const accessToken = getAccessToken();
    const refreshToken = getRefreshToken();
    const user = getUser();
    
    setTokenInfo({
      accessToken: accessToken ? `${accessToken.substring(0, 20)}...` : null,
      refreshToken: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
      user: user,
      isAuthenticated: isAuthenticated(),
      isTokenExpired: accessToken ? isTokenExpired(accessToken) : null,
      isTokenExpiringSoon: isTokenExpiringSoon(),
      shouldShowWarning: shouldShowExpirationWarning(),
      tokenExpiration: accessToken ? getTokenExpiration(accessToken) : null,
      timeUntilExpiration: accessToken ? getTimeUntilExpiration(accessToken) : null
    });
  };

  useEffect(() => {
    updateTokenInfo();
    
    // Update token info every 30 seconds
    const interval = setInterval(updateTokenInfo, 30000);
    setRefreshInterval(interval);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [auth]);

  const testApiCall = async () => {
    try {
      const response = await execute(() => 
        productsApiClient.get('/with-vendors')
      );
      console.log('API test successful:', response);
    } catch (error) {
      console.error('API test failed:', error);
    }
  };

  const getTokenStatus = () => {
    if (!tokenInfo.accessToken) {
      return { status: 'error', text: 'No Token', icon: <CloseCircleOutlined /> };
    }
    if (tokenInfo.isTokenExpired) {
      return { status: 'error', text: 'Expired', icon: <CloseCircleOutlined /> };
    }
    if (tokenInfo.isTokenExpiringSoon) {
      return { status: 'warning', text: 'Expiring Soon', icon: <ExclamationCircleOutlined /> };
    }
    return { status: 'success', text: 'Valid', icon: <CheckCircleOutlined /> };
  };

  const tokenStatus = getTokenStatus();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="Token Management Debugger" extra={
        <Button 
          icon={<ReloadOutlined />} 
          onClick={updateTokenInfo}
          size="small"
        >
          Refresh
        </Button>
      }>
        
        {/* Token Status Overview */}
        <Alert
          message={`Token Status: ${tokenStatus.text}`}
          type={tokenStatus.status}
          icon={tokenStatus.icon}
          showIcon
          style={{ marginBottom: '16px' }}
        />

        {/* Redux State */}
        <Descriptions title="Redux Auth State" bordered size="small" column={1}>
          <Descriptions.Item label="Is Authenticated">
            <Tag color={auth.isAuthenticated ? 'green' : 'red'}>
              {auth.isAuthenticated ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Is Refreshing">
            <Tag color={auth.isRefreshing ? 'blue' : 'default'}>
              {auth.isRefreshing ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Refresh Error">
            {auth.refreshError ? (
              <Tag color="red">{auth.refreshError}</Tag>
            ) : (
              <Tag color="green">None</Tag>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Profile Complete">
            <Tag color={auth.profileComplete ? 'green' : 'orange'}>
              {auth.profileComplete ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        {/* Token Details */}
        <Descriptions title="Token Information" bordered size="small" column={1}>
          <Descriptions.Item label="Access Token">
            {tokenInfo.accessToken || 'Not available'}
          </Descriptions.Item>
          <Descriptions.Item label="Refresh Token">
            {tokenInfo.refreshToken || 'Not available'}
          </Descriptions.Item>
          <Descriptions.Item label="Token Expired">
            <Tag color={tokenInfo.isTokenExpired ? 'red' : 'green'}>
              {tokenInfo.isTokenExpired ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Expiring Soon">
            <Tag color={tokenInfo.isTokenExpiringSoon ? 'orange' : 'green'}>
              {tokenInfo.isTokenExpiringSoon ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Should Show Warning">
            <Tag color={tokenInfo.shouldShowWarning ? 'orange' : 'green'}>
              {tokenInfo.shouldShowWarning ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Token Expiration">
            {tokenInfo.tokenExpiration ? 
              tokenInfo.tokenExpiration.toLocaleString() : 
              'Not available'
            }
          </Descriptions.Item>
          <Descriptions.Item label="Time Until Expiration">
            {tokenInfo.timeUntilExpiration !== null ? 
              `${tokenInfo.timeUntilExpiration} minutes` : 
              'Not available'
            }
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        {/* User Information */}
        {tokenInfo.user && (
          <>
            <Descriptions title="User Information" bordered size="small" column={1}>
              <Descriptions.Item label="User ID">
                {tokenInfo.user.sub || 'Not available'}
              </Descriptions.Item>
              <Descriptions.Item label="Email">
                {tokenInfo.user.email || 'Not available'}
              </Descriptions.Item>
              <Descriptions.Item label="Roles">
                {tokenInfo.user.realm_access?.roles ? 
                  tokenInfo.user.realm_access.roles.map(role => (
                    <Tag key={role} color="blue">{role}</Tag>
                  )) : 
                  'Not available'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Token Issued At">
                {tokenInfo.user.iat ? 
                  new Date(tokenInfo.user.iat * 1000).toLocaleString() : 
                  'Not available'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Token Expires At">
                {tokenInfo.user.exp ? 
                  new Date(tokenInfo.user.exp * 1000).toLocaleString() : 
                  'Not available'
                }
              </Descriptions.Item>
            </Descriptions>
            <Divider />
          </>
        )}

        {/* Test Actions */}
        <Space>
          <Button 
            type="primary" 
            onClick={testApiCall}
            loading={loading}
            disabled={!tokenInfo.isAuthenticated}
          >
            Test API Call
          </Button>
          <Button onClick={updateTokenInfo}>
            Refresh Token Info
          </Button>
        </Space>

        {error && (
          <Alert
            message="API Test Error"
            description={error}
            type="error"
            style={{ marginTop: '16px' }}
            showIcon
          />
        )}

        <Alert
          message="Debug Information"
          description="This component is for development purposes only. It displays sensitive token information and should not be used in production."
          type="info"
          icon={<InfoCircleOutlined />}
          style={{ marginTop: '16px' }}
          showIcon
        />
      </Card>
    </div>
  );
};

export default TokenDebugger;
