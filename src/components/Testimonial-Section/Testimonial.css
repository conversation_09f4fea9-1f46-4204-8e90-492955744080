/* Testimonial CSS - Responsive Version */

.container5 {
    text-align: center;
    padding: 50px 0px;
    position: relative;
    max-width: 1400px;
    margin: 0 auto;
}

.header-container5 {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 0px 40px 40px 40px;
    gap: 20px;
}

.heading5 {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
    text-align: start;
}

.subtext5 {
    font-size: 16px;
    color: #7d6e6e;
    margin-bottom: 20px;
    text-align: start;
}

.carousel5 {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 0 20px;
}

.card5 {
    width: 400px;
    min-height: 280px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: start;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.card5:hover {
    transform: translateY(-5px);
    box-shadow: 6px 6px 6px 6px rgba(0, 0, 0, 0.15);
}

.header5 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.logo5 {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.stars5 {
    color: gold;
    font-size: 18px;
}

.name5 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.role5 {
    font-size: 14px;
    color: gray;
    margin-bottom: 15px;
}

.text5 {
    font-size: 14px;
    line-height: 1.6;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.buttons5 {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.button5 {
    background-color: rgb(41, 5, 40);
    width: 50px;
    height: 50px;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 8px;
    font-size: 20px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.button5:hover:not(:disabled) {
    background-color: #5a1259;
    transform: scale(1.05);
}

.button5:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.button5:active {
    transform: scale(0.95);
}

/* RTL Support */
[dir="rtl"] .heading5,
[dir="rtl"] .subtext5 {
    text-align: end;
}

[dir="rtl"] .card5 {
    text-align: end;
}

[dir="rtl"] .button5 {
    transform: scaleX(-1);
}

/* Large Desktop */
@media (min-width: 1200px) {
    .carousel5 {
        gap: 40px;
    }
}

/* Tablet Styles */
@media (max-width: 1024px) {
    .container5 {
        padding: 40px 0px;
    }
    
    .header-container5 {
        margin: 0px 30px 30px 30px;
    }
    
    .heading5 {
        font-size: 28px;
    }
    
    .carousel5 {
        gap: 25px;
        padding: 0 15px;
    }
    
    .card5 {
        width: 350px;
        min-height: 260px;
    }
}

/* Mobile Landscape / Small Tablet */
@media (max-width: 768px) {
    .container5 {
        padding: 30px 0px;
    }
    
    .header-container5 {
        flex-direction: column;
        align-items: center;
        margin: 0px 20px 25px 20px;
        gap: 15px;
        text-align: center;
    }
    
    .heading5 {
        font-size: 24px;
        text-align: center;
        margin-bottom: 8px;
    }
    
    .subtext5 {
        font-size: 15px;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .carousel5 {
        gap: 20px;
        padding: 0 10px;
    }
    
    .card5 {
        width: 100%;
        max-width: 400px;
        min-height: 240px;
        padding: 18px;
    }
    
    .buttons5 {
        order: -1;
    }
    
    .button5 {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    [dir="rtl"] .heading5,
    [dir="rtl"] .subtext5 {
        text-align: center;
    }
}

/* Mobile Portrait */
@media (max-width: 480px) {
    .container5 {
        padding: 25px 0px;
    }
    
    .header-container5 {
        margin: 0px 15px 20px 15px;
        gap: 12px;
    }
    
    .heading5 {
        font-size: 22px;
    }
    
    .subtext5 {
        font-size: 14px;
    }
    
    .carousel5 {
        gap: 15px;
        padding: 0 8px;
    }
    
    .card5 {
        width: 100%;
        max-width: none;
        min-height: 220px;
        padding: 15px;
        border-radius: 8px;
    }
    
    .header5 {
        margin-bottom: 12px;
    }
    
    .logo5 {
        width: 45px;
        height: 45px;
    }
    
    .stars5 {
        font-size: 16px;
    }
    
    .name5 {
        font-size: 16px;
    }
    
    .role5 {
        font-size: 13px;
        margin-bottom: 12px;
    }
    
    .text5 {
        font-size: 13px;
        line-height: 1.5;
    }
    
    .button5 {
        width: 40px;
        height: 40px;
        font-size: 16px;
        border-radius: 6px;
    }
}

/* Dots Indicator */
.dots-container5 {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.dot5 {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background-color: #ddd;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.dot5:hover {
    background-color: #bbb;
    transform: scale(1.1);
}

.dot5.active {
    background-color: rgb(41, 5, 40);
}

/* Very Small Screens */
@media (max-width: 360px) {
    .container5 {
        padding: 20px 0px;
    }
    
    .header-container5 {
        margin: 0px 10px 15px 10px;
    }
    
    .heading5 {
        font-size: 20px;
    }
    
    .carousel5 {
        padding: 0 5px;
    }
    
    .card5 {
        padding: 12px;
        min-height: 200px;
    }
    
    .name5 {
        font-size: 15px;
    }
    
    .text5 {
        font-size: 12px;
    }
    
    .dots-container5 {
        margin-top: 20px;
    }
    
    .dot5 {
        width: 10px;
        height: 10px;
    }
}