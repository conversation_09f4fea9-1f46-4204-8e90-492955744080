import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import testimonials from "../Data/Testimonial.js";
import './Testimonial.css';

const Testimonial = () => {
    const { t, i18n } = useTranslation();
    const [startIndex, setStartIndex] = useState(0);
    const [itemsPerView, setItemsPerView] = useState(3);

    // Update items per view based on screen size
    useEffect(() => {
        const updateItemsPerView = () => {
            if (window.innerWidth <= 480) {
                setItemsPerView(1);
            } else if (window.innerWidth <= 768) {
                setItemsPerView(1);
            } else if (window.innerWidth <= 1024) {
                setItemsPerView(2);
            } else {
                setItemsPerView(3);
            }
        };

        updateItemsPerView();
        window.addEventListener('resize', updateItemsPerView);
        return () => window.removeEventListener('resize', updateItemsPerView);
    }, []);

    // Reset startIndex when itemsPerView changes to prevent showing empty spaces
    useEffect(() => {
        if (startIndex > testimonials.length - itemsPerView) {
            setStartIndex(Math.max(0, testimonials.length - itemsPerView));
        }
    }, [itemsPerView, startIndex]);

    const nextSlide = () => {
        if (startIndex < testimonials.length - itemsPerView) {
            setStartIndex(startIndex + 1);
        }
    };

    const prevSlide = () => {
        if (startIndex > 0) {
            setStartIndex(startIndex - 1);
        }
    };

    const maxStartIndex = Math.max(0, testimonials.length - itemsPerView);

    return (
        <div className="container5" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
            <div className="header-container5">
                <div>
                    <h2 className="heading5">{t("Testimonial")}</h2>
                    <p className="subtext5">{t("What Our Clients Say!")}</p>
                </div>
                {testimonials.length > itemsPerView && (
                    <div className="buttons5">
                        <button 
                            className="button5" 
                            onClick={prevSlide} 
                            disabled={startIndex === 0}
                            aria-label="Previous testimonials"
                        >
                            ←
                        </button>
                        <button 
                            className="button5" 
                            onClick={nextSlide} 
                            disabled={startIndex >= maxStartIndex}
                            aria-label="Next testimonials"
                        >
                            →
                        </button>
                    </div>
                )}
            </div>
            <div className="carousel5">
                {testimonials.slice(startIndex, startIndex + itemsPerView).map((testimonial) => (
                    <div key={testimonial.id} className="card5">
                        <div className="header5">
                            <img 
                                src={testimonial.logo} 
                                alt={t(testimonial.nameKey)} 
                                className="logo5" 
                            />
                            <div className="stars5" aria-label={`${testimonial.rating} stars`}>
                                {"⭐".repeat(testimonial.rating)}
                            </div>
                        </div>
                        <p className="name5">{t(testimonial.nameKey)}</p>
                        <p className="role5">— {t(testimonial.roleKey)}</p>
                        <p className="text5">"{t(testimonial.textKey)}"</p>
                    </div>
                ))}
            </div>
            
            {/* Optional: Add dots indicator for mobile */}
            {itemsPerView === 1 && testimonials.length > 1 && (
                <div className="dots-container5">
                    {Array.from({ length: testimonials.length }, (_, i) => (
                        <button
                            key={i}
                            className={`dot5 ${i === startIndex ? 'active' : ''}`}
                            onClick={() => setStartIndex(i)}
                            aria-label={`Go to testimonial ${i + 1}`}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default Testimonial;