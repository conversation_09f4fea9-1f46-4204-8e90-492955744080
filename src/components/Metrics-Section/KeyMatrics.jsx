import React from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import metrics from "../Data/KeyMatrices.js";
import './KeyMatrics.css';

const MetricsSection = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "en", label: "English" }, // Standardized language codes
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <div className="metrics-container">
            <h2 className="metrics-title" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >{t("Key Metrics & Statistics")}</h2>
            <p className="metrics-subtext" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} >
                {t("Measuring our impact through data-driven results and proven performance indicators.")}
            </p>
            <div className="metrics-grid">
                {metrics.map((metric) => (
                    <div key={metric.id} className="metric-card">
                        <div className="metric-header">
                            <h3>{t(metric.titleKey)}</h3>
                            <span className="metric-icon">{metric.icon}</span>
                        </div>
                        <div className="metric-value">
                            <strong>{metric.value}</strong>
                            <span className={`change ${metric.changeType}`}>
                                {metric.changeType === "positive" ? "▲" : "▼"} {metric.change}
                            </span>
                        </div>
                        <p className="metric-subtext">{t("vs. previous month")}</p>
                        <div className="progress-bar">
                            <div
                                className="progress-fill"
                                style={{ width: `${metric.progress}%` }}
                                role="progressbar"
                                aria-valuenow={metric.progress}
                                aria-valuemin="0"
                                aria-valuemax="100"
                            ></div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default MetricsSection;