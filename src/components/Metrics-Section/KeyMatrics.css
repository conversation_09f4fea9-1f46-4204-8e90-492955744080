/* Key Matrics CSS */

.metrics-container {
    padding: 50px 20px;
    text-align: center;
}

.metrics-title {
    font-size: 32px;
    font-weight: bold;
    text-align: left;
    margin-left: 30px;
}

.metrics-subtext {
    font-size: 16px;
    color: #777;
    margin-bottom: 30px;
    margin-left: 30px;
    text-align: left;
}

[dir='rtl'] .metrics-title,
.metrics-subtext {
    margin-right: 30px;
}

.metrics-grid {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.metric-card {
    width: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: left;
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: scale(1.02);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-icon {
    font-size: 24px;
    background: #eee;
    padding: 5px;
    border-radius: 50%;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    margin-top: 10px;
}

.change {
    font-size: 14px;
    margin-left: 10px;
    padding: 3px 6px;
    border-radius: 5px;
}

.positive {
    color: green;
    background: #e6ffec;
}

.negative {
    color: red;
    background: #ffe6e6;
}

.progress-bar {
    width: 100%;
    height: 5px;
    background: #eee;
    margin-top: 10px;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #3498db;
}