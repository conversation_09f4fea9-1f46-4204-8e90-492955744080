import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import DocumentsList from './DocumentsList';
import axios from 'axios';

// Mock axios
jest.mock('axios');

// Mock the translation function
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

describe('DocumentsList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    render(<DocumentsList userId="test-user-id" />);
    expect(screen.getByText('Loading documents...')).toBeInTheDocument();
  });

  test('renders empty state when no documents are found', async () => {
    axios.get.mockResolvedValueOnce({ data: [] });
    
    render(<DocumentsList userId="test-user-id" />);
    
    await waitFor(() => {
      expect(screen.getByText('No documents found')).toBeInTheDocument();
    });
  });

  test('renders documents list when documents are found', async () => {
    const mockDocuments = [
      {
        id: 1,
        fileName: 'test_document.pdf',
        originalFileName: 'test_document.pdf',
        fileType: 'application/pdf',
        fileSize: 1024,
        description: 'Test document',
        uploadDate: '2023-05-15T10:30:00',
        fileDownloadUri: 'http://example.com/download/test_document.pdf',
        fileViewUri: 'http://example.com/view/test_document.pdf'
      },
      {
        id: 2,
        fileName: 'test_image.jpg',
        originalFileName: 'test_image.jpg',
        fileType: 'image/jpeg',
        fileSize: 2048,
        description: 'Test image',
        uploadDate: '2023-05-16T11:30:00',
        fileDownloadUri: 'http://example.com/download/test_image.jpg',
        fileViewUri: 'http://example.com/view/test_image.jpg'
      }
    ];
    
    axios.get.mockResolvedValueOnce({ data: mockDocuments });
    
    render(<DocumentsList userId="test-user-id" />);
    
    await waitFor(() => {
      expect(screen.getByText('test_document.pdf')).toBeInTheDocument();
      expect(screen.getByText('test_image.jpg')).toBeInTheDocument();
      expect(screen.getByText('Test document')).toBeInTheDocument();
      expect(screen.getByText('Test image')).toBeInTheDocument();
    });
  });

  test('renders error state when API call fails', async () => {
    axios.get.mockRejectedValueOnce(new Error('API error'));
    
    render(<DocumentsList userId="test-user-id" />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load documents')).toBeInTheDocument();
    });
  });

  test('renders in read-only mode when readOnly prop is true', async () => {
    const mockDocuments = [
      {
        id: 1,
        fileName: 'test_document.pdf',
        originalFileName: 'test_document.pdf',
        fileType: 'application/pdf',
        fileSize: 1024,
        description: 'Test document',
        uploadDate: '2023-05-15T10:30:00',
        fileDownloadUri: 'http://example.com/download/test_document.pdf',
        fileViewUri: 'http://example.com/view/test_document.pdf'
      }
    ];
    
    axios.get.mockResolvedValueOnce({ data: mockDocuments });
    
    render(<DocumentsList userId="test-user-id" readOnly={true} />);
    
    await waitFor(() => {
      expect(screen.getByText('test_document.pdf')).toBeInTheDocument();
      // Delete button should not be present in read-only mode
      expect(screen.queryByTitle('Delete')).not.toBeInTheDocument();
    });
  });
});
