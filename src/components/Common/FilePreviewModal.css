.file-preview-modal .ant-modal-body {
  padding: 0;
  overflow: hidden;
}

.file-preview-content {
  position: relative;
  height: 70vh;
  overflow: auto;
  background-color: #f5f5f5;
}

.file-preview-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 16px;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdf-preview-container {
  height: 100%;
  width: 100%;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.generic-preview-container,
.office-preview-container,
.archive-preview-container,
.text-preview-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 24px;
  text-align: center;
}

.generic-preview-container .anticon,
.office-preview-container .anticon,
.archive-preview-container .anticon {
  font-size: 64px;
  color: #1890ff;
  margin-bottom: 16px;
}

.text-preview-container {
  height: 100%;
  width: 100%;
  padding: 0;
}

.text-preview-container .preview-iframe {
  background-color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .file-preview-content {
    height: 50vh;
  }

  .generic-preview-container .anticon,
  .office-preview-container .anticon,
  .archive-preview-container .anticon {
    font-size: 48px;
  }

  .file-preview-modal .ant-modal-footer {
    padding: 10px;
  }

  .file-preview-modal .ant-modal-title {
    font-size: 16px;
  }
}
