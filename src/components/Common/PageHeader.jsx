import React, { useState } from 'react';
import { Input, Avatar, Dropdown, message } from 'antd';
import { SearchOutlined, UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { clearAuth } from '../../redux/Slice/authSlice';
import { useTranslation } from 'react-i18next';
import './PageHeader.css';

const { Search } = Input;

const PageHeader = ({
  title,
  subtitle,
  timestamp,
  onSearch,
  showSearch = true,
  userAvatar = null
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Format current date as "Monday 27 Feb, 2023"
  const formattedDate = () => {
    const date = new Date();
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Format for "Last login: April 12, 2023 - 5:07 PM" or "Last Updated on Today"
  const getSubtitleText = () => {
    if (timestamp) {
      return timestamp;
    }
    if (subtitle) {
      return subtitle;
    }
    return `Last Updated on Today`;
  };

  // Handle sign out
  const handleSignOut = () => {
    dispatch(clearAuth());
    message.success(t('Successfully signed out'));
    navigate('/signin');
  };

  return (
    <div className="page-header-container">
      <div className="page-header">
        <div className="page-header-left">
          <h1 className="page-title">{title}</h1>
          <p className="page-subtitle">{getSubtitleText()}</p>
        </div>
        <div className="page-header-right">
          {showSearch && (
            <Search
              placeholder="Search"
              allowClear
              onSearch={onSearch}
              className="page-search"
              prefix={<SearchOutlined />}
            />
          )}

          {userAvatar && (
            <div className="user-avatar">
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'profile',
                      icon: <UserOutlined />,
                      label: t('Profile'),
                      onClick: () => navigate('/profile')
                    },
                    {
                      key: 'settings',
                      icon: <SettingOutlined />,
                      label: t('Settings'),
                      onClick: () => navigate('/settings')
                    },
                    {
                      type: 'divider'
                    },
                    {
                      key: 'signout',
                      icon: <LogoutOutlined />,
                      label: t('Sign Out'),
                      onClick: handleSignOut
                    }
                  ]
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <div className="user-avatar-wrapper">
                  <Avatar src={userAvatar} size="large" className="clickable-avatar" />
                </div>
              </Dropdown>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
