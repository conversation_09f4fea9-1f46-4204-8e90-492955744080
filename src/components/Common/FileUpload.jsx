import React, { useState } from 'react';
import { Upload, Button, message, Spin } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import axios from 'axios';

/**
 * Reusable file upload component
 * @param {Object} props Component props
 * @param {string} props.fileType Type of file (e.g., "profile", "logo", "certificate")
 * @param {Function} props.onUploadSuccess Callback function when upload is successful
 * @param {Function} props.onUploadError Callback function when upload fails
 * @param {string} props.buttonText Text to display on the upload button
 * @param {string} props.accept File types to accept (e.g., "image/*", ".pdf,.doc,.docx")
 * @param {boolean} props.multiple Whether to allow multiple file uploads
 * @param {number} props.maxCount Maximum number of files
 * @param {string} props.apiUrl API URL for file upload
 */
const FileUpload = ({
  fileType = 'document',
  onUploadSuccess,
  onUploadError,
  buttonText = 'Upload File',
  accept = '*/*',
  multiple = false,
  maxCount = 1,
  apiUrl = `${import.meta.env.VITE_APP_API_BASE_URL_UPLOAD}/upload`
}) => {
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async (options) => {
    const { onSuccess, onError, file, onProgress } = options;
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', fileType);

    setUploading(true);
    
    try {
      const response = await axios.post(apiUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (event) => {
          onProgress({ percent: (event.loaded / event.total) * 100 });
        },
      });

      setUploading(false);
      onSuccess(response.data, file);
      
      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
      
      message.success(`${file.name} uploaded successfully`);
    } catch (error) {
      setUploading(false);
      onError(error);
      
      if (onUploadError) {
        onUploadError(error);
      }
      
      message.error(`${file.name} upload failed: ${error.message}`);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple,
    maxCount,
    accept,
    fileList,
    customRequest: handleUpload,
    onChange(info) {
      let newFileList = [...info.fileList];
      
      // Limit the number of files if maxCount is set
      if (maxCount > 0) {
        newFileList = newFileList.slice(-maxCount);
      }
      
      // Update file status
      newFileList = newFileList.map(file => {
        if (file.response) {
          file.url = file.response.fileDownloadUri;
        }
        return file;
      });
      
      setFileList(newFileList);
    },
    onRemove(file) {
      // If the file has been uploaded and has a response with fileName
      if (file.response && file.response.fileName) {
        // You can implement file deletion here if needed
        // axios.delete(`http://localhost:8080/api/files/delete/${file.response.fileName}`);
      }
    },
  };

  return (
    <div className="file-upload-container">
      <Upload {...uploadProps} listType="picture">
        <Button icon={<UploadOutlined />} disabled={uploading}>
          {uploading ? <Spin size="small" /> : buttonText}
        </Button>
      </Upload>
    </div>
  );
};

export default FileUpload;
