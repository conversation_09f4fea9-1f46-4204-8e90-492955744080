/**
 * Session Extension Modal
 * Allows users to extend their session by re-entering their password
 * This provides a better UX than forcing logout when token expires
 */

import React, { useState } from 'react';
import { Modal, Form, Input, Button, Alert } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { setTokens, clearAuth } from '../../redux/Slice/authSlice';

const SessionExtensionModal = ({ visible, onCancel, onSuccess }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Debug user data
  console.log('SessionExtensionModal: Current user data:', user);

  const handleExtendSession = async (values) => {
    setLoading(true);
    setError(null);

    try {
      console.log('SessionExtensionModal: Attempting to extend session with:', {
        username: user?.email || values.username,
        hasPassword: !!values.password,
        endpoint: import.meta.env.VITE_APP_KEYCLOAK_URL
      });

      // Use the same login endpoint to get fresh tokens
      const response = await axios.post(
        import.meta.env.VITE_APP_KEYCLOAK_URL,
        {
          username: user?.email || values.username,
          password: values.password,
        },
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      console.log('SessionExtensionModal: Login response:', {
        status: response.status,
        hasAccessToken: !!response.data?.access_token,
        hasRefreshToken: !!response.data?.refresh_token
      });

      const { access_token, refresh_token } = response.data;

      if (access_token) {
        // Decode the new access token to get user data
        try {
          const tokenParts = access_token.split(".");
          if (tokenParts.length === 3) {
            const base64Url = tokenParts[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
              return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            
            const decodedAccessToken = JSON.parse(jsonPayload);

            // Update tokens in Redux and localStorage
            dispatch(setTokens({
              accessToken: access_token,
              refreshToken: refresh_token,
              user: decodedAccessToken,
            }));

            console.log('SessionExtensionModal: Session extended successfully');
            form.resetFields();
            onSuccess && onSuccess();
          }
        } catch (decodeError) {
          console.error("Error decoding JWT:", decodeError);
          throw new Error("Failed to process new session data");
        }
      }
    } catch (error) {
      console.error("Session extension error:", error);
      console.error("Error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      if (error.response?.status === 401) {
        setError(t('Invalid password. Please check your password and try again.'));
      } else if (error.response?.status === 403) {
        setError(t('Account access denied. Please contact support.'));
      } else if (error.response?.status === 400) {
        setError(t('Invalid request. Please check your credentials.'));
      } else if (error.response) {
        setError(t(`Server error (${error.response.status}): ${error.response.data?.message || error.response.statusText}`));
      } else if (error.request) {
        setError(t('Network error. Please check your connection and try again.'));
      } else {
        setError(t('Failed to extend session. Please try again or log in manually.'));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setError(null);
    onCancel && onCancel();
  };

  const handleLogoutInstead = () => {
    dispatch(clearAuth());
    navigate('/signin');
    handleCancel();
  };

  return (
    <Modal
      title={t('Extend Your Session')}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      closable={!loading}
      maskClosable={!loading}
      width={400}
      centered
    >
      <div style={{ marginBottom: '16px' }}>
        <Alert
          message={t('Session About to Expire')}
          description={t('Your session is about to expire. Enter your password to continue working, or log out to save your progress.')}
          type="warning"
          showIcon
        />
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleExtendSession}
        size="large"
      >
        {!user?.email && (
          <Form.Item
            name="username"
            label={t("Email")}
            rules={[
              { required: true, message: t("Email is required") },
              { type: "email", message: t("Please enter a valid email address") },
            ]}
          >
            <Input 
              prefix={<UserOutlined />}
              placeholder={t("Enter your email")} 
              disabled={loading} 
            />
          </Form.Item>
        )}

        <Form.Item
          name="password"
          label={t("Password")}
          rules={[
            { required: true, message: t("Password is required") },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t("Enter your password")}
            disabled={loading}
          />
        </Form.Item>

        {error && (
          <Alert
            message={error}
            type="error"
            style={{ marginBottom: '16px' }}
            showIcon
          />
        )}

        <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
          <Button 
            onClick={handleLogoutInstead}
            disabled={loading}
          >
            {t('Log Out Instead')}
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
          >
            {t('Extend Session')}
          </Button>
        </div>
      </Form>

      {user?.email && (
        <div style={{ marginTop: '16px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
          {t('Logged in as')}: {user.email}
        </div>
      )}
    </Modal>
  );
};

export default SessionExtensionModal;
