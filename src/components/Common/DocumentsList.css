.documents-list-container {
  width: 100%;
}

.documents-list {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.documents-list .ant-list-item {
  padding: 12px 16px;
  transition: background-color 0.3s;
}

.documents-list .ant-list-item:hover {
  background-color: #f5f5f5;
}

.documents-list .ant-list-item-meta-avatar {
  font-size: 24px;
  color: #1890ff;
  margin-right: 16px;
}

.documents-list .ant-list-item-meta-title {
  margin-bottom: 4px;
}

.documents-list .ant-list-item-meta-title a {
  color: #1890ff;
  transition: color 0.3s;
}

.documents-list .ant-list-item-meta-title a:hover {
  color: #40a9ff;
}

.documents-list .ant-list-item-action {
  margin-left: 48px;
}

.documents-list-loading,
.documents-list-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background-color: #fafafa;
  border-radius: 8px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .documents-list .ant-list-item {
    padding: 8px 12px;
  }
  
  .documents-list .ant-list-item-meta-avatar {
    font-size: 20px;
  }
  
  .documents-list .ant-list-item-action {
    margin-left: 24px;
  }
  
  .documents-list .ant-list-item-action > li {
    padding: 0 4px;
  }
}

@media (max-width: 576px) {
  .documents-list .ant-list-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .documents-list .ant-list-item-action {
    margin-left: 0;
    margin-top: 12px;
    padding-inline-start: 0;
  }
  
  .documents-list .ant-list-item-meta {
    width: 100%;
  }
}
