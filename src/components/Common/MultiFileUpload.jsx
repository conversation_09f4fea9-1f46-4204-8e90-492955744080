import React, { useState, useEffect } from 'react';
import { Upload, Button, message, Spin, List, Space, Typography, Tooltip, Modal } from 'antd';
import { UploadOutlined, DeleteOutlined, EyeOutlined, DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import './MultiFileUpload.css';
import {Progress} from 'antd';

const { Text, Paragraph } = Typography;
const { Dragger } = Upload;

/**
 * Component for uploading and managing multiple files
 * @param {Object} props Component props
 * @param {string} props.userId User ID for associating files
 * @param {string} props.fileType Type of file (e.g., "vendor_document")
 * @param {Function} props.onUploadSuccess Callback function when upload is successful
 * @param {Function} props.onUploadError Callback function when upload fails
 * @param {Function} props.onDeleteSuccess Callback function when delete is successful
 * @param {string} props.buttonText Text to display on the upload button
 * @param {string} props.accept File types to accept (e.g., ".pdf,.doc,.docx")
 * @param {boolean} props.multiple Whether to allow multiple file uploads
 * @param {number} props.maxCount Maximum number of files
 * @param {string} props.uploadApiUrl API URL for file upload
 * @param {string} props.listApiUrl API URL for listing files
 * @param {string} props.deleteApiUrl API URL for deleting files
 */
const MultiFileUpload = ({
  userId,
  fileType = 'vendor_document',
  onUploadSuccess,
  onUploadError,
  onDeleteSuccess,
  buttonText = 'Upload Files',
  accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.txt',
  multiple = true,
  maxCount = 10,
  uploadApiUrl = `${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents/upload`,
  listApiUrl = `${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents/user`,
  deleteApiUrl = `${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents`
}) => {
  const { t } = useTranslation();
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState({});

  // Fetch existing files on component mount
  useEffect(() => {
    if (userId) {
      fetchExistingFiles();
    }
  }, [userId]);

  const fetchExistingFiles = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const response = await axios.get(`${listApiUrl}/${userId}`);
      
      // Transform the response data to match Upload component's fileList format
      const existingFiles = response.data.map(file => ({
        uid: file.id,
        name: file.originalFileName || file.fileName,
        status: 'done',
        url: file.fileViewUri,
        downloadUrl: file.fileDownloadUri,
        response: file,
        size: file.fileSize,
        type: file.fileType
      }));
      
      setFileList(existingFiles);
    } catch (error) {
      console.error('Error fetching existing files:', error);
      message.error(t('Failed to load existing files'));
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (options) => {
    const { onSuccess, onError, file, onProgress } = options;
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('description', file.name);
    
    setUploading(true);
    
    try {
      const response = await axios.post(`${uploadApiUrl}/${userId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (event) => {
          onProgress({ percent: (event.loaded / event.total) * 100 });
        },
      });

      setUploading(false);
      onSuccess(response.data, file);
      
      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
      
      message.success(`${file.name} ${t('uploaded successfully')}`);
      
      // Refresh the file list
      fetchExistingFiles();
    } catch (error) {
      setUploading(false);
      onError(error);
      
      if (onUploadError) {
        onUploadError(error);
      }
      
      message.error(`${file.name} ${t('upload failed')}: ${error.message}`);
    }
  };

  const handleDelete = async (file) => {
    try {
      // If the file has been uploaded and has a response with id
      if (file.response && file.response.id) {
        await axios.delete(`${deleteApiUrl}/${file.response.id}`);
        message.success(`${file.name} ${t('deleted successfully')}`);
        
        if (onDeleteSuccess) {
          onDeleteSuccess(file.response);
        }
        
        // Refresh the file list
        fetchExistingFiles();
      }
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      message.error(`${t('Failed to delete')} ${file.name}: ${error.message}`);
      return false;
    }
  };

  const handlePreview = (file) => {
    setPreviewFile({
      url: file.url,
      downloadUrl: file.downloadUrl || file.url,
      name: file.name,
      type: file.type
    });
    setPreviewVisible(true);
  };

  const uploadProps = {
    name: 'file',
    multiple,
    accept,
    fileList,
    customRequest: handleUpload,
    onChange(info) {
      let newFileList = [...info.fileList];
      
      // Limit the number of files if maxCount is set
      if (maxCount > 0) {
        newFileList = newFileList.slice(-maxCount);
      }
      
      // Update file status
      newFileList = newFileList.map(file => {
        if (file.response) {
          file.url = file.response.fileViewUri;
          file.downloadUrl = file.response.fileDownloadUri;
        }
        return file;
      });
      
      setFileList(newFileList);
    },
    onRemove: handleDelete,
    onPreview: handlePreview,
    progress: {
      strokeColor: {
        '0%': '#108ee9',
        '100%': '#87d068',
      },
      strokeWidth: 3,
      format: percent => `${parseFloat(percent.toFixed(2))}%`,
    },
  };

  return (
    <div className="multi-file-upload-container">
      {loading ? (
        <div className="file-upload-loading">
          <Spin size="large" />
          <div className="loading-text">{t('Loading files...')}</div>
        </div>
      ) : (
        <>
          <Dragger {...uploadProps} className="file-upload-dragger">
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{t('Click or drag files to this area to upload')}</p>
            <p className="ant-upload-hint">
              {t('Support for a single or bulk upload. Strictly prohibited from uploading company data or other banned files.')}
            </p>
          </Dragger>
          
          {fileList.length > 0 && (
            <List
              className="uploaded-files-list"
              itemLayout="horizontal"
              dataSource={fileList}
              renderItem={file => (
                <List.Item
                  key={file.uid}
                  actions={[
                    <Tooltip title={t('View')}>
                      <Button 
                        icon={<EyeOutlined />} 
                        onClick={() => handlePreview(file)}
                        size="small"
                      />
                    </Tooltip>,
                    <Tooltip title={t('Download')}>
                      <Button 
                        icon={<DownloadOutlined />} 
                        onClick={() => window.open(file.downloadUrl || file.url, '_blank')}
                        size="small"
                      />
                    </Tooltip>,
                    <Tooltip title={t('Delete')}>
                      <Button 
                        icon={<DeleteOutlined />} 
                        onClick={() => handleDelete(file)}
                        size="small"
                        danger
                      />
                    </Tooltip>
                  ]}
                >
                  <List.Item.Meta
                    title={file.name}
                    description={
                      <Space direction="vertical" size={0}>
                        <Text type="secondary">
                          {file.size ? `${(file.size / 1024).toFixed(2)} KB` : ''}
                        </Text>
                        {file.status === 'uploading' && (
                          <Progress percent={file.percent} size="small" />
                        )}
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          )}
          
          <Modal
            open={previewVisible}
            title={previewFile.name}
            footer={[
              <Button key="download" icon={<DownloadOutlined />} onClick={() => window.open(previewFile.downloadUrl, '_blank')}>
                {t('Download')}
              </Button>,
              <Button key="close" onClick={() => setPreviewVisible(false)}>
                {t('Close')}
              </Button>
            ]}
            onCancel={() => setPreviewVisible(false)}
            width="80%"
          >
            <iframe
              src={previewFile.url}
              style={{ width: '100%', height: '70vh' }}
              title={previewFile.name}
            />
          </Modal>
        </>
      )}
    </div>
  );
};

export default MultiFileUpload;
