/**
 * Token Expiration Warning Component
 * Shows a warning notification when the user's token is about to expire
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { notification, Button } from 'antd';
import { ExclamationCircleOutlined, LogoutOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { shouldShowExpirationWarning, getTimeUntilExpiration, getAccessToken } from '../../services/tokenService';
import { clearAuth } from '../../redux/Slice/authSlice';

const TokenExpirationWarning = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [warningShown, setWarningShown] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }

    const checkTokenExpiration = () => {
      const accessToken = getAccessToken();
      
      if (!accessToken) {
        return;
      }

      const shouldWarn = shouldShowExpirationWarning();
      const timeLeft = getTimeUntilExpiration(accessToken);
      
      if (shouldWarn && !warningShown && timeLeft > 0) {
        showExpirationWarning(timeLeft);
        setWarningShown(true);
      } else if (timeLeft <= 0) {
        // Token has expired, logout user
        handleTokenExpired();
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Check every minute
    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, warningShown]);

  const showExpirationWarning = (minutesLeft) => {
    notification.warning({
      message: t('Session Expiring Soon'),
      description: t(`Your session will expire in ${minutesLeft} minutes. Please save your work and log in again to continue.`),
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      duration: 0, // Don't auto-close
      placement: 'topRight',
      btn: (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button 
            size="small" 
            onClick={() => {
              notification.destroy();
              setWarningShown(false);
            }}
          >
            {t('Dismiss')}
          </Button>
          <Button 
            type="primary" 
            size="small" 
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            {t('Log Out')}
          </Button>
        </div>
      ),
      key: 'token-expiration-warning',
    });
  };

  const handleTokenExpired = () => {
    notification.error({
      message: t('Session Expired'),
      description: t('Your session has expired. Please log in again.'),
      duration: 5,
      placement: 'topRight',
      key: 'token-expired',
    });

    // Clear auth state and redirect to login
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleLogout = () => {
    notification.destroy('token-expiration-warning');
    dispatch(clearAuth());
    navigate('/signin');
  };

  // This component doesn't render anything visible
  return null;
};

export default TokenExpirationWarning;
