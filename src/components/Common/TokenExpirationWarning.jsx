/**
 * Token Expiration Warning Component
 * Shows a warning notification when the user's token is about to expire
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { notification, Button } from 'antd';
import { ExclamationCircleOutlined, LogoutOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import SessionExtensionModal from './SessionExtensionModal';
import {
  shouldShowExpirationWarning,
  shouldShowUrgentWarning,
  getTimeUntilExpiration,
  getAccessToken
} from '../../services/tokenService';
import { clearAuth } from '../../redux/Slice/authSlice';
import { isAutoLogoutEnabled, isSessionExtensionEnabled, getMinWarningInterval } from '../../config/tokenConfig';

const TokenExpirationWarning = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [warningShown, setWarningShown] = useState(false);
  const [urgentWarningShown, setUrgentWarningShown] = useState(false);
  const [lastWarningTime, setLastWarningTime] = useState(0);
  const [showExtensionModal, setShowExtensionModal] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }

    const checkTokenExpiration = () => {
      const accessToken = getAccessToken();

      if (!accessToken) {
        return;
      }

      const shouldWarn = shouldShowExpirationWarning();
      const shouldUrgentWarn = shouldShowUrgentWarning();
      const timeLeft = getTimeUntilExpiration(accessToken);
      const currentTime = Date.now();

      // Prevent showing warnings too frequently (configurable interval)
      const timeSinceLastWarning = currentTime - lastWarningTime;
      const minWarningInterval = getMinWarningInterval();

      if (timeLeft <= 0) {
        // Token has expired - handle based on configuration
        if (isAutoLogoutEnabled()) {
          handleTokenExpired();
        } else {
          // Show final expiration warning instead of auto-logout
          showFinalExpirationWarning();
        }
      } else if (shouldUrgentWarn && !urgentWarningShown && timeSinceLastWarning > minWarningInterval) {
        // Show urgent warning (5 minutes or less)
        showUrgentExpirationWarning(timeLeft);
        setUrgentWarningShown(true);
        setLastWarningTime(currentTime);
      } else if (shouldWarn && !warningShown && !urgentWarningShown && timeSinceLastWarning > minWarningInterval) {
        // Show regular warning (15 minutes or less)
        showExpirationWarning(timeLeft);
        setWarningShown(true);
        setLastWarningTime(currentTime);
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Check every minute
    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, warningShown, urgentWarningShown, lastWarningTime]);

  const showExpirationWarning = (minutesLeft) => {
    notification.warning({
      message: t('Session Expiring Soon'),
      description: t(`Your session will expire in ${minutesLeft} minutes. Please save your work and be prepared to log in again.`),
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      duration: 30, // Auto-close after 30 seconds
      placement: 'topRight',
      btn: (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            size="small"
            onClick={() => {
              notification.destroy('token-expiration-warning');
              setWarningShown(false);
            }}
          >
            {t('Dismiss')}
          </Button>
          {isSessionExtensionEnabled() && (
            <Button
              size="small"
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => {
                notification.destroy('token-expiration-warning');
                setShowExtensionModal(true);
              }}
            >
              {t('Extend Session')}
            </Button>
          )}
          <Button
            type="primary"
            size="small"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            {t('Log Out')}
          </Button>
        </div>
      ),
      key: 'token-expiration-warning',
    });
  };

  const showUrgentExpirationWarning = (minutesLeft) => {
    notification.error({
      message: t('Session Expiring Very Soon!'),
      description: t(`⚠️ Your session will expire in ${minutesLeft} minutes! Please save your work immediately. You will be automatically logged out when the session expires.`),
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      duration: 0, // Don't auto-close
      placement: 'topRight',
      btn: (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            size="small"
            onClick={() => {
              notification.destroy('urgent-token-warning');
              setUrgentWarningShown(false);
            }}
          >
            {t('Dismiss')}
          </Button>
          {isSessionExtensionEnabled() && (
            <Button
              size="small"
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => {
                notification.destroy('urgent-token-warning');
                setShowExtensionModal(true);
              }}
            >
              {t('Extend Session')}
            </Button>
          )}
          <Button
            type="primary"
            size="small"
            danger
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            {t('Log Out Now')}
          </Button>
        </div>
      ),
      key: 'urgent-token-warning',
    });
  };

  const showFinalExpirationWarning = () => {
    notification.error({
      message: t('Session Expired'),
      description: t('🔒 Your session has expired. You can continue browsing, but you\'ll need to log in again to perform any actions that require authentication.'),
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      duration: 0, // Don't auto-close
      placement: 'topRight',
      btn: (
        <div style={{ display: 'flex', gap: '8px' }}>
          {isSessionExtensionEnabled() && (
            <Button
              type="primary"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => {
                notification.destroy('session-expired-warning');
                setShowExtensionModal(true);
              }}
            >
              {t('Log In Again')}
            </Button>
          )}
          <Button
            size="small"
            onClick={() => {
              notification.destroy('session-expired-warning');
            }}
          >
            {t('Continue Browsing')}
          </Button>
        </div>
      ),
      key: 'session-expired-warning',
    });
  };

  const handleTokenExpired = () => {
    notification.error({
      message: t('Session Expired'),
      description: t('Your session has expired. Please log in again.'),
      duration: 5,
      placement: 'topRight',
      key: 'token-expired',
    });

    // Clear auth state and redirect to login
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleLogout = () => {
    notification.destroy('token-expiration-warning');
    notification.destroy('urgent-token-warning');
    setShowExtensionModal(false);
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleSessionExtended = () => {
    // Reset warning states when session is successfully extended
    setWarningShown(false);
    setUrgentWarningShown(false);
    setShowExtensionModal(false);
    setLastWarningTime(0);

    // Clear any existing notifications
    notification.destroy('token-expiration-warning');
    notification.destroy('urgent-token-warning');
  };

  const handleExtensionModalCancel = () => {
    setShowExtensionModal(false);
  };

  return (
    <>
      <SessionExtensionModal
        visible={showExtensionModal}
        onCancel={handleExtensionModalCancel}
        onSuccess={handleSessionExtended}
      />
    </>
  );
};

export default TokenExpirationWarning;
