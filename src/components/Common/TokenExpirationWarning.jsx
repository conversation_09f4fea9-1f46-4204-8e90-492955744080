/**
 * Token Expiration Warning Component
 * Shows a warning notification when the user's token is about to expire
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Modal, Button, Alert } from 'antd';
import { ExclamationCircleOutlined, LogoutOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import SessionExtensionModal from './SessionExtensionModal';
import {
  shouldShowExpirationWarning,
  shouldShowUrgentWarning,
  getTimeUntilExpiration,
  getAccessToken
} from '../../services/tokenService';
import { clearAuth } from '../../redux/Slice/authSlice';
import { isAutoLogoutEnabled, isSessionExtensionEnabled, getMinWarningInterval } from '../../config/tokenConfig';

const TokenExpirationWarning = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);

  console.log('TokenExpirationWarning: Component rendered, isAuthenticated:', isAuthenticated);
  
  const [warningShown, setWarningShown] = useState(false);
  const [urgentWarningShown, setUrgentWarningShown] = useState(false);
  const [lastWarningTime, setLastWarningTime] = useState(0);
  const [showExtensionModal, setShowExtensionModal] = useState(false);

  // Modal states
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showUrgentModal, setShowUrgentModal] = useState(false);
  const [showExpiredModal, setShowExpiredModal] = useState(false);
  const [currentTimeLeft, setCurrentTimeLeft] = useState(0);

  // Reset warning states every time the component mounts (for testing)
  useEffect(() => {
    console.log('TokenExpirationWarning: Resetting warning states for testing');
    setWarningShown(false);
    setUrgentWarningShown(false);
    setLastWarningTime(0);
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }

    const checkTokenExpiration = () => {
      const accessToken = getAccessToken();

      if (!accessToken) {
        console.log('TokenExpirationWarning: No access token found');
        return;
      }

      const shouldWarn = shouldShowExpirationWarning();
      const shouldUrgentWarn = shouldShowUrgentWarning();
      const timeLeft = getTimeUntilExpiration(accessToken);
      const currentTime = Date.now();

      // Debug logging
      console.log('TokenExpirationWarning: Check results:', {
        timeLeft,
        shouldWarn,
        shouldUrgentWarn,
        warningShown,
        urgentWarningShown,
        isAutoLogoutEnabled: isAutoLogoutEnabled(),
        timeSinceLastWarning: currentTime - lastWarningTime
      });

      // Prevent showing warnings too frequently (configurable interval)
      const timeSinceLastWarning = currentTime - lastWarningTime;
      const minWarningInterval = getMinWarningInterval();

      if (timeLeft <= 0) {
        console.log('TokenExpirationWarning: Token expired, handling...');
        // Token has expired - handle based on configuration
        if (isAutoLogoutEnabled()) {
          handleTokenExpired();
        } else {
          // Show final expiration modal instead of auto-logout
          setCurrentTimeLeft(0);
          setShowExpiredModal(true);
        }
      } else if (shouldUrgentWarn && !urgentWarningShown && timeSinceLastWarning > minWarningInterval) {
        console.log('TokenExpirationWarning: Showing urgent warning modal');
        // Show urgent warning modal
        setCurrentTimeLeft(timeLeft);
        setShowUrgentModal(true);
        setUrgentWarningShown(true);
        setLastWarningTime(currentTime);
      } else if (shouldWarn && !warningShown && !urgentWarningShown && timeSinceLastWarning > minWarningInterval) {
        console.log('TokenExpirationWarning: Showing regular warning modal');
        // Show regular warning modal
        setCurrentTimeLeft(timeLeft);
        setShowWarningModal(true);
        setWarningShown(true);
        setLastWarningTime(currentTime);
      } else {
        console.log('TokenExpirationWarning: No warning shown because:', {
          shouldWarn,
          shouldUrgentWarn,
          warningShown,
          urgentWarningShown,
          timeSinceLastWarning,
          minWarningInterval,
          'timeSinceLastWarning > minWarningInterval': timeSinceLastWarning > minWarningInterval
        });
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Check every 10 seconds for testing (normally 60000 for 1 minute)
    const interval = setInterval(checkTokenExpiration, 10000);

    return () => clearInterval(interval);
  }, [isAuthenticated, warningShown, urgentWarningShown, lastWarningTime]);

  // Modal handlers
  const handleWarningModalClose = () => {
    setShowWarningModal(false);
    setWarningShown(false);
  };

  const handleUrgentModalClose = () => {
    setShowUrgentModal(false);
    setUrgentWarningShown(false);
  };

  const handleExpiredModalClose = () => {
    setShowExpiredModal(false);
  };



  const handleTokenExpired = () => {
    notification.error({
      message: t('Session Expired'),
      description: t('Your session has expired. Please log in again.'),
      duration: 5,
      placement: 'topRight',
      key: 'token-expired',
    });

    // Clear auth state and redirect to login
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleLogout = () => {
    // Close all modals
    setShowWarningModal(false);
    setShowUrgentModal(false);
    setShowExpiredModal(false);
    setShowExtensionModal(false);
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleSessionExtended = () => {
    // Reset warning states when session is successfully extended
    setWarningShown(false);
    setUrgentWarningShown(false);
    setShowExtensionModal(false);
    setLastWarningTime(0);

    // Clear any existing notifications
    notification.destroy('token-expiration-warning');
    notification.destroy('urgent-token-warning');
  };

  const handleExtensionModalCancel = () => {
    setShowExtensionModal(false);
  };

  return (
    <>
      {/* Session Extension Modal */}
      <SessionExtensionModal
        visible={showExtensionModal}
        onCancel={handleExtensionModalCancel}
        onSuccess={handleSessionExtended}
      />

      {/* Regular Warning Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            {t('Session Expiring Soon')}
          </div>
        }
        open={showWarningModal}
        onCancel={handleWarningModalClose}
        footer={[
          <Button key="dismiss" onClick={handleWarningModalClose}>
            {t('Dismiss')}
          </Button>,
          isSessionExtensionEnabled() && (
            <Button
              key="extend"
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => {
                setShowWarningModal(false);
                setShowExtensionModal(true);
              }}
            >
              {t('Extend Session')}
            </Button>
          ),
          <Button
            key="logout"
            type="primary"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            {t('Log Out')}
          </Button>
        ].filter(Boolean)}
        centered
        closable={true}
      >
        <Alert
          message={t(`Your session will expire in ${currentTimeLeft} minutes.`)}
          description={t('Please save your work and be prepared to log in again.')}
          type="warning"
          showIcon
        />
      </Modal>

      {/* Urgent Warning Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            {t('Session Expiring Very Soon!')}
          </div>
        }
        open={showUrgentModal}
        onCancel={handleUrgentModalClose}
        footer={[
          <Button key="dismiss" onClick={handleUrgentModalClose}>
            {t('Dismiss')}
          </Button>,
          isSessionExtensionEnabled() && (
            <Button
              key="extend"
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => {
                setShowUrgentModal(false);
                setShowExtensionModal(true);
              }}
            >
              {t('Extend Session')}
            </Button>
          ),
          <Button
            key="logout"
            type="primary"
            danger
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            {t('Log Out Now')}
          </Button>
        ].filter(Boolean)}
        centered
        closable={true}
      >
        <Alert
          message={t(`⚠️ Your session will expire in ${currentTimeLeft} minutes!`)}
          description={t('Please save your work immediately. You will be automatically logged out when the session expires.')}
          type="error"
          showIcon
        />
      </Modal>

      {/* Expired Session Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            {t('Session Expired')}
          </div>
        }
        open={showExpiredModal}
        onCancel={handleExpiredModalClose}
        footer={[
          <Button key="continue" onClick={handleExpiredModalClose}>
            {t('Continue Browsing')}
          </Button>,
          isSessionExtensionEnabled() && (
            <Button
              key="login"
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => {
                setShowExpiredModal(false);
                setShowExtensionModal(true);
              }}
            >
              {t('Log In Again')}
            </Button>
          )
        ].filter(Boolean)}
        centered
        closable={true}
      >
        <Alert
          message={t('🔒 Your session has expired.')}
          description={t('You can continue browsing, but you\'ll need to log in again to perform any actions that require authentication.')}
          type="error"
          showIcon
        />
      </Modal>
    </>
  );
};

export default TokenExpirationWarning;
