.multi-file-upload-container {
  width: 100%;
  margin-bottom: 20px;
}

.file-upload-dragger {
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  border: 2px dashed #d9d9d9;
  transition: border-color 0.3s;
}

.file-upload-dragger:hover {
  border-color: #1890ff;
}

.uploaded-files-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 8px;
}

.uploaded-files-list .ant-list-item {
  padding: 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.uploaded-files-list .ant-list-item:hover {
  background-color: #f5f5f5;
}

.file-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .uploaded-files-list .ant-list-item-action {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .uploaded-files-list .ant-list-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
