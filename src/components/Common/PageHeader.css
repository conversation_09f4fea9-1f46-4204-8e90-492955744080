.page-header-container {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.page-header-left {
  display: flex;
  justify-content: start;
  text-align: left;
  flex-direction: column;
}

.page-header-right {
  display: flex;
  align-items: center;
  gap: 21px;
  width: 40em;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #888;
}

.page-search {
  width: 100%;

  border-radius: 20px;
}

.page-search .ant-input-affix-wrapper {
  border-radius: 20px;
  height: 40px;
border-top-right-radius: 20px;
  border: 1px solid #b8aaaa;
}

.page-search .ant-input-search-button {
  display: none;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar-wrapper {
  cursor: pointer;
}

.clickable-avatar {
  transition: all 0.3s;
}

.clickable-avatar:hover {
  opacity: 0.8;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-header-right {
    width: 100%;
    justify-content: space-between;
  }

  .page-search {
    width: 100%;
    max-width: 350px;
    border-radius:  4px;
  }
}

/* RTL support */
[dir="rtl"] .page-header-left {
  text-align: right;
}

[dir="rtl"] .page-header-right {
  flex-direction: row-reverse;
}
