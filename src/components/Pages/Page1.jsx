import React, { useState, useEffect } from 'react';
import logo from './Assets/arabic-logo.svg';
import { useTranslation } from 'react-i18next';
import blogs from './Data/Blog';
import metrics from './Data/KeyMatrices';
import testimonials from './Data/Testimonial';
import dealsData from './Data/Deals';
import categories from './Data/Technology';
import vendorsData from './Data/Vendors';
import customer from './Data/Customers';
import partners from './Data/Partners';

const Page1 = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState(
        localStorage.getItem('selectedLanguage') || 'English'
    );

    const languages = [
        { code: 'english', label: 'English', dir: 'ltr' },
        { code: 'arabic', label: 'Arabic', dir: 'rtl' }
    ];

    // Set initial language and direction
    useEffect(() => {
        const savedLanguage = localStorage.getItem('i18nextLng') || 'english';
        const langConfig = languages.find(lang => lang.code === savedLanguage) || languages[0];

        document.documentElement.lang = langConfig.code;
        document.documentElement.dir = langConfig.dir;
        document.body.style.direction = langConfig.dir;

        // For material-ui or other libraries that need RTL support
        if (langConfig.dir === 'rtl') {
            document.body.classList.add('rtl');
            document.body.classList.remove('ltr');
        } else {
            document.body.classList.add('ltr');
            document.body.classList.remove('rtl');
        }
    }, [languages]);

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code).then(() => {
            // Force direction change
            document.getElementsByTagName('html')[0].setAttribute('dir', language.dir);
            document.getElementsByTagName('html')[0].setAttribute('lang', language.code);

            // Force body direction
            document.body.style.direction = language.dir;

            // For Material-UI or similar
            if (language.dir === 'rtl') {
                document.body.classList.add('rtl-mode');
            } else {
                document.body.classList.remove('rtl-mode');
            }

            // Sometimes needed for full layout recalculation
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        });
    };
    return (
        <>
            <header className="nav-bar">
                <div className="logo">
                    <img src={logo} alt="Bond" />
                </div>
                <input type="text" className="search-box" placeholder={t("Find Vendors, Services, or Products")} />
                <nav className="navMenu">
                    <button className='menu-button'>{t("Home")}</button>
                    <button className='menu-button'>{t("About")}</button>
                    <button className='menu-button'>{t("Features")}</button>
                    <button className='menu-button'>{t("Pricing")}</button>
                    <button className="btn-primary">{t("Post Project")}</button>
                    <button className="btn-secondary">{t("Vendor Signup")}</button>
                    <button
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className="dropdown-toggle"
                        aria-haspopup="listbox"
                    >
                        <span>{selectedLanguage}</span>
                    </button>

                    {isDropdownOpen && (
                        <div className="dropdown-menu">
                            {languages.map((language) => (
                                <div
                                    key={language.code}
                                    onClick={() => handleLanguageChange(language)}
                                    className={`dropdown-item ${language.label === selectedLanguage ? 'active' : ''}`}
                                >
                                    {language.label}
                                </div>
                            ))}
                        </div>
                    )}
                </nav>
            </header>

            {/* Hero Section */}
            <section className="landing-container">
                <div className="hero-content">
                    <p className="hero-text">
                        {t("Find the Best")} <br />
                        <span className="highlight">{t("IT Services, Products & Solutions")}</span>
                    </p>
                    <div className="buttons">
                        <button className="post-project">{t("Post Your Project")}</button>
                        <button className="join-vendor">{t("Join as a Vendor")}</button>
                    </div>
                    <input type="text" className="search-box2" placeholder={t("Find Vendors, Services, or Products")} />
                </div>
                <img
                    src="https://plus.unsplash.com/premium_photo-1683288706157-9913483dffc8?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8ZGlnaXRhbCUyMHN0b3JlfGVufDB8fDB8fHww"
                    alt="IT Services"
                    className="hero-image"
                />
            </section>


            {/* Technologies Section */}
            <div className="tech-container">
                <div className="tech-head">
                    <div>
                        <h1 className="title">{t("Top High-Demand Technology Categories")}</h1>
                        <p className="subtitle">
                            {t("Ratings and reviews ensure transparency, helping you choose the best service providers with confidence.")}
                        </p>
                    </div>
                </div>
                <div className="grid3">
                    {categories.map((category, index) => (
                        <div key={index} className="card3">
                            <img src={category.image} alt={category.title} className="card-img3" />
                            <div className="card-content3">
                                <h2 className="card-title3">{category.title}</h2>
                                <p className="card-subtitle3">{category.subtitle}</p>
                                <p className="card-description3">{category.description}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Partners Section */}
            <div className="partner-container">
                <div className="partner-header">
                    <h1 className="text">{t("Partners")}</h1>
                    <p className="subtext">
                        {t("Building Strong Alliances for Exceptional Results")}
                    </p>
                </div>
                <div className="grid4">
                    {partners.map((partner, index) => (
                        <div key={index} className="card4" style={{ backgroundColor: partner.bgColor }}>
                            <img src={partner.logo} alt={partner.name} className="logo4" />
                        </div>
                    ))};
                </div>
            </div>

            {/* Testimonials Section */}
            <div className="container5">
                <div className="header-container5">
                    <div>
                        <h2 className="heading5">{t("Testimonial")}</h2>
                        <p className="subtext5">{t("What Our Clients Say!")}</p>
                    </div>
                    <div className="buttons5">
                        <button className="button5">←</button>
                        <button className="button5">→</button>
                    </div>
                </div>
                <div className="carousel5">
                    {testimonials.map((testimonial, i) => (
                        <div key={i} className="card5">
                            <div className="header5">
                                <img src={testimonial.logo} alt={testimonial.name} className="logo" />
                                <div className="stars5">⭐️⭐️⭐️⭐️⭐️</div>
                            </div>
                            <p className="name5">{testimonial.name}</p>
                            <p className="role5">— {testimonial.role}</p>
                            <p className="text5">"{testimonial.text}"</p>
                        </div>
                    ))}
                </div>
            </div>


            {/* Blog Section */}
            <div className="blog-container">
                <div className="blog-header">
                    <div>
                        <h2 className="title">{t("Blog & Insights")}</h2>
                        <p className="subtitle">
                            {t(" Explore the latest trends, expert opinions, and industry innovations from our trusted partners.")}
                        </p>
                    </div>
                    <button className="explore-btn">
                        {t("Explore Blogs →")}
                    </button>
                </div>
                <div className="blog-grid">
                    {blogs.map((blog) => (
                        <div key={blog.id} className="blog-card">
                            <img src={blog.image} alt={blog.category} className="blog-image" />
                            <div className="blog-content">
                                <p className="blog-category">{blog.category}</p>
                                <h3 className="blog-heading">
                                    {blog.title} <a href={blog.link} className="blog-link">↗</a>
                                </h3>
                                <p className="blog-description">{blog.description}</p>
                                <p className="blog-date">{t("Updated on")} <br /> <span>{blog.date}</span></p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Metrics Section */}
            <div className="metrics-container">
                <h2 className="metrics-title">{t("Key Metrics & Statistics")}</h2>
                <p className="metrics-subtext">
                    {t(" Measuring our impact through data-driven results and proven performance indicators.")}
                </p>
                <div className="metrics-grid">
                    {metrics.map((metric, index) => (
                        <div key={index} className="metric-card">
                            <div className="metric-header">
                                <h3>{metric.title}</h3>
                                <span className="metric-icon">{metric.icon}</span>
                            </div>
                            <div className="metric-value">
                                <strong>{metric.value}</strong>
                                <span className={`change ${metric.changeType}`}>
                                    {metric.changeType === "positive" ? "▲" : "▼"} {metric.change}
                                </span>
                            </div>
                            <p className="metric-subtext">{t("vs. previous month")}</p>
                            <div className="progress-bar">
                                <div className="progress-fill" style={{ width: `${metric.progress}%` }}></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Footer */}
            <footer className="footer">
                <div className="footer-container">
                    {/* Left Section */}
                    <div className="footer-left">
                        <p className="footer-logo">  <img src={logo} alt="Bond" /> </p>
                        <p className="footer-text">
                            {t("  Lorem ipsum dolor sit amet consectetur adipiscing elit aliquam.")}
                        </p>
                        <div className="social-icons">
                            <span>📘</span>
                            <span>🐦</span>
                            <span>📸</span>
                            <span>🔗</span>
                            <span>▶️</span>
                        </div>
                    </div>

                    {/* Middle Sections */}
                    <div className="footer-links">
                        <div className="footer-column">
                            <h4>{t("Product")}</h4>
                            <ul>
                                <li>{t("Features")}</li>
                                <li>{t("Features")}</li>
                                <li>{t("Case studies")}</li>
                                <li>{t("Reviews")}</li>
                                <li>{t("Updates")}</li>
                            </ul>
                        </div>
                        <div className="footer-column">
                            <h4>{t("Company")}</h4>
                            <ul>
                                <li>{t("About")}</li>
                                <li>{t("Contact us")}</li>
                                <li>{t("Careers")}</li>
                                <li>{t("Culture")}</li>
                                <li>{t("Blog")}</li>
                            </ul>
                        </div>
                        <div className="footer-column">
                            <h4>{t("Support")}</h4>
                            <ul>
                                <li>{t("Getting started")}</li>
                                <li>{t("Help center")}</li>
                                <li>{t("Server status")}</li>
                                <li>{t("Report a bug")}</li>
                                <li>{t("Chat support")}</li>
                            </ul>
                        </div>
                    </div>

                    {/* Right Section */}
                    <div className="footer-right">
                        <h4>{t("Contacts us")}</h4>
                        <p>📧 <EMAIL></p>
                        <p>📞 (414) 687 - 5892</p>
                        <p>📍 794 Mcallister St, San Francisco, 94102</p>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="footer-bottom">
                    <p>{t("All Rights Reserved ")}| <a href="/">{t("Terms and Conditions")}</a> | <a href="/">{t("Privacy Policy")}</a></p>
                </div>
            </footer>
        </>
    );
};

export default Page1;