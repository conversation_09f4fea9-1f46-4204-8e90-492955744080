import React, { useState, useEffect } from 'react';
import { <PERSON>u, Layout, Button, Typography, Tooltip, Divider } from 'antd';
import {
  MenuOutlined,
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  SettingOutlined,
  BellOutlined,
  LogoutOutlined,
  UsergroupAddOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { clearAuth } from '../../redux/Slice/authSlice';
import arabicLogo from '../../assets/arabic-logo.svg';
import './CustomSider.css';

const { Sider } = Layout;
const { Text } = Typography;

function getItem(label, key, icon, children) {
  return {
    key,
    icon,
    children,
    label,
  };
}

const getMenuItems = (isAdmin, t) => {
  const items = [
    // Main Navigation
    { type: 'group', label: t('MAIN NAVIGATION') },
    getItem(t('Dashboard'), 'dashboard', <DashboardOutlined />),
    getItem(t('My Orders'), 'orders', <ShoppingOutlined />),
    getItem(t('Manage Users'), 'manage-user', <UsergroupAddOutlined />),
    getItem(t('Quotations'), 'quotations', <FileTextOutlined />),
    
    // User Section
    { type: 'group', label: t('USER') },
    getItem(t('Notifications'), 'notifications', <BellOutlined />),
    getItem(t('Profile'), 'profile', <UserOutlined />),
    getItem(t('Settings'), 'settings', <SettingOutlined />),
  ];
  
  // Only add Admin section if user is an admin
  if (isAdmin) {
    items.push(
      // Admin Section
      { type: 'group', label: t('ADMIN') },
      getItem(t('Admin Panel'), 'admin', <AppstoreOutlined />)
    );
  }
  
  return items;
};

const CustomSider = ({ collapsed, setCollapsed }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const [hoverExpanded, setHoverExpanded] = useState(false);
  
  //redux 
  const { user } = useSelector((state) => state.auth);

  const roles = ['vendor', 'customer', 'freelancer', 'admin'];
  const userRoles = roles.filter(role => user?.realm_access?.roles.includes(role));

  const isAdmin = user?.realm_access?.roles.includes('admin');
  
  const effectiveCollapsed = collapsed && !hoverExpanded;

  const handleSignOut = () => {
    dispatch(clearAuth());
    navigate('/signin');
  };

  const handleMenuClick = (e) => {
    switch (e.key) {
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'orders':
        navigate('/orders');
        break;
      case 'manage-user':
        navigate('/manage-user');
        break;
      case 'quotations':
        navigate('/quotations');
        break;
      case 'notifications':
        navigate('/notifications');
        break;
      case 'profile':
        navigate('/profile');
        break;
      case 'admin':
        navigate('/admin');
        break;  
      case 'settings':
        navigate('/settings');
        break;
      default:
        break;
    }
  };

  const getSelectedKey = () => {
    const path = location.pathname.split('/')[1];
    switch (path) {
      case 'dashboard':
        return ['dashboard'];
      case 'orders':
        return ['orders'];
      case 'manage-user':
        return ['manage-user']; 
      case 'quotations':
        return ['quotations'];
      case 'notifications':
        return ['notifications'];
      case 'profile':
        return ['profile'];
      case 'admin':
        return ['admin']; 
      case 'settings':
        return ['settings'];
      default:
        return ['dashboard'];
    }
  };

  // Handle mouse enter/leave for hover expansion
  const handleMouseEnter = () => {
    if (collapsed) {
      setHoverExpanded(true);
    }
  };

  const handleMouseLeave = () => {
    setHoverExpanded(false);
    // Ensure sidebar collapses when mouse leaves
    if (!collapsed) {
      setCollapsed(true);
    }
  };

  return (
    <Sider
      collapsible
      collapsed={effectiveCollapsed}
      onCollapse={(value) => setCollapsed(value)}
      width={240}
      collapsedWidth={80}
      style={{
        background: '#fff',
        boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
        overflow: 'hidden',
        height: '100vh',
        position: 'fixed',
        left: 0,
        zIndex: 999,
        transition: 'all 0.3s ease',
      }}
      className={`sider-container ${collapsed ? 'collapsible-sider' : ''}`}
      trigger={null}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="sider-logo">
        <img
          src={arabicLogo}
          alt="Bond Logo"
          style={{
            maxWidth: effectiveCollapsed ? '80%' : '70%',
            transition: 'all 0.5s ease',
          }}
        />
        {!effectiveCollapsed && (
          <Button 
            type="text" 
            icon={<MenuOutlined />} 
            onClick={() => setCollapsed(!collapsed)}
            className="collapse-btn"
          />
        )}
      </div>

      <Divider className="sider-divider" />

      {!effectiveCollapsed && (
        <div className="user-profile-section">
          <div className="user-avatar">
            {user?.given_name?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div className="user-info">
            <Text strong className="user-name">{user?.given_name || 'User'}</Text>
            <Text type="secondary" className="user-role">{userRoles[0] || 'User'}</Text>
          </div>
        </div>
      )}

      <div className="menu-section">
        <Menu
          theme="light"
          selectedKeys={getSelectedKey()}
          mode="inline"
          items={getMenuItems(isAdmin, t)}
          onClick={handleMenuClick}
          style={{
            borderRight: 0,
            backgroundColor: 'transparent',
            transition: 'all 0.3s ease',
          }}
          className={effectiveCollapsed ? 'collapsed-menu' : ''}
        />
      </div>

      <div className="sider-footer">
        <Tooltip title={effectiveCollapsed ? t('Sign Out') : ''} placement="right">
          <Button
            type="primary"
            icon={<LogoutOutlined />}
            onClick={handleSignOut}
            className="sign-out-btn"
            danger
            ghost
          >
            {!effectiveCollapsed && t('Sign Out')}
          </Button>
        </Tooltip>
      </div>
    </Sider>
  );
};

export default CustomSider;