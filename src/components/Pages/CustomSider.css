.sider-container {
  position: relative;
  height: 100%;
  overflow: hidden;
  background-color: white !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  border-right: 1px solid #f0f0f0;
  z-index: 1000;
}

.collapsible-sider {
  transition: width 0.3s ease !important;
}

/* Ensure the hover expansion works smoothly */
.ant-layout-sider {
  transition: width 0.5s ease !important;
}

.ant-layout-sider-children {
  transition: opacity 0.2s ease;
}

.sider-logo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 16px;
  overflow: hidden;
}

.sider-logo img {
  height: 40px;
  transition: all 0.5s ease;
}

.collapse-btn {
  color: #9d3ca2;
  border: none;
  font-size: 16px;
  padding: 0;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.4s ease;
}

.collapse-btn:hover {
  background-color: rgba(157, 60, 162, 0.1);
}

.sider-divider {
  margin: 0 16px 16px;
  opacity: 0.6;
}

.user-profile-section {
  display: flex;
  align-items: center;
  padding: 0 16px 16px;
  margin-bottom: 8px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #9d3ca2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.user-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1e293b;
}

.user-role {
  font-size: 12px;
  color: #64748b;
}

.menu-section {
  height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 16px;
  padding: 0 8px;
}

.ant-menu-item {
  margin: 4px 0 !important;
  border-radius: 8px !important;
  height: 44px !important;
  line-height: 44px !important;
  padding-left: 16px !important;
  color: #475569 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.ant-menu-item .anticon {
  font-size: 18px !important;
  margin-right: 12px !important;
  color: #64748b !important;
  transition: all 0.2s ease !important;
}

.ant-menu-item-selected {
  background-color: #9d3ca2 !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(157, 60, 162, 0.2) !important;
}

.ant-menu-item-selected .anticon {
  color: white !important;
}

.ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: rgba(157, 60, 162, 0.08) !important;
  color: #9d3ca2 !important;
}

.ant-menu-item:hover:not(.ant-menu-item-selected) .anticon {
  color: #9d3ca2 !important;
}

.ant-menu-item-active {
  color: #9d3ca2 !important;
}

.ant-menu-item-group-title {
  padding: 16px 16px 8px !important;
  color: #94a3b8 !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  opacity: 0.8;
}

.sider-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  border-top: 1px solid #f1f5f9;
}

.sign-out-btn {
  width: 100%;
  border-radius: 8px !important;
  transition: all 0.3s ease;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-color: #ef4444 !important;
  color: #ef4444 !important;
}

.sign-out-btn:hover {
  background-color: #ef4444 !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
}

.sign-out-btn .anticon {
  font-size: 16px;
  margin-right: 8px;
}

.collapsed-menu-label {
  display: none;
}

.ant-layout-sider-collapsed .collapsed-menu-label {
  display: none;
}

/* Custom scrollbar for the menu */
.menu-section::-webkit-scrollbar {
  width: 4px;
}

.menu-section::-webkit-scrollbar-thumb {
  background-color: rgba(157, 60, 162, 0.2);
  border-radius: 4px;
}

.menu-section::-webkit-scrollbar-track {
  background-color: transparent;
}

/* Collapsed menu styles */
.collapsed-menu .ant-menu-item {
  padding: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100% !important;
  height: 56px !important;
}

.collapsed-menu .ant-menu-item .anticon {
  margin-right: 0 !important;
  font-size: 22px !important;
}

.collapsed-menu .ant-menu-title-content {
  display: none !important;
}

.ant-layout-sider-collapsed .ant-menu-item-group-title {
  display: none;
}

/* Hover expand animation */
.collapsible-sider:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* RTL Support */
[dir="rtl"] .sider-container {
  direction: rtl;
}
