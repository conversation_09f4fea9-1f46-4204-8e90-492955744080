import React, { useState } from 'react';
import logo from './Assets/arabic-logo.svg';
import { useTranslation } from 'react-i18next';
import blogs from '../../Data/Blog';
import metrics from '../../Data/KeyMatrices';
import testimonials from '../../Data/Testimonial';
import dealsData from '../../Data/Deals';
import categories from '../../Data/Technology';
import vendorsData from '../../Data/Vendors';
import customer from '../../Data/Customers';
import partners from '../../Data/Partners';

const Page1 = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "english", label: "English" },
        { code: "arabic", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        console.log('selected language:', language);
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };

    return (
        <>
            <header className="nav-bar">
                <div className="logo">
                    <img src={logo} alt="Bond" />
                </div>
                <input type="text" className="search-box" placeholder={t("Find Vendors, Services, or Products")} />
                <nav className="navMenu">
                    <button className='menu-button'>Home</button>
                    <button className='menu-button'>About</button>
                    <button className='menu-button'>Features</button>
                    <button className='menu-button'>Pricing</button>
                    <button className="btn-primary">{t("Post Project")}</button>
                    <button className="btn-secondary">{t("Vendor Signup")}</button>
                    <div className="dropdown-container">
                        <button
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            className="dropdown-toggle"
                            aria-haspopup="listbox"
                        >
                            <span>{selectedLanguage}</span>
                        </button>

                        {isDropdownOpen && (
                            <div className="dropdown-menu">
                                {languages.map((language) => (
                                    <div
                                        key={language.code}
                                        onClick={() => handleLanguageChange(language)}
                                        className={`dropdown-item ${language.label === selectedLanguage ? 'active' : ''}`}
                                    >
                                        {language.label}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </nav>
            </header>

            {/* Hero Section */}
            <section className="landing-container">
                <div className="hero-content">
                    <p className="hero-text">
                        {t("Find the Best")} <br />
                        <span className="highlight">{t("IT Services, Products & Solutions")}</span>
                    </p>
                    <div className="buttons">
                        <button className="post-project">{t("Post Your Project")}</button>
                        <button className="join-vendor">{t("Join as a Vendor")}</button>
                    </div>
                    <input type="text" className="search-box2" placeholder={t("Find Vendors, Services, or Products")} />
                </div>
                <img
                    src="https://plus.unsplash.com/premium_photo-1683288706157-9913483dffc8?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8ZGlnaXRhbCUyMHN0b3JlfGVufDB8fDB8fHww"
                    alt="IT Services"
                    className="hero-image"
                />
            </section>

            {/* Deals Section */}
            <div className="deal-container">
                <div className="d-header">
                    <div>
                        <h1 className="title">{t("Top Deals in the Market")}</h1>
                        <p className="subtitle">
                            {t("Discover the best deals and unbeatable discounts on top products and services in the market today!")}
                        </p>
                    </div>
                    <button className="explore-btn">{t("Explore all Deals →")}</button>
                </div>

                <div className="deal-grid">
                    {dealsData.map((deal) => (
                        <div key={deal.id} className="deal-card">
                            <div className="deal-header">{deal.company}</div>
                            <div className="deal-body">
                                <div className="deal-discount">
                                    {deal.discount} <span className="off-text">off</span>
                                </div>
                                <div className="deal-category">
                                    Category <br />
                                    <span className="deal-category-bold">{deal.category}</span>
                                </div>
                            </div>
                            <hr />
                            <div className="deal-footer">
                                <div className="offer-end">Offer Ends <br /> {deal.offerEnds}</div>
                                <button className="view-deal">View Deal</button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Vendors Section */}
            <div className="vendor-container">
                <div className="vendor-header">
                    <div>
                        <h1 className="title">{t("Top Vendors in the Market")}</h1>
                        <p className="subtitle">
                            {t("Ratings and reviews ensure transparency, helping you choose best service providers with confidence.")}
                        </p>
                    </div>
                    <button className="explore-btn">{t("Explore all Deals →")}</button>
                </div>
                <div className="vendor-grid">
                    {vendorsData.map((vendor) => (
                        <div key={vendor.id} className="vendor-card">
                            <div className="vendor-logo">
                                <img src={vendor.logo} alt={vendor.name} />
                            </div>
                            <div className="vendor-info">
                                <h3 className="vendor-name">{vendor.name}</h3>
                                <p className="vendor-category">{vendor.category}</p>
                                <div className="vendor-projects">
                                    Completed Projects: <strong>{vendor.projects}</strong>
                                    <span className="vendor-rating">
                                        {"★".repeat(vendor.rating)}
                                        {"☆".repeat(5 - vendor.rating)}
                                    </span>
                                </div>
                                <p className="vendor-description">{vendor.description}</p>
                                <button className="vendor-profile-btn">{t("View Profile")}</button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Customers Section */}
            <div className="customer-container">
                <div className="customer-head">
                    <div>
                        <h1 className="title">{t("Top Customers in the Market")}</h1>
                        <p className="subtitle">
                            {t(" Our top Customers - valued partners in our journey of growth, success, and innovation - whose trust and loyalty.")}
                        </p>
                    </div>
                    <button className="explore-btn">{t("Explore all Deals →")}</button>
                </div>

                <div className="customer-grid">
                    {customer.map((deal, index) => (
                        <div className="customer-card" key={index}>
                            <div className="customer-header" style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                <span>{deal.name}</span>
                                <img className="customer-logo" src={deal.logo} alt={deal.name} />
                            </div>
                            <div className="customer-body">
                                <p className="industry">{t("Industry")} <br></br> <strong>{deal.industry}</strong></p>
                                <div>
                                    <p className="projects">
                                        <span className="projects-count" style={{ fontSize: "28px", color: "#000", textAlign: "right" }}>
                                            {deal.projects}
                                        </span> {t("Projects")}
                                    </p>
                                </div>
                            </div>
                            <hr />
                            <div className="customer-footer">
                                <button className="view-details">{t("View Details")}</button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Technologies Section */}
            <div className="tech-container">
                <div className="tech-head">
                    <div>
                        <h1 className="title">{t("Top High-Demand Technology Categories")}</h1>
                        <p className="subtitle">
                            {t("Ratings and reviews ensure transparency, helping you choose the best service providers with confidence.")}
                        </p>
                    </div>
                </div>
                <div className="grid3">
                    {categories.map((category, index) => (
                        <div key={index} className="card3">
                            <img src={category.image} alt={category.title} className="card-img3" />
                            <div className="card-content3">
                                <h2 className="card-title3">{category.title}</h2>
                                <p className="card-subtitle3">{category.subtitle}</p>
                                <p className="card-description3">{category.description}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Partners Section */}
            <div className="partner-container">
                <div className="partner-header">
                    <h1 className="text">{t("Partners")}</h1>
                    <p className="subtext">
                        {t("Building Strong Alliances for Exceptional Results")}
                    </p>
                </div>
                <div className="grid4">
                    {partners.map((partner, index) => (
                        <div key={index} className="card4" style={{ backgroundColor: partner.bgColor }}>
                            <img src={partner.logo} alt={partner.name} className="logo4" />
                        </div>
                    ))};
                </div>
            </div>

            {/* Testimonials Section */}
            <div className="container5">
                <div className="header-container5">
                    <div>
                        <h2 className="heading5">{t("Testimonial")}</h2>
                        <p className="subtext5">{t("What Our Clients Say!")}</p>
                    </div>
                    <div className="buttons5">
                        <button className="button5">←</button>
                        <button className="button5">→</button>
                    </div>
                </div>
                <div className="carousel5">
                    {testimonials.map((testimonial, i) => (
                        <div key={i} className="card5">
                            <div className="header5">
                                <img src={testimonial.logo} alt={testimonial.name} className="logo" />
                                <div className="stars5">⭐️⭐️⭐️⭐️⭐️</div>
                            </div>
                            <p className="name5">{testimonial.name}</p>
                            <p className="role5">— {testimonial.role}</p>
                            <p className="text5">"{testimonial.text}"</p>
                        </div>
                    ))}
                </div>
            </div>


            {/* Blog Section */}
            <div className="blog-container">
                <div className="blog-header">
                    <div>
                        <h2 className="title">{t("Blog & Insights")}</h2>
                        <p className="subtitle">
                            {t(" Explore the latest trends, expert opinions, and industry innovations from our trusted partners.")}
                        </p>
                    </div>
                    <button className="explore-btn">
                        {t("Explore Blogs →")}
                    </button>
                </div>
                <div className="blog-grid">
                    {blogs.map((blog) => (
                        <div key={blog.id} className="blog-card">
                            <img src={blog.image} alt={blog.category} className="blog-image" />
                            <div className="blog-content">
                                <p className="blog-category">{blog.category}</p>
                                <h3 className="blog-heading">
                                    {blog.title} <a href={blog.link} className="blog-link">↗</a>
                                </h3>
                                <p className="blog-description">{blog.description}</p>
                                <p className="blog-date">Updated on <br /> <span>{blog.date}</span></p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Metrics Section */}
            <div className="metrics-container">
                <h2 className="metrics-title">{t("Key Metrics & Statistics")}</h2>
                <p className="metrics-subtext">
                    {t(" Measuring our impact through data-driven results and proven performance indicators.")}
                </p>
                <div className="metrics-grid">
                    {metrics.map((metric, index) => (
                        <div key={index} className="metric-card">
                            <div className="metric-header">
                                <h3>{metric.title}</h3>
                                <span className="metric-icon">{metric.icon}</span>
                            </div>
                            <div className="metric-value">
                                <strong>{metric.value}</strong>
                                <span className={`change ${metric.changeType}`}>
                                    {metric.changeType === "positive" ? "▲" : "▼"} {metric.change}
                                </span>
                            </div>
                            <p className="metric-subtext">vs. previous month</p>
                            <div className="progress-bar">
                                <div className="progress-fill" style={{ width: `${metric.progress}%` }}></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Footer */}
            <footer className="footer">
                <div className="footer-container">
                    {/* Left Section */}
                    <div className="footer-left">
                        <p className="footer-logo">  <img src={logo} alt="Bond" /> </p>
                        <p className="footer-text">
                            {t("  Lorem ipsum dolor sit amet consectetur adipiscing elit aliquam.")}
                        </p>
                        <div className="social-icons">
                            <span>📘</span>
                            <span>🐦</span>
                            <span>📸</span>
                            <span>🔗</span>
                            <span>▶️</span>
                        </div>
                    </div>

                    {/* Middle Sections */}
                    <div className="footer-links">
                        <div className="footer-column">
                            <h4>{t("Product")}</h4>
                            <ul>
                                <li>{t("Features")}</li>
                                <li>{t("Features")}</li>
                                <li>{t("Case studies")}</li>
                                <li>{t("Reviews")}</li>
                                <li>{t("Updates")}</li>
                            </ul>
                        </div>
                        <div className="footer-column">
                            <h4>{t("Company")}</h4>
                            <ul>
                                <li>{t("About")}</li>
                                <li>{t("Contact us")}</li>
                                <li>{t("Careers")}</li>
                                <li>{t("Culture")}</li>
                                <li>{t("Blog")}</li>
                            </ul>
                        </div>
                        <div className="footer-column">
                            <h4>{t("Support")}</h4>
                            <ul>
                                <li>{t("Getting started")}</li>
                                <li>{t("Help center")}</li>
                                <li>{t("Server status")}</li>
                                <li>{t("Report a bug")}</li>
                                <li>{t("Chat support")}</li>
                            </ul>
                        </div>
                    </div>

                    {/* Right Section */}
                    <div className="footer-right">
                        <h4>{t("Contacts us")}</h4>
                        <p>📧 <EMAIL></p>
                        <p>📞 (414) 687 - 5892</p>
                        <p>📍 794 Mcallister St, San Francisco, 94102</p>
                    </div>
                </div>

                {/* Bottom Section */}
                <div className="footer-bottom">
                    <p>{t("All Rights Reserved ")}| <a href="/">{t("Terms and Conditions")}</a> | <a href="/">{t("Privacy Policy")}</a></p>
                </div>
            </footer>
        </>
    );
};

export default Page1;