// UserTable.jsx
import React from 'react';
import { Table, Button, Tooltip } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import './UserTable.css';
import mockUsers from './mockUser'; // Adjust path as needed

const UserTable = ({ searchTerm }) => {
  const dataSource = mockUsers;

  const filteredData = searchTerm
    ? dataSource.filter(
        (user) =>
          user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.role.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : dataSource;

  const handleEdit = (record) => {
    console.log('Edit user:', record);
  };

  const handleDelete = (record) => {
    console.log('Delete user:', record);
  };

  const handleAddUser = () => {
    console.log('Add new user');
  };

  const columns = [
    {
      title: '',
      key: 'edit',
      render: (_, record) => (
        <Tooltip title="Edit User">
          <EditOutlined onClick={() => handleEdit(record)} className="edit-icon" />
        </Tooltip>
      ),
      width: 50,
    },
    {
      title: 'User Name',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '',
      key: 'delete',
      align: 'right',
      render: (_, record) => (
        <Tooltip title="Delete User">
          <DeleteOutlined onClick={() => handleDelete(record)} className="delete-icon" />
        </Tooltip>
      ),
      width: 50,
    },
  ];

  return (
    <div className="user-table-container">
      <div className="user-table-header">
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
          Add User
        </Button>
      </div>
      <Table dataSource={filteredData} columns={columns} pagination={false} />
    </div>
  );
};

export default UserTable;
