import React from 'react';
import { useTranslation } from 'react-i18next';
import './FontDemo.css';

const FontDemo = () => {
  const { t, i18n } = useTranslation();
  
  return (
    <div className="font-demo-container">
      <h1 className="font-demo-title">{t('fontDemo.title', 'Font Demonstration')}</h1>
      
      <div className="font-section">
        <h2>Inter Font Weights</h2>
        <p className="font-weight-400">This text uses Inter with weight 400 (Regular)</p>
        <p className="font-weight-500">This text uses Inter with weight 500 (Medium)</p>
        <p className="font-weight-600">This text uses Inter with weight 600 (SemiBold)</p>
        <p className="font-weight-700">This text uses Inter with weight 700 (Bold)</p>
        <p className="font-weight-800">This text uses Inter with weight 800 (ExtraBold)</p>
        <p className="font-weight-900">This text uses Inter with weight 900 (Black)</p>
      </div>
      
      <div className="font-section">
        <h2>Cairo Font (Arabic)</h2>
        <p className="cairo-regular">هذا النص يستخدم خط القاهرة العادي</p>
        <p className="cairo-bold">هذا النص يستخدم خط القاهرة الغامق</p>
      </div>
      
      <div className="font-section">
        <h2>Language Specific Fonts</h2>
        <p>
          {t('fontDemo.description', 'This text will use Inter in English and Cairo in Arabic automatically based on the language direction.')}
        </p>
      </div>
    </div>
  );
};

export default FontDemo;
