import React, { useState, useEffect } from 'react';
import '../Email/pages/EmailClient.css';
import Header from '../Header-Section/Header';
import Footer from '../Footer-Section/Footer';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { EmailClientPage } from '../Email/pages/EmailClientPage';
import VendorManagement from '../Admin/ProductManagement';
import CustomSider from '../Pages/CustomSider';
import { Layout } from 'antd';



const EmailFunc = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [selectedLanguage, setSelectedLanguage] = useState("English");
     const [collapsed, setCollapsed] = useState(false);
    const languages = [
        { code: "en", label: "English" },
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
    };

    useEffect(() => {
        document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    }, [i18n.language]);

    return (
        <div >
            <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
            <Layout
        style={{
          marginLeft: collapsed ? '80px' : '220px',
          transition: 'margin-left 0.3s',
        }}
      >
            <EmailClientPage/>
        </Layout>    
        </div>
    );
};

export default EmailFunc;
