import React from 'react';
import './LandingPage.css';
import Header from '../Header-Section/Header';
import TechnologyCategories from '../Technologies/TopTechnologies';
import Partners from '../Partners-Section/Partners';
import Testimonial from '../Testimonial-Section/Testimonial';
import BlogSection from '../Blog-section/BlogSection';
import MetricsSection from '../Metrics-Section/KeyMatrics';
import Footer from '../Footer-Section/Footer';
import VendorsTable from '../Vendors-section/VendorsTable';
import DealsTable from '../Deals-section/DealsTable';
import CustomerProjects from '../CustomersProjects/CustomersProjects';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const LandingPage2 = () => {
    const { t, i18n } = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "english", label: "English" },
        { code: "arabic", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        console.log('selected language:', language);
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
        setIsDropdownOpen(false);
    };
    return (
        <div>
            <Header />
            <section className="landing-container">
                <div className="hero-content">
                    <p className="hero-text">
                        {t("Find the Best")} <br />
                        <span className="highlight">{t("IT Services, Products & Solutions")}</span>
                    </p>
                    <div className="buttons">

                        <button className="join-vendor">{t("Join as a Vendor")}</button>
                    </div>
                    <input type="text" className="search-box2" placeholder={t("Find Vendors, Services, or Products")} />
                </div>
                <img
                    src="https://plus.unsplash.com/premium_photo-1683288706157-9913483dffc8?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8ZGlnaXRhbCUyMHN0b3JlfGVufDB8fDB8fHww"
                    alt="IT Services"
                    className="hero-image"
                />
            </section>
            <DealsTable />
            <VendorsTable />
            <CustomerProjects />
            <TechnologyCategories />
            <Partners />
            <Testimonial />
            <BlogSection />
            <MetricsSection />
            <Footer />
        </div>
    )
}

export default LandingPage2
