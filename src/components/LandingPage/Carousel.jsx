import React from 'react';
import { Carousel, Button, Typography } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';

const wrapperStyle = {
  height: '100vh',
  minHeight: '500px', // Ensure minimum height on very small screens
  margin: 0,
  padding: 0,
  overflow: 'hidden',
  position: 'relative'
};

const carouselStyle = {
  height: '100%',
  width: '100%',
};

const slideStyle = {
  height: '100vh',
  minHeight: '500px',
  display: 'flex',
  alignItems: 'center',
  width: '100%',
  justifyContent: 'center',
  fontSize: '2rem',
  color: '#fff',
  backgroundColor: '#364d79',
  position: 'relative',
};

const getTextOverlayStyle = () => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'flex-start',
  padding: window.innerWidth <= 768 ? '0 5%' : window.innerWidth <= 1024 ? '0 7%' : '0 10%',
  background: 'linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.5) 50%, rgba(0,0,0,0) 100%)',
  zIndex: 10,
  textAlign: 'left',
});

const getHeadingStyle = () => ({
  color: 'white',
  fontSize: window.innerWidth <= 480 ? '1.8rem' : 
            window.innerWidth <= 768 ? '2.5rem' : 
            window.innerWidth <= 1024 ? '3rem' : '3.5rem',
  fontWeight: 'bold',
  marginBottom: '1rem',
  maxWidth: window.innerWidth <= 768 ? '90%' : '600px',
  lineHeight: 1.2,
});

const getSubheadingStyle = () => ({
  color: 'white',
  fontSize: window.innerWidth <= 480 ? '1rem' : 
            window.innerWidth <= 768 ? '1.2rem' : 
            window.innerWidth <= 1024 ? '1.3rem' : '1.5rem',
  marginBottom: '1.5rem',
  maxWidth: window.innerWidth <= 768 ? '90%' : '500px',
  opacity: 0.9,
});

const getDescriptionStyle = () => ({
  color: 'white', 
  fontSize: window.innerWidth <= 480 ? '14px' : 
            window.innerWidth <= 768 ? '16px' : '18px',
  marginBottom: window.innerWidth <= 768 ? '1.5rem' : '2rem', 
  opacity: 0.8,
  maxWidth: window.innerWidth <= 768 ? '90%' : 'auto',
});

const getButtonStyle = () => ({
  height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',
  padding: window.innerWidth <= 480 ? '0 20px' : '0 30px',
  fontSize: window.innerWidth <= 480 ? '14px' : '16px',
  borderRadius: '8px',
  display: 'flex',
  alignItems: 'center',
  gap: '10px',
  fontWeight: 'bold',
});

const getMediaStyle = () => ({
  width: '100%', 
  height: '100%', 
  objectFit: 'cover',
  // Ensure proper scaling on mobile
  '@media (max-width: 768px)': {
    objectFit: 'cover',
    objectPosition: 'center',
  }
});

const { Title, Text } = Typography;

const carouselContent = [
  {
    image: "assets/landing_page_img_1.png",
    heading: "Let AI simplify the way you discover, select",
    subheading: "with vendors.",
    description: "AI-Driven Service Selection at Your Fingertips",
    buttonText: "Post Your Project",
    buttonVariant: "primary"
  },
  {
    image: "assets/landing_page_img_2.png",
    heading: "Connect with the right vendors for your business",
    subheading: "Find qualified professionals in minutes",
    description: "Our AI matching algorithm finds the perfect fit",
    buttonText: "Join as a Vendor",
    buttonVariant: "default"
  },
  {
    image: "assets/landing_page_img_3.png",
    heading: "Streamline your vendor selection process",
    subheading: "Save time and resources with our platform",
    description: "Efficient, transparent, and reliable",
    buttonText: "Learn More",
    buttonVariant: "primary"
  },
  {
    image: "assets/landing_page_img_4.png",
    heading: "Grow your business with our network",
    subheading: "Access to thousands of opportunities",
    description: "Join our community of successful businesses",
    buttonText: "Get Started",
    buttonVariant: "default"
  },
  {
    video:"assets/kafd_vid.mp4",
    heading: "Grow your business with our network",
    subheading: "Access to thousands of opportunities",
    description: "Join our community of successful businesses",
    buttonText: "Get Started",
    buttonVariant: "default"
  }
];

const TopCarousel = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  React.useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleButtonClick = () => {
    setLoading(true);
    // Simulate loading state for 1.5 seconds before navigation
    setTimeout(() => {
      navigate('/dashboard');
      setLoading(false);
    }, 1500);
  };

  return (
    <div style={wrapperStyle}>
      <Carousel
        autoplay
        autoplaySpeed={3000}
        dots={{
          className: 'custom-carousel-dots'
        }}
        style={carouselStyle}
        effect="fade"
        dotPosition={windowWidth <= 768 ? 'bottom' : 'bottom'}
      >
        {carouselContent.map((slide, index) => (
          <div key={index}>
            <div style={slideStyle}>
              {slide.video && (
                <video 
                  autoPlay 
                  loop 
                  muted 
                  playsInline // Important for mobile devices
                  style={getMediaStyle()}
                >
                  <source src={slide.video} type="video/mp4" />
                </video>
              )}
              {!slide.video && (
                <img 
                  style={getMediaStyle()} 
                  src={slide.image} 
                  alt={`Slide ${index + 1}`} 
                />
              )}
              <div style={getTextOverlayStyle()}>
                <Title style={getHeadingStyle()}>{slide.heading}</Title>
                <Title level={2} style={getSubheadingStyle()}>{slide.subheading}</Title>
                <Text style={getDescriptionStyle()}>
                  {slide.description}
                </Text>
                <Button 
                  type={slide.buttonVariant === 'primary' ? 'primary' : 'default'}
                  size={windowWidth <= 480 ? 'middle' : 'large'}
                  style={getButtonStyle()}
                  onClick={handleButtonClick}
                  loading={loading}
                >
                  {slide.buttonText}
                  <ArrowRightOutlined />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </Carousel>
      
      {/* Custom CSS for carousel dots responsiveness */}
      <style jsx>{`
        .custom-carousel-dots {
          bottom: 20px !important;
        }
        
        @media (max-width: 768px) {
          .custom-carousel-dots {
            bottom: 15px !important;
          }
          .custom-carousel-dots li {
            margin: 0 4px !important;
          }
          .custom-carousel-dots li button {
            width: 8px !important;
            height: 8px !important;
          }
        }
        
        @media (max-width: 480px) {
          .custom-carousel-dots {
            bottom: 10px !important;
          }
          .custom-carousel-dots li {
            margin: 0 3px !important;
          }
          .custom-carousel-dots li button {
            width: 6px !important;
            height: 6px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default TopCarousel;