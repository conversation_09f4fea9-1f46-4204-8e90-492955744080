import React, { useState, useEffect } from 'react';
import './LandingPage.css';
import Header from '../Header-Section/Header';
import Deals from '../Deals-section/TopDeals';
import TopVendors from '../Vendors-section/TopVendors';
import Customers from '../Customers-Section/Customers';
import TechnologyCategories from '../Technologies/TopTechnologies';
import Partners from '../Partners-Section/Partners';
import Testimonial from '../Testimonial-Section/Testimonial';
import BlogSection from '../Blog-section/BlogSection';
import MetricsSection from '../Metrics-Section/KeyMatrics';
import Footer from '../Footer-Section/Footer';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ForgetPassword from '../SignUpFlow/ForgetPassword';
import LandingCarousel from "./Carousel";

const LandingPage1 = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [selectedLanguage, setSelectedLanguage] = useState("English");

    const languages = [
        { code: "en", label: "English" },
        { code: "ar", label: "Arabic" }
    ];

    const handleLanguageChange = (language) => {
        i18n.changeLanguage(language.code);
        setSelectedLanguage(language.label);
    };

    useEffect(() => {
        document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    }, [i18n.language]);

    return (
        <div className="landing-page">
            <Header />
                <LandingCarousel />

            {/* <section className="landing-container">
                <div className="hero-content">
                    <p className="hero-text">
                        {t("Find the Best")} <br />
                        <span className="highlight">{t("IT Services, Products & Solutions")}</span>
                    </p>
                    <div className="buttons">
                        <button className="join-vendor" onClick={() => navigate('/signup')}>
                            {t("Join as a Vendor")}
                        </button>
                    </div>
                    <input
                        type="text"
                        className="search-box2"
                        placeholder={t("Find Vendors, Services, or Products")}
                    />
                </div>
                <img
                    src="https://plus.unsplash.com/premium_photo-1683288706157-9913483dffc8?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8ZGlnaXRhbCUyMHN0b3JlfGVufDB8fDB8fHww"
                    alt="IT Services"
                    className="hero-image"
                />
            </section> */}
            <Deals />
            <TopVendors />
            <Customers />
            <TechnologyCategories />
            <Partners />
            <Testimonial />
            <BlogSection />
            <MetricsSection />
            <Footer />
        </div>
    );
};

export default LandingPage1;
