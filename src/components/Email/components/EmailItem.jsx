// src/features/email/components/EmailItem.jsx
import React from 'react';

export function EmailItem({ email, isSelected, onClick, onReply }) {
  return (
    <div 
      className={`email-item ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="email-row">
        <div className="email-sender">{email.sender}</div>
        <div className="email-subject">{email.subject}</div>
        <div className="email-preview">{email.body.substring(0, 60)}...</div>
        <div className="email-date">{email.date}</div>
      </div>
      
      {isSelected && (
        <div className="email-content">
          <div className="email-full-header">
            <h2>{email.subject}</h2>
            <div className="email-metadata">
              <div className="sender-info">
                <span className="sender-name">{email.sender}</span>
                <span className="sender-date">{email.date}</span>
              </div>
            </div>
          </div>
          <div className="email-body">{email.body}</div>
          <div className="email-actions">
            {/* Added onClick handler to connect with reply function */}
            <button className="reply-btn" onClick={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              onReply(email);
            }}>Reply</button>
            <button className="forward-btn" onClick={(e) => e.stopPropagation()}>Forward</button>
          </div>
        </div>
      )}
    </div>
  );
}