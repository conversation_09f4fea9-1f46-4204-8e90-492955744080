// src/features/email/components/EmailDetail.jsx
import React from 'react';
import { Button, Typography, Avatar, Space, Divider, Empty, Card, Row, Col } from 'antd';
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  InboxOutlined,
  MailOutlined,
  ForwardOutlined,
  UserOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

export function EmailDetail({ email, onReply, onBack }) {
  if (!email) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px 20px',
        height: '100%'
      }}>
        <Empty
          description="No email selected"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ marginBottom: '24px' }}
        />
        <Button type="primary" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back to Inbox
        </Button>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <Button
          type="text"
          onClick={onBack}
          icon={<ArrowLeftOutlined />}
        >
          Back to Inbox
        </Button>
        <Space>
          <Button type="text" icon={<InboxOutlined />}>Archive</Button>
          <Button type="text" icon={<DeleteOutlined />} danger>Delete</Button>
        </Space>
      </div>

      <div style={{
        flex: 1,
        padding: '24px',
        overflow: 'auto'
      }}>
        <Card
          bordered={false}
          style={{
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            marginBottom: '24px'
          }}
        >
          <Title level={4} style={{ marginTop: 0, marginBottom: '16px' }}>
            {email.subject}
          </Title>

          <Row align="middle" style={{ marginBottom: '16px' }}>
            <Col>
              <Avatar
                size={48}
                icon={<UserOutlined />}
                style={{
                  backgroundColor: '#9e3ca2',
                  marginRight: '16px'
                }}
              >
                {email.sender.charAt(0).toUpperCase()}
              </Avatar>
            </Col>
            <Col flex="1">
              <Text strong style={{ display: 'block', fontSize: '16px' }}>
                {email.sender}
              </Text>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                to me
              </Text>
            </Col>
            <Col>
              <Text type="secondary">
                {email.date}
              </Text>
            </Col>
          </Row>

          <Divider style={{ margin: '0 0 16px 0' }} />

          <Paragraph
            style={{
              whiteSpace: 'pre-wrap',
              fontSize: '14px',
              lineHeight: '1.6',
              color: 'rgba(0, 0, 0, 0.85)'
            }}
          >
            {email.body}
          </Paragraph>
        </Card>

        <div style={{ marginTop: '24px' }}>
          <Space size="middle">
            <Button
              type="primary"
              icon={<MailOutlined />}
              onClick={onReply}
            >
              Reply
            </Button>
            <Button
              icon={<ForwardOutlined />}
            >
              Forward
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
}