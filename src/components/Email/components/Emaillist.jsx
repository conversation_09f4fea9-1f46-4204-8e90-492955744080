// src/features/email/components/EmailList.jsx
import React from 'react';
import { Button, Input, Empty, Badge, List, Typography, Space, Avatar } from 'antd';
import { ReloadOutlined, SearchOutlined, MailOutlined } from '@ant-design/icons';

const { Text } = Typography;

export function EmailList({ emails, onSelectEmail }) {
  return (
    <div className="email-list-view">
      <div style={{
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <Button
          icon={<ReloadOutlined />}
          type="text"
        >
          Refresh
        </Button>

        <Input
          prefix={<SearchOutlined />}
          placeholder="Search mail"
          style={{ width: '240px' }}
        />
      </div>

      <div style={{ padding: '0', overflow: 'auto' }}>
        {emails.length === 0 ? (
          <Empty
            description="Your inbox is empty"
            style={{ padding: '40px 0' }}
          />
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={emails}
            renderItem={email => (
              <List.Item
                key={email.id}
                onClick={() => onSelectEmail(email.id)}
                style={{
                  padding: '12px 16px',
                  cursor: 'pointer',
                  backgroundColor: !email.read ? 'rgba(158, 60, 162, 0.05)' : 'transparent',
                  borderBottom: '1px solid #f0f0f0',
                  transition: 'background-color 0.3s'
                }}
                className="email-list-item"
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      style={{
                        backgroundColor: !email.read ? '#9e3ca2' : '#d9d9d9',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                      icon={<MailOutlined />}
                    />
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text strong={!email.read}>{email.sender}</Text>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{email.date}</Text>
                    </div>
                  }
                  description={
                    <Space direction="vertical" size={0} style={{ width: '100%' }}>
                      <Text strong={!email.read} style={{ fontSize: '14px' }}>
                        {email.subject}
                      </Text>
                      <Text type="secondary" ellipsis style={{ fontSize: '13px' }}>
                        {email.body.substring(0, 80)}...
                      </Text>
                    </Space>
                  }
                />
                {!email.read && (
                  <Badge status="processing" color="#9e3ca2" style={{ marginLeft: '8px' }} />
                )}
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
}