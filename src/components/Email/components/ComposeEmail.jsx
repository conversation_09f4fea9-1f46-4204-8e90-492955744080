// src/features/email/components/ComposeEmail.jsx
import React from 'react';
import { Modal, Form, Input, Button, Typography } from 'antd';
import { SendOutlined, DeleteOutlined, CloseOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Title } = Typography;

export function ComposeEmail({ isOpen, composeData, onChange, onSubmit, onClose }) {
  if (!isOpen) return null;

  const isReply = composeData.subject.startsWith('RE:');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>{isReply ? 'Reply' : 'New Message'}</Title>
          <Button type="text" icon={<CloseOutlined />} onClick={onClose} />
        </div>
      }
      open={isOpen}
      footer={null}
      closable={false}
      width={600}
      style={{ top: 20 }}
      bodyStyle={{ padding: '16px' }}
    >
      <Form layout="vertical" onSubmitCapture={handleSubmit}>
        <Form.Item label="To:" required>
          <Input
            type="email"
            value={composeData.to}
            onChange={(e) => onChange({...composeData, to: e.target.value})}
            placeholder="<EMAIL>"
          />
        </Form.Item>

        <Form.Item label="Subject:" required>
          <Input
            value={composeData.subject}
            onChange={(e) => onChange({...composeData, subject: e.target.value})}
            placeholder="Email subject"
          />
        </Form.Item>

        <Form.Item required>
          <TextArea
            rows={10}
            placeholder="Compose email..."
            value={composeData.body}
            onChange={(e) => onChange({...composeData, body: e.target.value})}
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SendOutlined />}
            >
              Send
            </Button>
            <Button
              type="default"
              onClick={onClose}
              icon={<DeleteOutlined />}
            >
              Discard
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}