// src/features/email/hooks/useEmailClient.js
import { useState, useEffect } from 'react';
import { fetchEmails, sendEmail } from '../service/emailService';

export function useEmailClient() {
  const [emails, setEmails] = useState([]);
  const [selectedEmailId, setSelectedEmailId] = useState(null);
  const [showCompose, setShowCompose] = useState(false);
  const [composeData, setComposeData] = useState({
    to: '',
    subject: '',
    body: ''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch emails on component mount
  useEffect(() => {
    const getEmails = async () => {
      try {
        setLoading(true);
        const data = await fetchEmails();
        setEmails(data);
      } catch (err) {
        setError('Failed to load emails');
      } finally {
        setLoading(false);
      }
    };

    getEmails();
  }, []);

  // Get the selected email object
  const selectedEmail = emails.find(email => email.id === selectedEmailId);

  // Handle compose new email
  const handleCompose = (isReply = false) => {
    setShowCompose(true);

    if (isReply && selectedEmail) {
      setComposeData({
        to: selectedEmail.sender,
        subject: `RE: ${selectedEmail.subject}`,
        body: `\n\n-------- Original Message --------\nFrom: ${selectedEmail.sender}\nDate: ${selectedEmail.date}\nSubject: ${selectedEmail.subject}\n\n${selectedEmail.body}`
      });
    } else {
      setComposeData({ to: '', subject: '', body: '' });
    }
  };

  // Handle reply to email
  const handleReply = (email) => {
    handleCompose(true);
  };

  // Submit email
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await sendEmail(composeData);
      // Reset form
      setShowCompose(false);
      setComposeData({ to: '', subject: '', body: '' });

      // Add the sent email to the emails list (for demo purposes)
      const newEmail = {
        id: Date.now().toString(),
        sender: '<EMAIL>',
        recipient: composeData.to,
        subject: composeData.subject,
        body: composeData.body,
        date: new Date().toLocaleDateString(),
        folder: 'sent'
      };
      setEmails([newEmail, ...emails]);

    } catch (err) {
      setError('Failed to send email');
    }
  };

  const closeCompose = () => {
    setShowCompose(false);
  };

  return {
    emails,
    selectedEmailId,
    setSelectedEmailId,
    selectedEmail,
    showCompose,
    composeData,
    setComposeData,
    loading,
    error,
    handleCompose,
    handleReply,
    handleSubmit,
    closeCompose
  };
}