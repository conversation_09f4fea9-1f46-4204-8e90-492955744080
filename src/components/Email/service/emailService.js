// src/features/email/services/emailService.js

// API endpoints
const API = {
    getEmails: '/emails.json',
    sendEmail: '/api/send',
};

export async function fetchEmails() {
    try {
        const response = await fetch(API.getEmails);
        if (!response.ok) {
            throw new Error('Failed to fetch emails');
        }
        return await response.json();
    } catch (error) {
        console.error('Error loading emails:', error);
        throw error;
    }
}

export async function sendEmail(emailData) {
    try {
        // Replace with actual API implementation
        console.log('Sending email:', emailData);
        // Simulate API success
        return { success: true };
    } catch (error) {
        console.error('Error sending email:', error);
        throw error;
    }
}