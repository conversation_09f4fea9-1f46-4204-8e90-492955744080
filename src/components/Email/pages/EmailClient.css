/* src/features/email/styles/EmailClient.css */
:root {
  --primary-color: #1a73e8;
  --hover-color: #e8f0fe;
  --border-color: #adadad;
  --text-color: #202124;
  --secondary-text: #5f6368;
  --background-color: #f6f8fc;
  --selected-color: #c2e7ff;
}

/* Base Layout */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', Arial, sans-serif;
  color: var(--text-color);
  background-color: #ffffff;
}

.email-app {
  display: grid;
  grid-template-columns: 256px 1fr;
  min-height: 100vh;
  width: 170vh;
  align-items: center;
}

/* Integrated Email App */
.email-integrated-app {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.email-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
  z-index: 1;
}

.email-content {
  flex: 1;
  overflow: auto;
  background-color: #fff;
}

/* Email List Item Hover Effect */
.email-list-item:hover {
  background-color: rgba(158, 60, 162, 0.1) !important;
}

/* Sidebar */
.app-sidebar {
  background-color: var(--background-color);
  padding: 16px;
  height: 100vh;
  overflow-y: auto;
  border-right: 1px solid var(--border-color);
}

.compose-btn {
  background-color: var(--selected-color);
  color: var(--text-color);
  border: none;
  border-radius: 16px;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 20px;
  width: 100%;
  text-align: center;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
  transition: box-shadow 0.08s linear;
}

.compose-btn:hover {
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(60, 64, 67, 0.4);
}

.nav-menu {
  margin-top: 20px;
}

.nav-item {
  padding: 12px 16px;
  border-radius: 0 16px 16px 0;
  margin-right: 8px;
  cursor: pointer;
  font-size: 0.875rem;
}

.nav-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.nav-item.active {
  background-color: var(--selected-color);
  font-weight: 500;
}

/* Main Content Area */
.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Email List View */
.email-list-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.email-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
}

.refresh-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--secondary-text);
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
}

.refresh-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-icon {
  margin-right: 4px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  width: 250px;
  font-size: 0.875rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.emails-container {
  flex: 1;
  overflow-y: auto;
}

.empty-inbox {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--secondary-text);
  font-size: 1rem;
}

/* Email List Item */
.email-list-item {
  display: grid;
  grid-template-columns: 200px 1fr 120px;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  position: relative;
  transition: box-shadow 0.1s ease;
  min-height: 40px;
}

.email-list-item:hover {
  background-color: #e8e8e9;
  box-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60, 64, 67, 0.3);
  z-index: 1;
}

.email-sender {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.email-content-preview {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
}

.email-subject {
  font-weight: 500;
  margin-right: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.email-separator {
  margin: 0 6px;
  color: var(--secondary-text);
}

.email-preview {
  color: var(--secondary-text);
  overflow: hidden;
  text-overflow: ellipsis;
}

.email-date {
  text-align: right;
  color: var(--secondary-text);
  font-size: 0.75rem;
}

/* Email Detail View */
.email-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
}

.detail-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.back-to-inbox {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.back-to-inbox:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.back-icon {
  margin-right: 8px;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--secondary-text);
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.email-detail-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.email-detail-header {
  margin-bottom: 24px;
}

.detail-subject {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.detail-metadata {
  display: flex;
  align-items: center;
}

.sender-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--selected-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
}

.sender-info {
  flex: 1;
}

.sender-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.sender-address {
  font-size: 0.75rem;
  color: var(--secondary-text);
}

.email-detail-date {
  font-size: 0.75rem;
  color: var(--secondary-text);
}

.email-detail-body {
  padding: 16px 0;
  line-height: 1.5;
  white-space: pre-wrap;
}

.email-detail-actions {
  margin-top: 24px;
  display: flex;
  gap: 8px;
}

.primary-btn, .secondary-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
  border: none;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
}

.secondary-btn {
  background-color: var(--hover-color);
  color: var(--primary-color);
}

.detail-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--secondary-text);
  font-size: 1rem;
}

/* Compose Email Modal */
.compose-modal {
  position: fixed;
  bottom: 0;
  right: 24px;
  width: 600px;
  background: white;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  max-height: 85vh;
}

.compose-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #f2f6fc;
  border-radius: 8px 8px 0 0;
  color: var(--text-color);
}

.compose-header h3 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--secondary-text);
}

.compose-modal form {
  display: flex;
  flex-direction: column;
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.compose-field {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding: 8px 0;
}

.compose-field label {
  width: 80px;
  color: var(--secondary-text);
  font-size: 0.875rem;
}

.compose-field input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 0.875rem;
}

.compose-modal textarea {
  flex: 1;
  min-height: 300px;
  margin: 16px 0;
  padding: 8px;
  border: none;
  resize: none;
  outline: none;
  font-size: 0.875rem;
  font-family: inherit;
}

.compose-actions {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  padding-top: 8px;
}

.send-btn, .discard-btn {
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
  border: none;
}

.send-btn {
  background-color: var(--primary-color);
  color: white;
}

.send-btn:hover {
  background-color: #0b57d0;
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3);
}

.discard-btn {
  background-color: transparent;
  color: var(--secondary-text);
}

.discard-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Loading and Error States */
.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1rem;
}

.error {
  color: #d93025;
}