// src/features/email/pages/EmailClientPage.jsx
import React, { useState } from 'react';
import { Button, Spin, Alert, Card, Typography, Space } from 'antd';
import { PlusOutlined, MailOutlined } from '@ant-design/icons';
import { EmailList } from '../components/Emaillist';
import { EmailDetail } from '../components/EmailDetail';
import { ComposeEmail } from '../components/ComposeEmail';
import { useEmailClient } from '../hooks/useEmailClient';
import './EmailClient.css';

const { Title } = Typography;

export function EmailClientPage() {
  const {
    emails,
    selectedEmailId,
    setSelectedEmailId,
    selectedEmail,
    showCompose,
    composeData,
    setComposeData,
    loading,
    error,
    handleCompose,
    handleReply,
    handleSubmit,
    closeCompose
  } = useEmailClient();

  // Control view visibility
  const [currentView, setCurrentView] = useState('list'); // 'list' or 'detail'

  // Handle email selection
  const handleSelectEmail = (emailId) => {
    setSelectedEmailId(emailId);
    setCurrentView('detail');
  };

  // Handle back to inbox
  const handleBackToInbox = () => {
    setCurrentView('list');
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', padding: '40px' }}>
        <Spin size="large" tip="Loading emails..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="Error"
          description={error || "Failed to load emails. Please try again later."}
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <Card
      className="email-integrated-app"
      bordered={false}
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
      bodyStyle={{
        padding: '0',
        display: 'flex',
        flexDirection: 'column',
        flex: 1,
        overflow: 'hidden'
      }}
    >
      <div className="email-header" style={{
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Space>
          <MailOutlined style={{ fontSize: '20px', color: '#9e3ca2' }} />
          <Title level={4} style={{ margin: 0 }}>
            {currentView === 'list' ? 'Inbox' : 'Email Details'}
          </Title>
        </Space>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => handleCompose(false)}
        >
          Compose New Email
        </Button>
      </div>

      <div className="email-content" style={{
        flex: 1,
        overflow: 'auto',
        backgroundColor: '#fff'
      }}>
        {currentView === 'list' ? (
          <EmailList
            emails={emails}
            onSelectEmail={handleSelectEmail}
          />
        ) : (
          <EmailDetail
            email={selectedEmail}
            onReply={() => handleReply(selectedEmail)}
            onBack={handleBackToInbox}
          />
        )}
      </div>

      <ComposeEmail
        isOpen={showCompose}
        composeData={composeData}
        onChange={setComposeData}
        onSubmit={handleSubmit}
        onClose={closeCompose}
      />
    </Card>
  );
}