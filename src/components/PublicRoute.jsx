import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';

const PublicRoute = () => {
    const { isAuthenticated } = useSelector((state) => state.auth);

    // If user is authenticated, redirect to dashboard
    if (isAuthenticated) {
        return <Navigate to="/dashboard" replace />;
    }

    // If user is not authenticated, show the public route
    return <Outlet />;
};

export default PublicRoute;