import React, { useEffect, useState } from 'react';
import {
  Table, Button, Modal, Form, Input, InputNumber, Select, Space,
  Popconfirm, message
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined
} from '@ant-design/icons';
import { getReviews, createReview, updateReview, deleteReview, getCustomers, getServices, getProducts, getVendors } from './ApiWrapper';

import './OrdersStyles.css';

const { Option } = Select;
const { TextArea } = Input;

const ReviewManagement = () => {
  const [reviews, setReviews] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [services, setServices] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingReview, setEditingReview] = useState(null);
  const [form] = Form.useForm();
            const [filterCustomer, setFilterCustomer] = useState(null);
const [filterVendor, setFilterVendor] = useState(null);
const [filterProduct, setFilterProduct] = useState(null);
const [filterService, setFilterService] = useState(null);
const [filterRating, setFilterRating] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [reviewData, customerData, vendorData, serviceData, productData] = await Promise.all([
          getReviews(), getCustomers(), getVendors(), getServices(), getProducts()
        ]);
        setReviews(reviewData);
        setCustomers(customerData);
        setVendors(vendorData);
        setServices(serviceData);
        setProducts(productData);
        setLoading(false);
      } catch (error) {
        message.error('Failed to load data');
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingReview) {
            await updateReview(editingReview.review_id, values);
      }

else {
  await createReview(values);
}
      const updatedReviews = await getReviews();
      setReviews(updatedReviews);
      message.success(editingReview ? 'Review updated' : 'Review added');
      setIsModalVisible(false);
      form.resetFields();
      setEditingReview(null);
    } catch {
      message.error('Failed to save review');
    }
  };

  const handleEdit = (record) => {
    setEditingReview(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    await deleteReview(id);
    const updatedReviews = await getReviews();
    setReviews(updatedReviews);
    message.success('Review deleted');
  };

  const columns = [
    { title: 'ID', dataIndex: 'review_id', key: 'review_id' },
    {
      title: 'Customer',
      dataIndex: 'customer_id',
      render: id => customers.find(c => c.customer_id === id)?.name || 'Unknown'
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor_id',
      render: id => vendors.find(v => parseInt(v.vendor_id.slice(1)) === id)?.name || '—'
    },
    {
      title: 'Product',
      dataIndex: 'product_id',
      render: id => products.find(p => p.product_id === id)?.name || '—'
    },
    {
      title: 'Service',
      dataIndex: 'service_id',
      render: id => services.find(s => s.id === id)?.name || '—'
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      render: (rate) => `${rate} ★`
    },
    {
      title: 'Review',
      dataIndex: 'review_text'
    },
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      render: ts => new Date(ts).toLocaleString()
    },
    {
      title: 'Actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className="order-link"
          >
            Edit
          </Button>
          <Popconfirm title="Delete this review?" onConfirm={() => handleDelete(record.review_id)}>
            <Button type="link" icon={<DeleteOutlined />} danger>Delete</Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Review Management</h2>
            <p className="orders-subtitle">Manage customer feedback and reviews</p>
          </div>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => { setIsModalVisible(true); setEditingReview(null); form.resetFields(); }}>
            Add Review
          </Button>
        </div>
                <div style={{ marginBottom: 20 }}>
        <Space wrap>
            <Select
            placeholder="Filter by Customer"
            allowClear
            style={{ width: 180 }}
            value={filterCustomer}
            onChange={setFilterCustomer}
            >
            {customers.map(c => (
                <Option key={c.customer_id} value={c.customer_id}>{c.name}</Option>
            ))}
            </Select>

            <Select
            placeholder="Filter by Vendor"
            allowClear
            style={{ width: 180 }}
            value={filterVendor}
            onChange={setFilterVendor}
            >
            {vendors.map(v => (
                <Option key={v.vendor_id} value={parseInt(v.vendor_id.slice(1))}>{v.name}</Option>
            ))}
            </Select>

            <Select
            placeholder="Filter by Product"
            allowClear
            style={{ width: 180 }}
            value={filterProduct}
            onChange={setFilterProduct}
            >
            {products.map(p => (
                <Option key={p.product_id} value={p.product_id}>{p.name}</Option>
            ))}
            </Select>

            <Select
            placeholder="Filter by Service"
            allowClear
            style={{ width: 180 }}
            value={filterService}
            onChange={setFilterService}
            >
            {services.map(s => (
                <Option key={s.id} value={s.id}>{s.name}</Option>
            ))}
            </Select>

            <Select
            placeholder="Filter by Rating"
            allowClear
            style={{ width: 150 }}
            value={filterRating}
            onChange={setFilterRating}
            >
            {[5, 4, 3, 2, 1].map(r => (
                <Option key={r} value={r}>{r} ★</Option>
            ))}
            </Select>

            <Button onClick={() => {
            setFilterCustomer(null);
            setFilterVendor(null);
            setFilterProduct(null);
            setFilterService(null);
            setFilterRating(null);
            }}>
            Clear Filters
            </Button>
        </Space>
        </div>

        <Table
          className="orders-table"
          dataSource={reviews.filter(review => {
            return (
                (filterCustomer ? review.customer_id === filterCustomer : true) &&
                (filterVendor ? review.vendor_id === filterVendor : true) &&
                (filterProduct ? review.product_id === filterProduct : true) &&
                (filterService ? review.service_id === filterService : true) &&
                (filterRating ? Math.floor(review.rating) === filterRating : true)
            );
            })}
          columns={columns}
          rowKey="review_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          rowClassName={(_, index) => (index % 2 === 0 ? 'table-row-light' : 'table-row-dark')}
        />

        <Modal
          title={editingReview ? "Edit Review" : "Add Review"}
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={() => { setIsModalVisible(false); setEditingReview(null); }}
          destroyOnClose
        >
          <Form layout="vertical" form={form}>
            <Form.Item name="customer_id" label="Customer" rules={[{ required: true }]}>
              <Select placeholder="Select customer">
                {customers.map(c => (
                  <Option key={c.customer_id} value={c.customer_id}>{c.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="vendor_id" label="Vendor">
              <Select allowClear placeholder="Select vendor">
                {vendors.map(v => (
                  <Option key={v.vendor_id} value={parseInt(v.vendor_id.slice(1))}>{v.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="product_id" label="Product">
              <Select allowClear placeholder="Select product">
                {products.map(p => (
                  <Option key={p.product_id} value={p.product_id}>{p.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="service_id" label="Service">
              <Select allowClear placeholder="Select service">
                {services.map(s => (
                  <Option key={s.id} value={s.id}>{s.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="rating" label="Rating" rules={[{ required: true }]}>
              <InputNumber min={1} max={5} step={0.1} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item name="review_text" label="Review Text" rules={[{ required: true }]}>
              <TextArea rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default ReviewManagement;
