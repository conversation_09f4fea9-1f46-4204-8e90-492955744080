import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Popconfirm,
  Rate,
  Typography,
  message
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';

import {
  getVendorCategories as getCategories,
  getVendors,
  updateVendor,
  deleteVendor
} from './ApiWrapper';
import "./OrdersStyles.css";

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const VendorManagement = () => {
  const [vendors, setVendors] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingVendor, setEditingVendor] = useState(null);
  const [selectedCategories, setSelectedCategories] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [vendorsData, categoriesData] = await Promise.all([
          getVendors(),
          getCategories()
        ]);
        setVendors(vendorsData);
        setCategories(categoriesData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading data:', error);
        message.error('Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.categories = selectedCategories;

      const updatedVendor = await updateVendor(editingVendor.vendor_id, values);
      setVendors(vendors.map(v =>
        v.vendor_id === editingVendor.vendor_id ? updatedVendor : v
      ));
      message.success('Vendor updated successfully');

      setIsModalVisible(false);
      form.resetFields();
      setSelectedCategories([]);
    } catch (error) {
      console.error('Error saving vendor:', error);
      message.error('Failed to save vendor');
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setSelectedCategories([]);
  };

  const handleEdit = (record) => {
    setEditingVendor(record);
    form.setFieldsValue({ ...record });
    setSelectedCategories(record.categories);
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await deleteVendor(id);
      setVendors(vendors.filter(v => v.vendor_id !== id));
      message.success('Vendor deleted successfully');
    } catch (error) {
      console.error('Error deleting vendor:', error);
      message.error('Failed to delete vendor');
    }
  };

  const handleCategoryChange = (value) => {
    setSelectedCategories(value);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'vendor_id',
      key: 'vendor_id',
      width: 80
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name)
    },
    {
      title: 'Categories',
      dataIndex: 'categories',
      key: 'categories',
      render: (categories) => (
        <Space>
          {categories.map(category => (
            <span key={category} style={{
              background: '#f0f0f0',
              borderRadius: '4px',
              padding: '2px 8px',
              margin: '2px'
            }}>
              {category}
            </span>
          ))}
        </Space>
      ),
      filters: categories.map(cat => ({ text: cat, value: cat })),
      onFilter: (value, record) => record.categories.includes(value)
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => <Rate disabled defaultValue={rating} />,
      sorter: (a, b) => a.rating - b.rating
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location'
    },
    {
      title: 'Contact Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Contact Phone',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            className="order-link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this vendor?"
            onConfirm={() => handleDelete(record.vendor_id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="link"
              className="order-link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Vendor Management</h2>
            <p className="orders-subtitle">Manage your vendors and service providers</p>
          </div>
        </div>

        <Table
          className="orders-table"
          dataSource={vendors}
          columns={columns}
          rowKey="vendor_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1300 }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />

        <Modal
          title="Edit Vendor"
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={700}
          destroyOnClose
        >
          <Form form={form} layout="vertical">
            <Form.Item name="vendor_id" label="Vendor ID" className="form-item">
              <Input disabled />
            </Form.Item>

            <Form.Item name="name" label="Vendor Name" rules={[{ required: true }]}>
              <Input />
            </Form.Item>

            <Form.Item name="categories" label="Categories">
              <Select
                mode="multiple"
                onChange={handleCategoryChange}
                value={selectedCategories}
              >
                {categories.map(cat => (
                  <Option key={cat} value={cat}>{cat}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="rating" label="Rating">
              <Rate />
            </Form.Item>

            <Form.Item name="location" label="Location">
              <Input />
            </Form.Item>

            <Form.Item name="email" label="Contact Email">
              <Input />
            </Form.Item>

            <Form.Item name="phone" label="Contact Phone">
              <Input />
            </Form.Item>

            <Form.Item name="notes" label="Notes">
              <TextArea rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default VendorManagement;
