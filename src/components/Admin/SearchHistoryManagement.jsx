// SearchHistoryManagement.jsx
import React, { useEffect, useState } from 'react';
import { Table, Button, Space, Modal, Input, Popconfirm, message, Tag } from 'antd';
import { EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import { getSearchHistory, deleteSearchHistory } from './ApiWrapper';
import './OrdersStyles.css';

const SearchHistoryManagement = () => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchHistory = async () => {
    setLoading(true);
    try {
      const data = await getSearchHistory();
      setHistory(data);
    } catch (err) {
      message.error("Failed to load search history");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteSearchHistory(id);
      message.success("Deleted successfully");
      fetchHistory();
    } catch {
      message.error("Deletion failed");
    }
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  const columns = [
    {
      title: 'Search ID',
      dataIndex: 'search_id',
      key: 'search_id',
    },
    {
      title: 'Customer ID',
      dataIndex: 'customer_id',
      key: 'customer_id',
    },
    {
      title: 'Query',
      dataIndex: 'query',
      key: 'query',
    },
    {
      title: 'Filters',
      dataIndex: 'filters',
      key: 'filters',
      render: (filters) => (
        <Space size={[4, 4]} wrap>
            {Object.entries(filters).map(([key, value]) => (
            <Tag color="geekblue" key={key}>
                <strong>{key}:</strong> {value}
            </Tag>
            ))}
        </Space>
        )

    },
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (val) => new Date(val).toLocaleString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() =>
              Modal.info({
                title: `Search Details: #${record.search_id}`,
                content: (
                  <div>
                    <p><strong>Customer ID:</strong> {record.customer_id}</p>
                    <p><strong>Query:</strong> {record.query}</p>
                    <p><strong>Filters:</strong> {JSON.stringify(record.filters, null, 2)}</p>
                    <p><strong>Timestamp:</strong> {new Date(record.timestamp).toLocaleString()}</p>
                  </div>
                )
              })
            }
          >
            View
          </Button>
          <Popconfirm title="Delete this record?" onConfirm={() => handleDelete(record.search_id)}>
            <Button type="link" icon={<DeleteOutlined />} danger>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Search History</h2>
            <p className="orders-subtitle">Manage search history records</p>
          </div>
        </div>

        <Table
          className="orders-table"
          columns={columns}
          dataSource={history}
          rowKey="search_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1000 }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />
      </div>
    </div>
  );
};

export default SearchHistoryManagement;
