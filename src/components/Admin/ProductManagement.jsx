import React, { useState, useEffect } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Space, Popconfirm,
  message
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined
} from '@ant-design/icons';
import {
  getProductCategories as getCategories,
  getTags, getProducts,
  createProduct, updateProduct, deleteProduct
} from './ApiWrapper';

import "./OrdersStyles.css";

const { Option } = Select;
const { TextArea } = Input;

const ProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState({});
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingProduct, setEditingProduct] = useState(null);
  const [category, setCategory] = useState('');
  const [productOptions, setProductOptions] = useState([]);
  const [vendorOptions, setVendorOptions] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);

  // Filter states
  const [filterCategory, setFilterCategory] = useState(null);
  const [filterVendor, setFilterVendor] = useState(null);
  const [filterTag, setFilterTag] = useState(null);
  const [filterAvailability, setFilterAvailability] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [categoriesData, tagsData, productsData] = await Promise.all([
          getCategories(),
          getTags(),
          getProducts()
        ]);
        setCategories(categoriesData);
        setTags(tagsData);
        setProducts(productsData);
        setLoading(false);
      } catch (error) {
        message.error('Failed to load data');
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const showModal = () => {
    setEditingProduct(null);
    form.resetFields();
    setCategory('');
    setProductOptions([]);
    setVendorOptions([]);
    setSelectedTags([]);
    setIsModalVisible(true);
  };

  const handleCategoryChange = (value) => {
    setCategory(value);
    setProductOptions(categories[value]?.products || []);
    setVendorOptions(categories[value]?.vendors || []);
    form.setFieldsValue({ name: undefined, vendor_name: undefined });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.tags = selectedTags;
      values.category = category;

      if (editingProduct) {
        await updateProduct(editingProduct.product_id, values);
        message.success('Product updated successfully');
      } else {
        await createProduct(values);
        message.success('Product added successfully');
      }

      const updatedProducts = await getProducts();
      setProducts(updatedProducts);
      setIsModalVisible(false);
    } catch (error) {
      message.error('Failed to save product');
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setCategory('');
  };

  const handleEdit = (record) => {
    setEditingProduct(record);
    form.setFieldsValue(record);
    setCategory(record.category);
    setProductOptions(categories[record.category]?.products || []);
    setVendorOptions(categories[record.category]?.vendors || []);
    setSelectedTags(record.tags);
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await deleteProduct(id);
      const updatedProducts = await getProducts();
      setProducts(updatedProducts);
      message.success('Product deleted successfully');
    } catch (error) {
      console.error('Delete error:', error);
      message.error('Failed to delete product');
    }
  };

  // Apply filters to products
  const filteredProducts = products.filter((product) => {
    const categoryMatch = filterCategory ? product.category === filterCategory : true;
    const vendorMatch = filterVendor ? product.vendor_name === filterVendor : true;
    const tagMatch = filterTag ? product.tags.includes(filterTag) : true;
    const availabilityMatch =
      filterAvailability !== null ? product.availability === filterAvailability : true;
    return categoryMatch && vendorMatch && tagMatch && availabilityMatch;
  });

  const columns = [
    {
      title: 'Product',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="order-link">{text}</span>
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor_name',
      key: 'vendor_name'
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      render: (price) => `$${price.toLocaleString()}`
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <Space>
          {tags.map(tag => (
            <span key={tag} style={{
              background: '#f0f0f0',
              borderRadius: '4px',
              padding: '2px 8px',
              margin: '2px'
            }}>
              ✅ {tag}
            </span>
          ))}
        </Space>
      )
    },
    {
      title: 'Availability',
      dataIndex: 'availability',
      key: 'availability',
      render: (available) => (
        <span className={`status-badge ${available ? 'status-approved' : 'status-expired'}`}>
          {available ? 'Available' : 'Unavailable'}
        </span>
      )
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            className="order-link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete this product?"
            onConfirm={() => handleDelete(record.product_id)}
          >
            <Button
              type="link"
              className="order-link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Product Management</h2>
            <p className="orders-subtitle">Manage your available services and vendors</p>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showModal}
          >
            Add Product
          </Button>
        </div>

        {/* Filters */}
        <div style={{ marginBottom: 20 }}>
          <Space wrap>
            <Select
              placeholder="Filter by Category"
              allowClear
              style={{ width: 180 }}
              value={filterCategory}
              onChange={setFilterCategory}
            >
              {Object.keys(categories).map(cat => (
                <Option key={cat} value={cat}>{cat}</Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by Vendor"
              allowClear
              style={{ width: 180 }}
              value={filterVendor}
              onChange={setFilterVendor}
            >
              {[...new Set(products.map(p => p.vendor_name))].map(vendor => (
                <Option key={vendor} value={vendor}>{vendor}</Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by Tag"
              allowClear
              style={{ width: 180 }}
              value={filterTag}
              onChange={setFilterTag}
            >
              {tags.map(tag => (
                <Option key={tag} value={tag}>{tag}</Option>
              ))}
            </Select>

            <Select
              placeholder="Availability"
              allowClear
              style={{ width: 180 }}
              value={filterAvailability}
              onChange={value => setFilterAvailability(value)}
            >
              <Option value={true}>Available</Option>
              <Option value={false}>Unavailable</Option>
            </Select>

            <Button onClick={() => {
              setFilterCategory(null);
              setFilterVendor(null);
              setFilterTag(null);
              setFilterAvailability(null);
            }}>
              Clear Filters
            </Button>
          </Space>
        </div>

        {/* Product Table */}
        <Table
          className="orders-table"
          dataSource={filteredProducts}
          columns={columns}
          rowKey="product_id"
          loading={loading}
          pagination={false}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />

        {/* Modal */}
        <Modal
          title={editingProduct ? "Edit Product" : "Add Product"}
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={600}
        >
          <Form form={form} layout="vertical">
            <Form.Item name="category" label="Category" rules={[{ required: true }]}>
              <Select onChange={handleCategoryChange} placeholder="Select category">
                {Object.keys(categories).map(cat => (
                  <Option key={cat} value={cat}>{cat}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="name" label="Product Name" rules={[{ required: true }]}>
              <Select placeholder="Select product">
                {productOptions.map(product => (
                  <Option key={product} value={product}>{product}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="vendor_name" label="Vendor" rules={[{ required: true }]}>
              <Select placeholder="Select vendor">
                {vendorOptions.map(vendor => (
                  <Option key={vendor} value={vendor}>{vendor}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="price" label="Price" rules={[{ required: true }]}>
              <Input type="number" placeholder="Enter price" />
            </Form.Item>

            <Form.Item label="Tags">
              <Select
                mode="multiple"
                placeholder="Select tags"
                value={selectedTags}
                onChange={setSelectedTags}
              >
                {tags.map(tag => (
                  <Option key={tag} value={tag}>{tag}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="availability" label="Availability" valuePropName="checked">
              <Select>
                <Option value={true}>Available</Option>
                <Option value={false}>Unavailable</Option>
              </Select>
            </Form.Item>

            <Form.Item name="description" label="Description">
              <TextArea rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default ProductManagement;
