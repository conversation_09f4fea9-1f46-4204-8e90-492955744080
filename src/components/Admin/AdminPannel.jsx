import React, { useState } from 'react';
import { Layout, Menu, Typography, Breadcrumb, theme } from 'antd';
import {
  StarOutlined,
  AppstoreOutlined,
  ShoppingOutlined,
  UserOutlined,
  TeamOutlined,
  HomeOutlined
} from '@ant-design/icons';
import VendorManagement from './VendorManagement';
import ProductManagement from './ProductManagement';
import CustomerManagement from './CustomerManagement';
import ServicesManagement from './ServiceManagement';
import SearchHistoryManagement from './SearchHistoryManagement';
import Dashboard from './AdminDash';
import ReviewManagement from './ReviewManagement';
import CustomSider from '../Pages/CustomSider';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

const AdminPannel = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const { token } = theme.useToken();
  const [collapsed, setCollapsed] = useState(false);

const renderContent = () => {
  switch (currentPage) {
    case 'dashboard':
      return <Dashboard />;
    case 'vendors':
      return <VendorManagement />;
    case 'products':
      return <ProductManagement />;
    case 'customers':
      return <CustomerManagement />;
    case 'services': // Add this case
      return <ServicesManagement />;
      case 'reviews':
  return <ReviewManagement />;
  case 'searchHistory':
  return <SearchHistoryManagement />;
    default:
      return <Dashboard />;
  }
};

const getPageTitle = () => {
  switch (currentPage) {
    case 'dashboard':
      return 'Dashboard';
    case 'vendors':
      return 'Vendor Management';
    case 'products':
      return 'Product Management';
    case 'customers':
      return 'Customer Management';
    case 'services': // Add this case
      return 'Service Management';
    case 'reviews':
  return 'Review Management';
    case 'searchHistory':
  return 'Search History';
    default:
      return 'Dashboard';
  }
};

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout style={{
        marginLeft: collapsed ? '80px' : '220px',
        transition: 'margin-left 0.3s'
      }}>
        <Header
          style={{
            background: token.colorBgContainer,
            position: 'sticky',
            top: 0,
            zIndex: 1,
            display: 'flex',
            alignItems: 'center',
            padding: '0 24px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
          }}
        >
          <Title level={4} style={{ margin: 0, marginRight: 'auto' }}>
            Enterprise Admin System
          </Title>
          <Menu
            mode="horizontal"
            selectedKeys={[currentPage]}
            onClick={({ key }) => setCurrentPage(key)}
            style={{ flexGrow: 1, justifyContent: 'flex-end', border: 'none' }}
            items={[
  {
    key: 'dashboard',
    icon: <HomeOutlined />,
    label: 'Dashboard',
  },
  {
    key: 'vendors',
    icon: <TeamOutlined />,
    label: 'Vendors',
  },
  {
    key: 'products',
    icon: <ShoppingOutlined />,
    label: 'Products',
  },
  {
    key: 'customers',
    icon: <UserOutlined />,
    label: 'Customers',
  },
  {
    key: 'services', // Add this menu item
    icon: <AppstoreOutlined />,
    label: 'Services',
  },
{
  key: 'reviews',
  icon: <StarOutlined />,
  label: 'Reviews',
},
{
  key: 'searchHistory',
  icon: <AppstoreOutlined />,
  label: 'Search History',
}

]}
          />
        </Header>

        <Content style={{ margin: '16px', backgroundColor: '#f5f7fa' }}>
          <Breadcrumb style={{ margin: '16px 0' }}>
            <Breadcrumb.Item>Admin</Breadcrumb.Item>
            <Breadcrumb.Item>{getPageTitle()}</Breadcrumb.Item>
          </Breadcrumb>
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: token.colorBgContainer,
              borderRadius: token.borderRadiusLG,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            {renderContent()}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center', backgroundColor: token.colorBgContainer }}>
          Enterprise Admin System Â©{new Date().getFullYear()} Created by Your Company
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AdminPannel;