// ApiWrapper.js
import productsData from '../../mock-data/products.json';
import vendorsData from '../../mock-data/vendors.json';
import customersData from '../../mock-data/customers.json';
import servicesData from '../../mock-data/services.json';
import categoriesData from '../../mock-data/categories.json';
import reviewsData from '../../mock-data/reviews.json';
import searchHistoryData from '../../mock-data/search_history.json';



const API_DELAY = 500; // Simulated API delay in milliseconds

// Initialize in-memory data stores
let inMemoryProducts = [...productsData.products];
let inMemoryVendors = [...vendorsData.vendors];
let inMemoryCustomers = [...customersData.customers];
let inMemoryServices = [...servicesData]; 
let inMemoryCategories = [...categoriesData.categories];
let inMemoryReviews = [...reviewsData.reviews];
let inMemorySearchHistory = [...searchHistoryData];


// Product Operations
export const getProductCategories = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve(productsData.categories), API_DELAY
  ));
};

export const getTags = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve(productsData.tags), API_DELAY
  ));
};

export const getProducts = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve([...inMemoryProducts]), API_DELAY
  ));
};

export const createProduct = (productData) => {
  return new Promise(resolve => {
    setTimeout(() => {
      const lastId = inMemoryProducts.length > 0 ? 
        parseInt(inMemoryProducts[inMemoryProducts.length - 1].product_id.slice(1)) : 0;
      const product_id = `P${String(lastId + 1).padStart(3, '0')}`;
      
      const newProduct = {
        ...productData,
        product_id,
        added_date: new Date().toISOString().split('T')[0]
      };
      inMemoryProducts.push(newProduct);
      resolve(newProduct);
    }, API_DELAY);
  });
};

export const updateProduct = (productId, productData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = inMemoryProducts.findIndex(p => p.product_id === productId);
      if (index === -1) reject(new Error('Product not found'));
      
      inMemoryProducts[index] = {
        ...inMemoryProducts[index],
        ...productData,
        product_id: productId
      };
      resolve(inMemoryProducts[index]);
    }, API_DELAY);
  });
};

export const deleteProduct = (productId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemoryProducts = inMemoryProducts.filter(p => p.product_id !== productId);
      resolve();
    }, API_DELAY);
  });
};

// Vendor Operations
export const getVendorCategories = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve(vendorsData.categories), API_DELAY
  ));
};

export const getVendors = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve([...inMemoryVendors]), API_DELAY
  ));
};



export const updateVendor = (vendorId, vendorData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = inMemoryVendors.findIndex(v => v.vendor_id === vendorId);
      if (index === -1) reject(new Error('Vendor not found'));
      
      inMemoryVendors[index] = {
        ...vendorData,
        vendor_id: vendorId
      };
      resolve(inMemoryVendors[index]);
    }, API_DELAY);
  });
};

export const deleteVendor = (vendorId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemoryVendors = inMemoryVendors.filter(v => v.vendor_id !== vendorId);
      resolve();
    }, API_DELAY);
  });
};

// Customer Operations
export const getCustomers = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve([...inMemoryCustomers]), API_DELAY
  ));
};



export const updateCustomer = (customerId, customerData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = inMemoryCustomers.findIndex(c => c.customer_id === customerId);
      if (index === -1) reject(new Error('Customer not found'));
      
      inMemoryCustomers[index] = {
        ...inMemoryCustomers[index],
        ...customerData,
        customer_id: customerId
      };
      resolve(inMemoryCustomers[index]);
    }, API_DELAY);
  });
};

export const deleteCustomer = (customerId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemoryCustomers = inMemoryCustomers.filter(c => c.customer_id !== customerId);
      resolve();
    }, API_DELAY);
  });
};
// Service Operations
export const getServices = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve([...inMemoryServices]), API_DELAY
  ));
};

export const createService = (serviceData) => {
  return new Promise(resolve => {
    setTimeout(() => {
      const lastId = inMemoryServices.length > 0 ? 
        Math.max(...inMemoryServices.map(s => s.id)) : 0;
      
      const newService = {
        id: lastId + 1,
        category_id: serviceData.category_id,
        vendor_id: serviceData.vendor_id,
        name: serviceData.name,
        slug: serviceData.name.toLowerCase().replace(/\s+/g, '-'),
        description: serviceData.description || null,
        price_model: serviceData.price_model || 'Per Project',
        base_price: parseFloat(serviceData.base_price) || null,
        icon_url: serviceData.icon_url || null,
        is_active: serviceData.is_active !== undefined ? serviceData.is_active : true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      inMemoryServices.push(newService);
      resolve(newService);
    }, API_DELAY);
  });
};

export const updateService = (serviceId, serviceData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = inMemoryServices.findIndex(s => s.id === parseInt(serviceId));
      if (index === -1) reject(new Error('Service not found'));
      
      inMemoryServices[index] = {
        ...inMemoryServices[index],
        ...serviceData,
        slug: serviceData.name ? serviceData.name.toLowerCase().replace(/\s+/g, '-') : inMemoryServices[index].slug,
        updated_at: new Date().toISOString()
      };
      resolve(inMemoryServices[index]);
    }, API_DELAY);
  });
};

export const deleteService = (serviceId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemoryServices = inMemoryServices.filter(s => s.id !== parseInt(serviceId));
      resolve();
    }, API_DELAY);
  });
};

export const getServiceCategories = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve([...inMemoryCategories]), API_DELAY
  ));
};

//REviews Opertaions 

export const getReviews = () => {
  return new Promise(resolve =>
    setTimeout(() => resolve([...inMemoryReviews]), 500)
  );
};

export const createReview = (reviewData) => {
  return new Promise(resolve => {
    setTimeout(() => {
      const newId = Math.max(0, ...inMemoryReviews.map(r => r.review_id)) + 1;
      const newReview = {
        ...reviewData,
        review_id: newId,
        timestamp: new Date().toISOString()
      };
      inMemoryReviews.push(newReview);
      resolve(newReview);
    }, 500);
  });
};
export const updateReview = (reviewId, reviewData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = inMemoryReviews.findIndex(r => r.review_id === reviewId);
      if (index === -1) return reject(new Error("Review not found"));

      inMemoryReviews[index] = {
        ...inMemoryReviews[index],
        ...reviewData,
        review_id: reviewId,
        timestamp: new Date().toISOString()
      };
      resolve(inMemoryReviews[index]);
    }, 500);
  });
};


export const deleteReview = (reviewId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemoryReviews = inMemoryReviews.filter(r => r.review_id !== reviewId);
      resolve();
    }, 500);
  });
};

// Search History Operations
export const getSearchHistory = () => {
  return new Promise(resolve =>
    setTimeout(() => resolve([...inMemorySearchHistory]), 500)
  );
};

export const deleteSearchHistory = (searchId) => {
  return new Promise(resolve => {
    setTimeout(() => {
      inMemorySearchHistory = inMemorySearchHistory.filter(s => s.search_id !== searchId);
      resolve();
    }, 500);
  });
};

// Common
export const getCategories = () => {
  return new Promise(resolve => 
    setTimeout(() => resolve(productsData.categories), API_DELAY
  ));
};
