import React, { useState, useEffect } from 'react';
import { 
  Table, Button, Modal, Form, Input, Select, Space, Popconfirm, Tag,
  Typography, Avatar, message 
} from 'antd';
import { 
  EditOutlined, DeleteOutlined, UserOutlined,
  MailOutlined, PhoneOutlined, EnvironmentOutlined, DollarOutlined 
} from '@ant-design/icons';
import { 
  getCustomers, updateCustomer, deleteCustomer 
} from './ApiWrapper';
import "./OrdersStyles.css";

const { Option } = Select;
const { Text } = Typography;

const PREFERENCES = [
  'Cloud Services', 'Web Services', 'Security Services',
  'Hardware', 'Software', 'Consulting', 'Support'
];

const BUDGET_RANGES = [
  { label: 'Low (₹1,000 - ₹10,000)', value: { min: 1000, max: 10000 } },
  { label: 'Medium (₹10,001 - ₹50,000)', value: { min: 10001, max: 50000 } },
  { label: 'High (₹50,001 - ₹200,000)', value: { min: 50001, max: 200000 } },
  { label: 'Enterprise (₹200,001+)', value: { min: 200001, max: null } }
];

const getBudgetRangeLabel = (budget) => {
  if (!budget) return '';
  const range = BUDGET_RANGES.find(r => 
    r.value.min === budget.min && r.value.max === budget.max
  );
  return range ? range.label : '';
};

const CustomerManagement = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [selectedPreferences, setSelectedPreferences] = useState([]);
  const [selectedBudgetRange, setSelectedBudgetRange] = useState(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        const data = await getCustomers();
        setCustomers(data);
      } catch (error) {
        message.error('Failed to load customers');
      } finally {
        setLoading(false);
      }
    };
    fetchCustomers();
  }, []);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.preferences = selectedPreferences;
      values.budget_range = selectedBudgetRange;

      const updatedCustomer = await updateCustomer(editingCustomer.customer_id, values);
      setCustomers(customers.map(c => 
        c.customer_id === editingCustomer.customer_id ? updatedCustomer : c
      ));
      message.success('Customer updated successfully');

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Failed to save customer');
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setSelectedPreferences([]);
    setSelectedBudgetRange(null);
  };

  const handleEdit = (record) => {
    setEditingCustomer(record);
    form.setFieldsValue(record);
    setSelectedPreferences(record.preferences);
    setSelectedBudgetRange(record.budget_range);
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await deleteCustomer(id);
      setCustomers(customers.filter(c => c.customer_id !== id));
      message.success('Customer deleted successfully');
    } catch (error) {
      message.error('Failed to delete customer');
    }
  };

  const handlePreferenceChange = (value) => {
    setSelectedPreferences(value);
  };

  const handleBudgetRangeChange = (value) => {
    const selectedRange = BUDGET_RANGES.find(r => r.label === value);
    setSelectedBudgetRange(selectedRange?.value);
  };

  const getBudgetTagColor = (budget) => {
    if (!budget) return 'default';
    if (budget.max === null) return 'purple';
    if (budget.min >= 50001) return 'red';
    if (budget.min >= 10001) return 'orange';
    return 'green';
  };

  const columns = [
    { 
      title: 'ID', 
      dataIndex: 'customer_id', 
      key: 'customer_id',
      width: 100
    },
    { 
      title: 'Customer Name', 
      dataIndex: 'name', 
      key: 'name',
      render: (text) => (
        <span>
          <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
          <Text strong>{text}</Text>
        </span>
      ),
      sorter: (a, b) => a.name.localeCompare(b.name)
    },
    { 
      title: 'Contact Info', 
      key: 'contact',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div><MailOutlined /> {record.email}</div>
          <div><PhoneOutlined /> {record.phone}</div>
        </Space>
      )
    },
    { 
      title: 'Location', 
      dataIndex: 'location', 
      key: 'location',
      render: (text) => (
        <div><EnvironmentOutlined /> {text}</div>
      )
    },
    { 
      title: 'Preferences', 
      dataIndex: 'preferences', 
      key: 'preferences',
      render: (preferences) => (
        <Space size="small">
          {preferences.map(pref => (
            <Tag color="blue" key={pref}>{pref}</Tag>
          ))}
        </Space>
      ),
      filters: PREFERENCES.map(pref => ({ text: pref, value: pref })),
      onFilter: (value, record) => record.preferences.includes(value)
    },
    { 
      title: 'Budget Range', 
      dataIndex: 'budget_range', 
      key: 'budget_range',
      render: (budget) => (
        <Tag color={getBudgetTagColor(budget)}>
          <DollarOutlined /> {getBudgetRangeLabel(budget)}
        </Tag>
      ),
      filters: BUDGET_RANGES.map(range => ({ text: range.label, value: range.label })),
      onFilter: (value, record) => getBudgetRangeLabel(record.budget_range) === value
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            className="order-link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete this customer?"
            onConfirm={() => handleDelete(record.customer_id)}
            okText="Yes"
            cancelText="No"
          >
            <Button 
              type="link" 
              className="order-link"
              danger 
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Customer Management</h2>
            <p className="orders-subtitle">Manage your enterprise customers</p>
          </div>
        </div>

        <Table
          className="orders-table"
          dataSource={customers}
          columns={columns}
          rowKey="customer_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1500 }}
          rowClassName={(_, index) => 
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />

        <Modal
          title="Edit Customer"
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={700}
          destroyOnClose
        >
          <Form form={form} layout="vertical">
            <Form.Item name="customer_id" label="Customer ID">
              <Input disabled />
            </Form.Item>

            <Form.Item
              name="name"
              label="Customer Name"
              rules={[{ required: true, message: 'Please enter customer name' }]}
            >
              <Input placeholder="Enter customer name" />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter email' },
                { type: 'email', message: 'Invalid email format' }
              ]}
            >
              <Input placeholder="Enter customer email" />
            </Form.Item>

            <Form.Item
              name="phone"
              label="Phone"
              rules={[{ required: true, message: 'Please enter phone number' }]}
            >
              <Input placeholder="Enter customer phone number" />
            </Form.Item>

            <Form.Item
              name="location"
              label="Location"
              rules={[{ required: true, message: 'Please enter location' }]}
            >
              <Input placeholder="Enter customer location" />
            </Form.Item>

            <Form.Item
              label="Preferences"
              rules={[{ required: true, message: 'Please select at least one preference' }]}
            >
              <Select
                mode="multiple"
                placeholder="Select preferences"
                value={selectedPreferences}
                onChange={handlePreferenceChange}
              >
                {PREFERENCES.map(pref => (
                  <Option key={pref} value={pref}>{pref}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="Budget Range"
              rules={[{ required: true, message: 'Please select budget range' }]}
            >
              <Select
                placeholder="Select budget range"
                onChange={handleBudgetRangeChange}
                value={selectedBudgetRange ? getBudgetRangeLabel(selectedBudgetRange) : undefined}
              >
                {BUDGET_RANGES.map(range => (
                  <Option key={range.label} value={range.label}>
                    {range.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default CustomerManagement;