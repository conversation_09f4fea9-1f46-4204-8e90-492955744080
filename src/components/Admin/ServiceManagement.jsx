import React, { useState, useEffect } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Space, Popconfirm,
  message, InputNumber, Switch, Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined
} from '@ant-design/icons';
import {
  getServices, createService, updateService, deleteService,
  getServiceCategories, getVendors
} from './ApiWrapper';

import "./OrdersStyles.css";

const { Option } = Select;
const { TextArea } = Input;

const PRICE_MODELS = [
  'Per Project',
  'Monthly',
  'Yearly',
  'Per Hour',
  'Per Assessment',
  'Per Implementation',
  'One-time'
];

const ServiceManagement = () => {
  const [services, setServices] = useState([]);
  const [categories, setCategories] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingService, setEditingService] = useState(null);

  // Filter states
  const [filterCategory, setFilterCategory] = useState(null);
  const [filterVendor, setFilterVendor] = useState(null);
  const [filterPriceModel, setFilterPriceModel] = useState(null);
  const [filterStatus, setFilterStatus] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [servicesData, categoriesData, vendorsData] = await Promise.all([
          getServices(),
          getServiceCategories(),
          getVendors()
        ]);
        setServices(servicesData);
        setCategories(categoriesData);
        setVendors(vendorsData);
        setLoading(false);
      } catch (error) {
        message.error('Failed to load data');
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const showModal = () => {
    setEditingService(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingService) {
        await updateService(editingService.id, values);
        message.success('Service updated successfully');
      } else {
        await createService(values);
        message.success('Service added successfully');
      }

      const updatedServices = await getServices();
      setServices(updatedServices);
      setIsModalVisible(false);
    } catch (error) {
      console.error('Save error:', error);
      message.error('Failed to save service');
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleEdit = (record) => {
    setEditingService(record);
    form.setFieldsValue({
      ...record,
      base_price: record.base_price || undefined
    });
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await deleteService(id);
      const updatedServices = await getServices();
      setServices(updatedServices);
      message.success('Service deleted successfully');
    } catch (error) {
      console.error('Delete error:', error);
      message.error('Failed to delete service');
    }
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  const getVendorName = (vendorId) => {
    const vendor = vendors.find(v => v.vendor_id === `V${String(vendorId).padStart(3, '0')}`);
    return vendor ? vendor.name : 'Unknown';
  };

  // Apply filters to services
  const filteredServices = services.filter((service) => {
    const categoryMatch = filterCategory ? service.category_id === filterCategory : true;
    const vendorMatch = filterVendor ? service.vendor_id === filterVendor : true;
    const priceModelMatch = filterPriceModel ? service.price_model === filterPriceModel : true;
    const statusMatch = filterStatus !== null ? service.is_active === filterStatus : true;
    return categoryMatch && vendorMatch && priceModelMatch && statusMatch;
  });

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      sorter: (a, b) => a.id - b.id
    },
    {
      title: 'Service Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="order-link">{text}</span>,
      sorter: (a, b) => a.name.localeCompare(b.name)
    },
    {
      title: 'Category',
      dataIndex: 'category_id',
      key: 'category_id',
      render: (categoryId) => (
        <Tag color="blue">{getCategoryName(categoryId)}</Tag>
      )
    },
    {
      title: 'Vendor',
      dataIndex: 'vendor_id',
      key: 'vendor_id',
      render: (vendorId) => getVendorName(vendorId)
    },
    {
      title: 'Price Model',
      dataIndex: 'price_model',
      key: 'price_model',
      render: (model) => (
        <Tag color="orange">{model}</Tag>
      )
    },
    {
      title: 'Base Price',
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price) => price ? `₹${price.toLocaleString()}` : 'N/A',
      sorter: (a, b) => (a.base_price || 0) - (b.base_price || 0)
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <span className={`status-badge ${active ? 'status-approved' : 'status-expired'}`}>
          {active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at)
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            className="order-link"
            icon={<EyeOutlined />}
            onClick={() => {
              Modal.info({
                title: record.name,
                content: (
                  <div>
                    <p><strong>Description:</strong> {record.description || 'No description'}</p>
                    <p><strong>Category:</strong> {getCategoryName(record.category_id)}</p>
                    <p><strong>Vendor:</strong> {getVendorName(record.vendor_id)}</p>
                    <p><strong>Price Model:</strong> {record.price_model}</p>
                    <p><strong>Base Price:</strong> {record.base_price ? `₹${record.base_price.toLocaleString()}` : 'N/A'}</p>
                    <p><strong>Slug:</strong> {record.slug}</p>
                    <p><strong>Status:</strong> {record.is_active ? 'Active' : 'Inactive'}</p>
                  </div>
                )
              });
            }}
          >
            View
          </Button>
          <Button
            type="link"
            className="order-link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete this service?"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button
              type="link"
              className="order-link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="orders-container">
      <div className="orders-content">
        <div className="orders-header">
          <div>
            <h2 className="orders-title">Service Management</h2>
            <p className="orders-subtitle">Manage your available services and offerings</p>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showModal}
          >
            Add Service
          </Button>
        </div>

        {/* Filters */}
        <div style={{ marginBottom: 20 }}>
          <Space wrap>
            <Select
              placeholder="Filter by Category"
              allowClear
              style={{ width: 180 }}
              value={filterCategory}
              onChange={setFilterCategory}
            >
              {categories.map(cat => (
                <Option key={cat.id} value={cat.id}>{cat.name}</Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by Vendor"
              allowClear
              style={{ width: 180 }}
              value={filterVendor}
              onChange={setFilterVendor}
            >
              {vendors.map(vendor => (
                <Option key={vendor.vendor_id} value={parseInt(vendor.vendor_id.slice(1))}>
                  {vendor.name}
                </Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by Price Model"
              allowClear
              style={{ width: 180 }}
              value={filterPriceModel}
              onChange={setFilterPriceModel}
            >
              {PRICE_MODELS.map(model => (
                <Option key={model} value={model}>{model}</Option>
              ))}
            </Select>

            <Select
              placeholder="Filter by Status"
              allowClear
              style={{ width: 150 }}
              value={filterStatus}
              onChange={setFilterStatus}
            >
              <Option value={true}>Active</Option>
              <Option value={false}>Inactive</Option>
            </Select>

            <Button onClick={() => {
              setFilterCategory(null);
              setFilterVendor(null);
              setFilterPriceModel(null);
              setFilterStatus(null);
            }}>
              Clear Filters
            </Button>
          </Space>
        </div>

        {/* Service Table */}
        <Table
          className="orders-table"
          dataSource={filteredServices}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1400 }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />

        {/* Modal */}
        <Modal
          title={editingService ? "Edit Service" : "Add Service"}
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={700}
          destroyOnClose
        >
          <Form form={form} layout="vertical">
            <Form.Item 
              name="name" 
              label="Service Name" 
              rules={[{ required: true, message: 'Please enter service name' }]}
            >
              <Input placeholder="Enter service name" />
            </Form.Item>

            <Form.Item 
              name="category_id" 
              label="Category" 
              rules={[{ required: true, message: 'Please select a category' }]}
            >
              <Select placeholder="Select category">
                {categories.map(cat => (
                  <Option key={cat.id} value={cat.id}>{cat.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item 
              name="vendor_id" 
              label="Vendor" 
              rules={[{ required: true, message: 'Please select a vendor' }]}
            >
              <Select placeholder="Select vendor">
                {vendors.map(vendor => (
                  <Option key={vendor.vendor_id} value={parseInt(vendor.vendor_id.slice(1))}>
                    {vendor.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item 
              name="price_model" 
              label="Price Model"
              rules={[{ required: true, message: 'Please select a price model' }]}
            >
              <Select placeholder="Select price model">
                {PRICE_MODELS.map(model => (
                  <Option key={model} value={model}>{model}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item 
              name="base_price" 
              label="Base Price (₹)"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="Enter base price"
                min={0}
                step={100}
                formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/₹\s?|(,*)/g, '')}
              />
            </Form.Item>

            <Form.Item 
              name="icon_url" 
              label="Icon URL"
            >
              <Input placeholder="Enter icon URL (optional)" />
            </Form.Item>

            <Form.Item 
              name="description" 
              label="Description"
            >
              <TextArea rows={4} placeholder="Enter service description" />
            </Form.Item>

            <Form.Item 
              name="is_active" 
              label="Active Status" 
              valuePropName="checked"
              initialValue={true}
            >
              <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default ServiceManagement;