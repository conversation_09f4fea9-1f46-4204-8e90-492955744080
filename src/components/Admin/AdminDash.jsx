import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Typography, Progress, List } from 'antd';
import { 
  ShoppingOutlined, 
  UserOutlined, 
  TeamOutlined, 
  DollarOutlined 
} from '@ant-design/icons';
import axios from 'axios';

const { Title } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalVendors: 0,
    totalProducts: 0,
    totalCustomers: 0,
    totalRevenue: 0
  });
  const [recentProducts, setRecentProducts] = useState([]);
  const [topVendors, setTopVendors] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, you would fetch this data from your API endpoints
    const fetchData = async () => {
      try {
        // Simulate API calls with timeouts
        setTimeout(() => {
          setStats({
            totalVendors: 24,
            totalProducts: 158,
            totalCustomers: 89,
            totalRevenue: 425600
          });

          setRecentProducts([
            { id: 'P123', name: 'Enterprise Cloud Hosting', vendor: 'ABC Cloud Services', price: 4999, added: '2025-05-01' },
            { id: 'P124', name: 'Cloud Storage Premium', vendor: 'FastCloud', price: 1999, added: '2025-05-03' },
            { id: 'P125', name: 'Domain Registration Bundle', vendor: 'NameGenie', price: 899, added: '2025-05-05' },
            { id: 'P126', name: 'Web Hosting Plus', vendor: 'WebWorld', price: 1499, added: '2025-05-08' },
          ]);

          setTopVendors([
            { name: 'ABC Cloud Services', rating: 4.8, products: 12 },
            { name: 'WebWorld', rating: 4.7, products: 8 },
            { name: 'FastCloud', rating: 4.6, products: 15 },
            { name: 'NameGenie', rating: 4.5, products: 6 },
          ]);

          setLoading(false);
        }, 800);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const recentProductColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: 'Product Name', dataIndex: 'name', key: 'name' },
    { title: 'Vendor', dataIndex: 'vendor', key: 'vendor' },
    { title: 'Price (â‚¹)', dataIndex: 'price', key: 'price', render: (price) => `â‚¹${price.toLocaleString()}` },
    { title: 'Date Added', dataIndex: 'added', key: 'added' },
  ];

  return (
    <div>
      <Title level={3}>Dashboard Overview</Title>
      
      {/* Stats Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card bordered={false} loading={loading}>
            <Statistic
              title="Total Vendors"
              value={stats.totalVendors}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card bordered={false} loading={loading}>
            <Statistic
              title="Total Products"
              value={stats.totalProducts}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#0050b3' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card bordered={false} loading={loading}>
            <Statistic
              title="Total Customers"
              value={stats.totalCustomers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card bordered={false} loading={loading}>
            <Statistic
              title="Total Revenue"
              value={stats.totalRevenue}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#cf1322' }}
              suffix="â‚¹"
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Products Table */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card 
            title="Recently Added Products" 
            bordered={false}
            loading={loading}
            style={{ marginBottom: 24 }}
          >
            <Table 
              dataSource={recentProducts} 
              columns={recentProductColumns} 
              rowKey="id" 
              pagination={false} 
            />
          </Card>
        </Col>
      </Row>

      {/* Top Vendors */}
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Card title="Top Rated Vendors" bordered={false} loading={loading}>
            <List
              itemLayout="horizontal"
              dataSource={topVendors}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    title={<a href="#">{item.name}</a>}
                    description={`${item.products} products`}
                  />
                  <div>
                    Rating: {item.rating}/5
                    <Progress 
                      percent={item.rating * 20} 
                      showInfo={false} 
                      strokeColor="#52c41a" 
                      size="small" 
                      style={{ width: 100, marginLeft: 10 }}
                    />
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Category Distribution" bordered={false} loading={loading}>
            <div style={{ padding: '20px 0' }}>
              <Progress 
                percent={42} 
                status="active" 
                strokeColor="#1677ff"
                format={() => 'Cloud Services (42%)'}
              />
              <Progress 
                percent={28} 
                status="active" 
                strokeColor="#52c41a"
                format={() => 'Web Services (28%)'}
              />
              <Progress 
                percent={18} 
                status="active" 
                strokeColor="#faad14"
                format={() => 'Security Services (18%)'}
              />
              <Progress 
                percent={12} 
                status="active" 
                strokeColor="#f5222d"
                format={() => 'Other Services (12%)'}
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;