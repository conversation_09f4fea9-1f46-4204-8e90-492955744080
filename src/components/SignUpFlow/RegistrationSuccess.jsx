import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";
import "./VendorRF.css";

const RegistrationSuccess = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const navigate = useNavigate();
    const email = location.state?.email || "";

    return (
        <>
            <Header />
            <div className="registration-success-wrapper">
                <div className="registration-success-card">
                    <h2>{t("Registration Successful!")}</h2>
                    <p>{t("Your account has been created successfully.")}</p>
                    <p>
                        {t("An activation email has been sent to")} <strong>{email}</strong>
                    </p>
                    <p>{t("Please check your email to activate your account.")}</p>
                   
                </div>
            </div>
            <Footer />
        </>
    );
};

export default RegistrationSuccess;