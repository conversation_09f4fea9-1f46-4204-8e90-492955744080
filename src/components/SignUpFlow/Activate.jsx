import axios from 'axios';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import { Input, Button, notification, Modal } from 'antd';
import backgroundImage from "../../assets/Image.png";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";
import "./ForgetPassword.css";

const Activate = () => {
    const { t, i18n } = useTranslation();

    const token = new URLSearchParams(window.location.search).get('token'); 
    const [otp, setOtp] = React.useState(""); // Add state to store OTP input
    const [loading, setLoading] = React.useState(false); // Add loading state
    const [errorModalVisible, setErrorModalVisible] = React.useState(false);
    const [errorMessage, setErrorMessage] = React.useState("");

    const decodedToken = jwtDecode(token);

    const email = decodedToken.sub; // Extract the email from the decoded token

    const navigate = useNavigate();

    useEffect(() => {
        const dir = i18n.language === "ar" ? "rtl" : "ltr";
        document.documentElement.setAttribute("dir", dir);
    }, [i18n.language]);

    React.useEffect(() => {
        const sendOtp = async () => {
            try {
                await fetch(`${import.meta.env.VITE_APP_API_BASE_URL}/activate?token=` + token, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
            } catch (error) {
                console.error("Error sending OTP:", error);
                notification.error({
                    message: t("Error"),
                    description: t("Failed to send OTP. Please try again."),
                });
            }
        };
        sendOtp();
    }, []);

    const verifyOtp = async (otp) => {
        setLoading(true);
        try {
            const response = await axios.post(`${import.meta.env.VITE_APP_API_BASE_URL}/verify-otp`, {
                otp: otp,
                token: token
            }, {
                headers: {
                    "Content-Type": "application/json",
                },
            });
            if (response.data.includes("Invalid OTP")) {
                throw new Error("Invalid OTP");
            }
            console.log("OTP verification successful:", response.data);
            
            notification.success({
                message: t("Success"),
                description: t("OTP verified successfully. Redirecting to set password page."),
            });
            navigate("/set-password", { state: { token: token } }); // Navigate to set password page
        } catch (error) {
            console.error("Error verifying OTP:", error);
            setErrorMessage(t("The OTP you entered is incorrect. Please try again."));
            setErrorModalVisible(true);
        } finally {
            setLoading(false);
        }
    };

    const resendOtp = async () => {
        setLoading(true);
        try {
            const response = await axios.post(`${import.meta.env.VITE_APP_API_BASE_URL}/resend-otp/` + email, {}, {
                headers: {
                    "Content-Type": "application/json",
                },
            });
            console.log("OTP resent successful:", response.data);
            notification.success({
                message: t("Success"),
                description: t("OTP has been resent to your email."),
            });
        } catch (error) {
            console.error("Error resending OTP:", error);
            notification.error({
                message: t("Error"),
                description: t("Failed to resend OTP. Please try again."),
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
        <Header />
        <div className="forget-container">
            <div
                className="forget-left"
                style={{ backgroundImage: `url(${backgroundImage})` }}
            >
                <div className="forget-welcome-box">
                    <h2>{t("Welcome!")}</h2>
                </div>
            </div>

            <div className="forget-right">
                <h2 className="forget-heading">{t("Activate your account")}</h2>
                <p>{t("Please enter the OTP sent to your email")}</p>

                <form className="forget-form">
                    <label>{t("OTP Code")}</label>
                    <input
                        type="text"
                        placeholder={t("Enter OTP")}
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                    />

                    <div className="form-buttons">
                        <Button
                            type="primary"
                            onClick={() => verifyOtp(otp)}
                            loading={loading}
                            className="submit-btn"
                        >
                            {t("Verify")}
                        </Button>
                        <Button
                            onClick={() => resendOtp()}
                            loading={loading}
                            className="reset-btn"
                        >
                            {t("Resend OTP")}
                        </Button>
                    </div>

                    <div className="extra-links">
                        <span onClick={() => navigate("/signin")}>{t("Back To Login")}</span>
                    </div>

                    <p className="signup-link">
                        {t("Don't have an account?")} {" "}
                        <span onClick={() => navigate("/signup")}>{t("Sign up")}</span>
                    </p>
                </form>
            </div>

            <Modal
                title={t("Verification Failed")}
                open={errorModalVisible}
                onOk={() => setErrorModalVisible(false)}
                onCancel={() => setErrorModalVisible(false)}
                footer={[
                    <Button key="ok" type="primary" onClick={() => setErrorModalVisible(false)}>
                        {t("OK")}
                    </Button>
                ]}
            >
                <p>{errorMessage}</p>
            </Modal>
        </div>
        <Footer />
        </>
    );
};

export default Activate;