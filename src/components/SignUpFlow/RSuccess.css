.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 10px;
    width: 500px;
    max-width: 90%;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.success-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}

.success-heading {
    font-size: 24px;
    color: #28a745;
    margin-bottom: 10px;
}

.success-message {
    font-size: 16px;
    color: #333;
}

.note {
    font-size: 15px;
    color: #000;
    margin: 15px 0;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 25px;
}

.back-btn {
    padding: 10px 20px;
    background: white;
    border: 1px solid #28a745;
    color: #000000;
    border-radius: 5px;
    cursor: pointer;
}

.close-btn {
    padding: 10px 20px;
    background-color: #9e3ca2;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.back-btn:hover {
    background-color: #28a745;
    color: white;
}

.close-btn:hover {
    background-color: #912491;
}