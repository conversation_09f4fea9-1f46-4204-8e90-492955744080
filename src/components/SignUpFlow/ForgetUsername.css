.forget-container {
    display: flex;
    width: 50%; /* Reduced from 60% */
    height: 50%; /* Reduced from 60% */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Segoe UI', sans-serif;
    margin: 30px auto; /* Reduced from 50px */
    position: relative;
    left: 0;
    right: 0;
    max-width: 1000px; /* Reduced from 1200px */
}

[dir='rtl'] .forget-container {
    margin: 50px auto;
}

.forget-left {
    width: 50%;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 60px 30px;
    color: white;
}

.forget-welcome-box {
    text-align: center;
    padding: 30px;
}

.forget-welcome-box h2 {
    font-size: 28px;
    margin-bottom: 10px;
}

.forget-welcome-box p {
    font-size: 14px;
    line-height: 1.6;
}

.forget-welcome-box button {
    margin-top: 20px;
    padding: 10px 24px;
    border: 2px solid white;
    background-color: transparent;
    color: white;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: 0.3s ease;
}

.forget-welcome-box button:hover {
    background-color: white;
    color: #6b21a8;
}

.forget-right {
    width: 50%;
    background-color: white;
    padding: 30px; /* Reduced from 40px */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.forget-heading {
    font-size: 24px; /* Reduced from 26px */
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px; /* Reduced from 10px */
}

.forget-subtext {
    font-size: 14px;
    color: #333;
    text-align: left;
    margin-bottom: 30px;
}

.forget-form {
    max-width: 350px; /* Reduced from 400px */
    margin: 0 auto;
    padding: 1.5rem; /* Reduced from 2rem */
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 2px 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.forget-form label {
    font-weight: 600;
    display: block;
    margin-bottom: 6px;
    margin-top: 15px;
    text-align: start;
}

.forget-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #9333ea;
    border-radius: 6px;
    margin-bottom: 20px;
}

.form-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 16px;
    margin: 24px auto;
    max-width: 700px;
}

.submit-btn,
.reset-btn {
    
    padding: 6px 10px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.submit-btn {
    background-color: #9333ea;
    color: white;
    border: none;
}

.submit-btn:hover {
    background-color: #7e22ce;
}

.reset-btn {
    background-color: white;
    border: 1px solid #22c55e;
    color: #22c55e;
    margin-top: 8px;
}

.reset-btn:hover {
    background-color: #f0fdf4;
}

.extra-links {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    font-size: 13px;
    color: #9333ea;
    cursor: pointer;
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
}

.signup-link span {
    color: #a855f7;
    cursor: pointer;
}

/* RTL Styles */
html[dir="rtl"] .forget-subtext,
html[dir="rtl"] .forget-form label {
    text-align: right;
}

html[dir="rtl"] .forget-form input {
    direction: rtl;
}

/* Center the container */
.activate-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    padding: 20px;
}

.password-requirements {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.requirement {
    display: flex;
    align-items: center;
    margin: 5px 0;
    color: #6c757d;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.requirement span {
    margin-right: 8px;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.requirement.valid {
    color: #28a745;
}

.requirement.valid span {
    opacity: 1;
}

/* Success Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 10px;
    width: 500px;
    max-width: 90%;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.success-icon {
    font-size: 50px;
    color: #28a745;
    margin-bottom: 15px;
}

.success-heading {
    font-size: 24px;
    color: #28a745;
    margin-bottom: 10px;
}

.success-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 25px;
}

.close-btn {
    padding: 10px 20px;
    background-color: #9333ea;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: #7928ca;
}

.password-input-container {
    position: relative;
    width: 100%;
}

.password-input-container input {
    width: 100%;
    padding-right: 40px;
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 16px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle-btn:hover {
    color: #333;
}