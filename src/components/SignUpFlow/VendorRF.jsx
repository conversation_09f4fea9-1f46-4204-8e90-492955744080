import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import axios from "axios";
import { Form, Input, Radio, Select, Button, Modal, Spin, Row, Col, Space } from "antd";
import Turnstile from "react-turnstile";
import "./VendorRF.css";
import backgroundImage from "../../assets/Registration.svg";
import { useNavigate } from "react-router-dom";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";
import TurnstileWidget from "../Captcha/Captcha";
const { Option } = Select;



const VendorRegistration = () => {
    const { t, i18n } = useTranslation();
    const [form] = Form.useForm();
    const [userType, setUserType] = useState("Customer");
    const [loading, setLoading] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalContent, setModalContent] = useState({ title: "", content: "" });
    const [errorModalVisible, setErrorModalVisible] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const [turnstileToken, setTurnstileToken] = useState(null);
    const turnstileRef = useRef(null);
    const navigate = useNavigate();

    // Cloudflare Turnstile site key - should be stored in environment variables
    const TURNSTILE_SITE_KEY = import.meta.env.VITE_APP_TURNSTILE_SITE_KEY; // Replace with your actual site key

    useEffect(() => {
        document.documentElement.setAttribute("dir", i18n.language === "ar" ? "rtl" : "ltr");
    }, [i18n.language]);

    const handleUserTypeChange = (e) => {
        setUserType(e.target.value);
    };

    const showModal = (title, content) => {
        setModalContent({ title, content });
        setIsModalVisible(true);
    };

    const handleOk = () => {
        setIsModalVisible(false);
        navigate("/signin");
    };

    const handleCancel = () => {
        setIsModalVisible(false);
    };

    const showErrorModal = (message) => {
        setErrorMessage(message);
        setErrorModalVisible(true);
    };

    const handleErrorModalOk = () => {
        setErrorModalVisible(false);
    };

    const handleSubmit = async (values) => {
        console.log("Form submitted", values);

        // Check if Turnstile token is available
        if (!turnstileToken) {
            // showErrorModal(t("Please complete the security verification"));
            return;
        }

        setLoading(true);
        try {
            // Include the Turnstile token in the request
            const dataWithToken = {
                ...values,
                turnstileToken: turnstileToken
            };

            const response = await axios.post(`${import.meta.env.VITE_APP_API_BASE_URL}/signup`, dataWithToken, {
                headers: {
                    "Content-Type": "application/json",
                    //clouflare ip

                },
                withCredentials: true,
            });

            console.log("Signup response:", response.data);
            if (response.data.includes("409")) {
                throw new Error("Conflict: User already exists");
            }

            // "ApiResponse(status=error, message=Invalid Turnstile token, code=400)""
            if (response.data.includes("Invalid Turnstile token")) {
                throw new Error("Invalid Turnstile token");
            }

            if (response.data) {
                form.resetFields();
                if (turnstileRef.current) {
                    turnstileRef.current.reset();
                }
                setTurnstileToken(null);

                // Navigate to the success page and pass the email
                navigate("/registration-success", {
                    state: { email: values.emailAddress }
                });
            }
        } catch (error) {
            console.error("Signup error:", error);
            let errorMessage = t("An error occurred during signup. Please try again.");
            form.resetFields();
            // Reset Turnstile widget
            if (turnstileRef.current) {
                turnstileRef.current.reset();
            }
            setTurnstileToken(null);
            if (error.response) {
                switch (error.response.status) {
                    case 409:
                        errorMessage = t("This email is already registered. Please use a different email address.");
                        break;
                    case 400:
                        errorMessage = t("Invalid input data. Please check your information and try again.");
                        break;
                    case 422:
                        errorMessage = t("Please fill in all required fields correctly.");
                        break;
                    case 401:
                        errorMessage = t("Unauthorized access. Please check your credentials or try again later.");
                        break;
                }
            } else if (error.message.includes("User already exist")) {
                errorMessage = t("User already exists. Please try signing in.");
            }

            showErrorModal(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Header />  {/* ⬅ Add Header on top */}
            <div className="page-wrapper">
                <div className="registration-container">
                    <Row gutter={0} className="registration-row" dir={i18n.language === "ar" ? "rtl" : "ltr"}>
                        <Col xs={24} md={12} className="left-section" style={{ backgroundImage: `url(${backgroundImage})` }}>
                            <div className="overlay-text">
                                <h2>
                                    {t("The Best")} <br /> {t("IT Services, Products & Solutions")}
                                </h2>
                                <ul>
                                    <li className="list1">{t("Cloud Computing for Business Efficiency")}</li>
                                    <li className="list2">{t("Cloud Solutions")}</li>
                                    <li className="list3">{t("Securing Your Digital World")}</li>
                                    <li className="list4">{t("Building Seamless Mobile Experiences")}</li>
                                </ul>
                            </div>
                        </Col>

                        <Col xs={24} md={12} className="right-section">
                            <h2 className="signup-heading">{t("Sign Up")}</h2>

                            <Form
                                form={form}
                                layout="vertical"
                                onFinish={handleSubmit}
                                className="signup-form"
                                size="large"
                                initialValues={{
                                    
                                    countryCode: "+966",
                                }}
                            >
                                <Form.Item
                                    name="userType"
                                    label={t("User Type")}
                                    rules={[{ required: true, message: t("User Type is required") }]} // <-- Add this line
                                >
                                    <Radio.Group onChange={handleUserTypeChange}>
                                        <Radio value="customer">{t("Customer")}</Radio>
                                        <Radio value="vendor">{t("Vendor")}</Radio>
                                        <Radio value="freelancer">{t("Freelancer")}</Radio>
                                    </Radio.Group>
                                </Form.Item>

                                <Form.Item
                                    name="firstName"
                                    label={t("First Name")}
                                    rules={[{ required: true, message: t("First Name is required") }]}
                                >
                                    <Input placeholder={t("Enter Name")} />
                                </Form.Item>

                                <Form.Item
                                    name="lastName"
                                    label={t("Last Name")}
                                    rules={[{ required: true, message: t("Last Name is required") }]}
                                >
                                    <Input placeholder={t("Enter Last Name")} />
                                </Form.Item>

                                <Form.Item
                                    name="emailAddress"
                                    label={t("Email Address")}
                                    rules={[ 
                                        { required: true, message: t("Email Address is required") },
                                        { type: "email", message: t("Invalid email format") },
                                    ]}
                                >
                                    <Input placeholder={t("Enter Email Address")} />
                                </Form.Item>

                                <Form.Item
                                    name="phoneNumber"
                                    label={t("Phone Number")}
                                    rules={[
                                        { required: true, message: t("Phone Number is required") },
                                        { pattern: /^\d{9}$/, message: t("Phone Number must be 9 digits long") },
                                    ]}
                                >
                                    <Input
                                        addonBefore={
                                            <Form.Item name="countryCode" noStyle>
                                                <Select>
                                                    <Option value="+966">+966</Option>
                                                    <Option value="+1">+1</Option>
                                                    <Option value="+44">+44</Option>
                                                    <Option value="+91">+91</Option>
                                                </Select>
                                            </Form.Item>
                                        }
                                        placeholder={t("Enter Phone Number")}
                                        maxLength={9}
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="turnstile"
                                    label={t("Security Verification")}
                                  
                                >
                                    <div className="turnstile-container">
                                        <Turnstile
                                            ref={turnstileRef}
                                            sitekey={TURNSTILE_SITE_KEY}
                                            onVerify={(token) => {
                                                setTurnstileToken(token);
                                                form.validateFields(['turnstile']);
                                            }}
                                            onError={() => {
                                                setTurnstileToken(null);
                                                form.validateFields(['turnstile']);
                                            }}
                                            onExpire={() => {
                                                setTurnstileToken(null);
                                                form.validateFields(['turnstile']);
                                            }}
                                            theme="light"
                                            language={i18n.language}
                                        />
                                    </div>
                                </Form.Item>



                                <Form.Item>
                                <Space>
                                    <Button type="primary" htmlType="submit">
                                    {t('Submit')} 
                                    </Button>
                                    {/* <Button htmlType="reset">{t('Submit')}</Button> */}
                                </Space>
                                </Form.Item>
                            </Form>

                            <div className="signin-link">
                                {t("Have an account?")}{" "}
                                <span onClick={() => navigate("/signin")} style={{ cursor: "pointer", color: "#007bff" }}>
                                    {t("Sign In")}
                                </span>
                            </div>
                        </Col>
                    </Row>
                </div>

                <Modal
                    title={t("Error")}
                    open={errorModalVisible}
                    onOk={handleErrorModalOk}
                    onCancel={handleErrorModalOk}
                    centered
                >
                    <p>{errorMessage}</p>
                </Modal>

                <Modal
                    open={isModalVisible}
                    title={modalContent.title}
                    onOk={handleOk}
                    onCancel={handleCancel}
                    okText={t("Go to Sign In")}
                    cancelText={t("Stay Here")}
                    centered
                >
                    {modalContent.content}
                </Modal>
            </div>
            <Footer />
        </>
    );
};

export default VendorRegistration;
