import React from "react";
import "./App.css";
import { FaCheckCircle } from "react-icons/fa";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";

const SuccessModal = ({ onClose, onBackToLogin }) => {
    return (
        <>
        <Header />
        <div className="modal-overlay">
            <div className="modal-content">
                <FaCheckCircle className="success-icon" />
                <h2 className="success-heading">Success</h2>
                <p className="success-message">
                    An email has been sent to the provided email address. Please follow the email instructions.
                </p>
                <p className="note">
                    <strong>If not received in inbox, please verify the spam folder.</strong>
                </p>
                <div className="modal-buttons">
                    <button className="back-btn" onClick={onBackToLogin}>Back to login</button>
                    <button className="close-btn" onClick={onClose}>Close</button>
                </div>
            </div>
        </div>
        <Footer />
        </>

    );
};

export default SuccessModal;
