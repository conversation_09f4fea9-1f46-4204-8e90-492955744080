import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Row, Col, Form, Input, Button, Spin } from "antd";
import { useNavigate } from "react-router-dom";
import backgroundImage from "../../assets/Image.png";
import axios from "axios";
import { useDispatch } from "react-redux";
import { setTokens } from "../../redux/Slice/authSlice";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import "./SignInPage.css";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";

const SignInPage = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = () => {
        setShowPassword((prev) => !prev);
    };

    const handleSubmit = async (values) => {
        setLoading(true);
        try {
            const response = await axios.post(
                import.meta.env.VITE_APP_KEYCLOAK_URL,
                {
                    username: values.username,
                    password: values.password,
                },
      
            {
                headers: { "Content-Type": "application/json" },
            }
                            
            );

            const { access_token, refresh_token } = response.data;
            

            try {
                const tokenParts = access_token.split(".");
                console.log("Token parts:", tokenParts);

                if (tokenParts.length !== 3) {
                    console.error("Invalid JWT format - should have 3 parts");
                    throw new Error("Invalid JWT format");
                }

                const base64Url = tokenParts[1];
                console.log("Base64 payload:", base64Url);

                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                console.log("Decoded payload:", jsonPayload);
                const decodedAccessToken = JSON.parse(jsonPayload);
                console.log("Parsed token data:", decodedAccessToken);

                dispatch(setTokens({
                    accessToken: access_token,
                    refreshToken: refresh_token,
                    user: decodedAccessToken,
                }));

                // Verify the data was stored in Redux
                setTimeout(() => {
                    const storedUser = JSON.parse(localStorage.getItem("user"));
                    console.log("User stored in localStorage:", storedUser);
                }, 100);

                navigate("/dashboard");
            } catch (error) {
                console.error("Error decoding JWT:", error);
                message.error("Authentication successful but there was an error processing user data");
            }
        } catch (error) {
            console.error("Sign-in error:", error);
            form.setFields([{
                name: "password",
                errors: [t(error.response?.status === 401 ? "Invalid username or password" : "An error occurred. Please try again.")],
            }]);
        } finally {
            setLoading(false);
        }
    };

    const handleForgotPassword = () => {
        navigate("/user-email");
    };

    return (
        <>
            <Header />
            <div className="page-wrapper">
                <div className="auth-container">
                    <Row gutter={0} className="auth-row" dir={i18n.language === "ar" ? "rtl" : "ltr"}>
                        <Col xs={24} md={12} className="auth-left" style={{ backgroundImage: `url(${backgroundImage})` }}>
                            <div className="welcome-box">
                                <h2>{t("Welcome!")}</h2>
                                <p>{t("To keep connected with us please")}<br />{t("Sign in with your personal info")}</p>
                            </div>
                        </Col>

                        <Col xs={24} md={12} className="auth-right">
                            <h2 className="auth-heading">{t("Sign In")}</h2>

                            <Form
                                form={form}
                                layout="vertical"
                                onFinish={handleSubmit}
                                className="auth-form"
                                size="large"
                            >
                                <Form.Item
                                    name="username"
                                    label={t("Email")}
                                    rules={[
                                        { required: true, message: t("Email is required") },
                                        { type: "email", message: t("Please enter a valid email address") },
                                    ]}
                                >
                                    <Input placeholder={t("Enter Email")} disabled={loading} />
                                </Form.Item>

                                <Form.Item
                                    name="password"
                                    label={t("Password")}
                                    rules={[
                                        { required: true, message: t("Password is required") },
                                        { min: 9, message: t("Password must be at least 9 characters") },
                                    ]}
                                >
                                    <Input.Password
                                        placeholder={t("Enter Password")}
                                        iconRender={(visible) => (visible ? <FaEyeSlash /> : <FaEye />)}
                                        disabled={loading}
                                        style={{ alignItems: "center" }}
                                    />
                                </Form.Item>

                                <Form.Item>
                                    <Button type="primary" htmlType="submit" block loading={loading} style={{ marginTop: "20px" }}>
                                        {loading ? <Spin /> : t("Sign In")}
                                    </Button>
                                </Form.Item>

                                <div className="extra-links">
                                    <div className="forgot-link">
                                        <a onClick={handleForgotPassword} style={{ cursor: "pointer", color: "#007bff" }}>
                                            {t("Forget Password?")}
                                        </a>
                                    </div>
                                </div>

                                <p className="signup-link">
                                    {t("Don't have an account?")}{" "}
                                    <span onClick={() => navigate("/signup")} style={{ cursor: "pointer", color: "#007bff" }}>
                                        {t("Sign Up")}
                                    </span>
                                </p>
                            </Form>
                        </Col>
                    </Row>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default SignInPage;
