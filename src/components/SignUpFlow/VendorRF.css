.page-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem;
    background-color: #f0f2f5;
}

.registration-container {
    width: 100%;
    max-width: 1200px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Segoe UI', sans-serif;
}

.registration-row {
    width: 100%;
    display: flex;
}

.left-section {
    width: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 30px 20px;
    color: white;
    text-align: center;
    position: relative; /* Add this */
    min-height: 300px; /* Add minimum height */
}

/* Add overlay background to improve text readability */
.left-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
    z-index: 1;
}

@media (min-width: 768px) {
    .left-section {
        width: 50%;
        padding: 60px 30px;
    }
}

.overlay-text {
    position: relative; /* Add this */
    z-index: 2; /* Place text above the overlay */
    padding: 20px;
    width: 100%;
    max-width: 400px; /* Limit text width */
}

.overlay-text h2 {
    font-size: 20px;
    margin-bottom: 20px;
}

@media (min-width: 768px) {
    .overlay-text h2 {
        font-size: 28px;
    }
}

.overlay-text ul {
    list-style-type: none;
    padding-left: 20px;
    text-align: left;
}

.right-section {
    flex: 1;
    background-color: #ffffff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.signup-heading {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.signup-form {
    width: 100%;
    max-width: 450px;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.signup-form .ant-form-item {
    margin-bottom: 20px;
}

.signup-form .ant-form-item-label {
    padding-bottom: 6px;
}

.signup-form .ant-form-item-label > label {
    font-weight: 600;
    color: #333;
}

.signup-form .ant-input,
.signup-form .ant-input-password {
    border-radius: 6px;
    height: 40px;
}

.signup-form .ant-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.form-buttons-container {
    display: flex;
    align-items: center;  /* Align buttons vertically */
    gap: 16px;           /* Consistent gap between buttons */
    margin-top: 24px;
    margin-right: 10px;
}

.form-buttons-container .ant-btn {
    height: 40px;        /* Consistent height */
    min-width: 120px;    /* Minimum width */
}

.signin-link {
    text-align: center;
    margin-top: 25px;
    font-size: 14px;
    color: #666;
}

.signin-link span {
    color: #1890ff;
    cursor: pointer;
    font-weight: 500;
    transition: color 0.3s;
}

.signin-link span:hover {
    color: #40a9ff;
    text-decoration: underline;
}

/* Media Queries for Responsiveness */
@media (max-width: 1024px) {
    .page-wrapper {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .registration-row {
        flex-direction: column;
    }

    .left-section,
    .right-section {
        width: 100%;
        padding: 30px 20px;
    }

    .left-section {
        min-height: 350px; /* Increase minimum height on mobile */
        padding: 40px 20px;
    }

    .signup-form {
        padding: 1.5rem;
        box-shadow: none;
    }

    .overlay-text {
        padding: 15px;
    }

    .overlay-text h2 {
        font-size: 22px;
        margin-bottom: 15px;
    }

    .overlay-text ul {
        padding-left: 10px;
        margin-bottom: 0;
    }

    .overlay-text li {
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 1.4;
    }

    .form-buttons-container {
        flex-direction: column;
        align-items: center;
        gap: 20px;       /* Increased gap for mobile */
    }

    .form-buttons-container .ant-btn {
        width: 80%;
        max-width: 300px;
        margin: 0 !important;  /* Override any inline styles */
    }

    /* Reset specific button styles for mobile */
    .form-buttons-container .ant-btn:first-child {
        margin-top: 0 !important;
    }

    .form-buttons-container .ant-btn:last-child {
        margin-left: 0 !important;
    }

    /* Reset the margin-left from inline style */
    .form-buttons-container .ant-btn:last-child {
        margin-left: 0 !important;
    }
}

@media (max-width: 480px) {
    .page-wrapper {
        padding: 0.5rem;
        min-height: 70vh;
    }

    .registration-container {
        box-shadow: none;
    }

    .left-section {
        min-height: 300px;
        padding: 30px 15px;
    }

    .signup-form {
        padding: 1rem 0.5rem;
    }

    .signup-heading {
        font-size: 22px;
        margin-bottom: 15px;
    }

    .right-section {
        padding: 20px 15px;
    }

    .signup-form .ant-input,
    .signup-form .ant-input-password {
        height: 36px;
    }

    .signup-form .ant-form-item {
        margin-bottom: 15px;
    }

    .form-buttons-container {
        flex-direction: column;
        gap: 10px;
    }

    .form-buttons-container .ant-btn {
        width: 100%;
    }

    .overlay-text h2 {
        font-size: 20px;
        margin-bottom: 12px;
    }

    .overlay-text ul {
        padding-left: 5px;
    }

    .overlay-text li {
        font-size: 13px;
        margin-bottom: 6px;
    }
}

/* Turnstile container styles */
.turnstile-container {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

/* Center the Turnstile widget on mobile */
@media (max-width: 480px) {
    .turnstile-container {
        transform: scale(0.9);
        transform-origin: center;
    }
}

/* Registration Success Page Styles */
.registration-success-wrapper {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f3e8ff 0%, #f0f2f5 100%);
}

.registration-success-card {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(80, 0, 120, 0.10), 0 1.5px 6px rgba(147, 51, 234, 0.08);
    padding: 48px 32px 40px 32px;
    max-width: 420px;
    width: 100%;
    margin: 40px auto;
    text-align: center;
    animation: fadeInUp 0.7s cubic-bezier(.39,.575,.565,1) both;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(40px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.registration-success-card h2 {
    color:  #9e3ca2;
    font-size: 2rem;
    margin-bottom: 18px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.registration-success-card p {
    color: #444;
    font-size: 1.08rem;
    margin-bottom: 14px;
    line-height: 1.6;
}

.registration-success-card strong {
    color: #9e3ca2;
    font-weight: 600;
}

.registration-success-card .submit-btn {
    background: linear-gradient(90deg, #9333ea 60%, #7e22ce 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 18px;
    box-shadow: 0 2px 8px rgba(147, 51, 234, 0.10);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s;
}

.registration-success-card .submit-btn:hover {
    background: linear-gradient(90deg, #7e22ce 60%, #9333ea 100%);
    box-shadow: 0 4px 16px rgba(147, 51, 234, 0.18);
}

@media (max-width: 600px) {
    .registration-success-card {
        padding: 32px 10px 28px 10px;
        max-width: 98vw;
    }
}