import React from 'react';
import './ForgetUsername.css';
import bgImage from '../../assets/Image.png';
import { useTranslation } from 'react-i18next';
import Header from '../Header-Section/Header'; // ✅ Use your custom Header component
import Footer from '../Footer-Section/Footer';
import "./PasswordForms.css"
const ForgetUsername = () => {
    const { t } = useTranslation();

    return (
        <>
            <Header /> {/* ✅ This renders your reusable header */}
            <div className="container">
                {/* Left side */}
                <div className="left-side" style={{ backgroundImage: `url(${bgImage})` }}>
                    <div className="overlay">
                        <h2>{t("Welcome!")}</h2>
                        <p>
                            {t("To keep connected with us please")}<br />
                            {t("Sign in with your personal info")}
                        </p>
                        {/* <button className="sign-in-btn">{t("SIGN IN")}</button> */}
                    </div>
                </div>

                {/* Right side */}
                <div className="right-side">
                    <h2 className="title">{t("Forget Username")}</h2>
                    <p className="subtext">{t("Fill below fields")}</p>
                    <div className="form-box">
                        <label>{t("Email")}</label>
                        <input type="email" placeholder={t("Enter Email ID")} />
                        <div className="button-group">
                            <button className="reset-btn">{t("Reset")}</button>
                            <button className="submit-btn">{t("Submit")}</button>
                        </div>
                        <div className="links">
                            <p>
                                {t("Don't have an account?")} <a href="#">{t("Sign up")}</a>
                            </p>
                            <div className="link-row">
                                <a href="#">{t("Back To Login")}</a>
                                <a href="#">{t("Forget Password?")}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default ForgetUsername;
