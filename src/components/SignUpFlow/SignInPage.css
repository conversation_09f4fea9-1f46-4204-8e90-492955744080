/* Imports common styles */
@import  './AuthCommon.css';

/* RTL specific tweaks */
html[dir="rtl"] .auth-form input,
html[dir="rtl"] .auth-form label,
html[dir="rtl"] .signin-heading {
    text-align: right;
}

html[dir="rtl"] .extra-links {
    flex-direction: row-reverse;
}

/* Sign-in specific */
.extra-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}

.forgot-link {
    width: 100%;
    text-align: center;
}

.forgot-link a {
    color: #1890ff;
    transition: color 0.3s;
}

.forgot-link a:hover {
    color: #40a9ff;
    text-decoration: underline;
}

.signup-link {
    text-align: center;
    margin-top: 25px;
    font-size: 14px;
    color: #666;
}

.signup-link span {
    color: #1890ff;
    cursor: pointer;
    font-weight: 500;
    transition: color 0.3s;
}

.signup-link span:hover {
    color: #40a9ff;
    text-decoration: underline;
}
