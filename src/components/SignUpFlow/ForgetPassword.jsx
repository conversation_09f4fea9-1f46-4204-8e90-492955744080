
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import backgroundImage from "../../assets/Image.png";
import "./PasswordForms.css";
import Header from "../Header-Section/Header";
import { FaCheckCircle, FaEye, FaEyeSlash } from "react-icons/fa";

import Footer from "../Footer-Section/Footer";
import { Form, Input, Radio, Select, Button, Modal, Spin, Row, Col, Space } from "antd";


const ForgetPassword = () => {
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    useEffect(() => {
        const dir = i18n.language === "ar" ? "rtl" : "ltr";
        document.documentElement.setAttribute("dir", dir);
    }, [i18n.language]);

    return (
        <>
            <Header />
            <div className="page-wrapper">
                <div className="forget-container">
                    <div
                        className="forget-left"
                        style={{ backgroundImage: `url(${backgroundImage})` }}
                    >
                        <div className="forget-welcome-box">
                            <h2>{t("Welcome!")}</h2>
                            <p>
                                {t("To keep connected with us please")}<br />
                                {t("Sign in with your personal info")}
                            </p>
                        </div>
                    </div>

                    <div className="forget-right">
                        <h2 className="forget-heading">{t("Reset Password")}</h2>
                        <p className="forget-subtext">{t("Please enter your new password")}</p>

                        <form className="forget-form">
                            <label>{t("Password")}</label>
                            <div className="password-input-container">
                                <input 
                                    type={showPassword ? "text" : "password"} 
                                    placeholder={t("Enter Password")} 
                                />
                                <button 
                                    type="button" 
                                    className="password-toggle-btn"
                                    onClick={() => setShowPassword(!showPassword)}
                                >
                                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                                </button>
                            </div>

                            <label>{t("Confirm Password")}</label>
                            <div className="password-input-container">
                                <input 
                                    type={showConfirmPassword ? "text" : "password"} 
                                    placeholder={t("Confirm Password")} 
                                />
                                <button 
                                    type="button" 
                                    className="password-toggle-btn"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                                </button>
                            </div>

                            <div className="form-buttons">
                                {/* <button type="submit" className="submit-btn">
                                    {t("Set Password")}
                                </button>
                                <button type="reset" className="reset-btn">
                                    {t("Reset")}
                                </button> */}
                                  <Form.Item>
                                    <Space>
                                        <Button type="primary" htmlType="submit">
                                            {t("Set Password")}
                                        </Button>
                                        <Button htmlType="reset">{t("Reset")}</Button>
                                    </Space>
                                </Form.Item>
                            </div>

                            <div className="extra-links">
                                <span onClick={() => navigate("/signin")}>
                                    {t("Back To Login")}
                                </span>
                            </div>

                            <p className="signup-link">
                                {t("Don't have an account?")}{" "}
                                <span onClick={() => navigate("/signup")}>
                                    {t("Sign up")}
                                </span>
                            </p>
                        </form>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default ForgetPassword;


