/* Common Wrapper */
.page-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem;
}

.auth-container {
    width: 100%;
    max-width: 1200px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-family: 'Segoe UI', sans-serif;
    overflow: hidden;
}

.auth-row {
    width: 100%;
    display: flex;
}

/* Left side (shared) */
.auth-left {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
    padding: 20px;
    color: white;
    text-align: center;
    min-height: 300px;
}

.welcome-box {
    max-width: 300px;
    text-align: center;
    padding: 30px;
}

.welcome-box h2 {
    font-size: 28px;
    margin-bottom: 10px;
}

.welcome-box p {
    font-size: 14px;
    line-height: 1.6;
}

/* Right side (form wrapper) */
.auth-right {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
}

.auth-heading {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.auth-form {
    width: 100%;
    max-width: 400px;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.auth-form .ant-form-item {
    margin-bottom: 20px;
}

.auth-form .ant-form-item-label {
    padding-bottom: 6px;
}

.auth-form .ant-form-item-label > label {
    font-weight: 600;
    color: #333;
}

.auth-form .ant-input,
.auth-form .ant-input-password {
    border-radius: 6px;
    height: 40px;
}

/* Responsive Media Queries */
@media (max-width: 768px) {
    .auth-row {
        flex-direction: column;
    }

    .auth-left,
    .auth-right {
        width: 100%;
        padding: 30px 20px;
    }

    .auth-left {
        min-height: 200px;
    }

    .auth-form {
        padding: 1.5rem;
        box-shadow: none;
    }

    .welcome-box {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .page-wrapper {
        padding: 0.5rem;
        min-height: 70vh;
    }

    .auth-container {
        box-shadow: none;
    }

    .auth-form {
        padding: 1rem 0.5rem;
    }

    .auth-heading {
        font-size: 22px;
        margin-bottom: 15px;
    }

    .auth-right {
        padding: 20px 15px;
    }

    .auth-form .ant-input,
    .auth-form .ant-input-password {
        height: 36px;
    }

    .auth-form .ant-form-item {
        margin-bottom: 15px;
    }
}
