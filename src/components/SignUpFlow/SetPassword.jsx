import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Radio, Select, Button,Spin, Row, Col, Space } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import axios from "axios";
import backgroundImage from "../../assets/Image.png";
import { notification, Modal } from "antd";
import { FaCheckCircle, FaEye, FaEyeSlash } from "react-icons/fa";
import Header from "../Header-Section/Header";
import Footer from "../Footer-Section/Footer";
import "./SetPassword.css";

const SetPassword = () => {
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const location = useLocation();
    const token = location.state?.token || null;

    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [errorMessageModal, setErrorMessageModal] = useState(false);
    const [validations, setValidations] = useState({
        minLength: false,
        hasNumber: false,
        hasSpecial: false,
        hasUpper: false,
        hasLower: false,
        matches: false
    });

    useEffect(() => {
        const dir = i18n.language === "ar" ? "rtl" : "ltr";
        document.documentElement.setAttribute("dir", dir);
    }, [i18n.language]);

    const validatePassword = (pass) => {
        setValidations({
            minLength: pass.length >= 8,
            hasNumber: /\d/.test(pass),
            hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(pass),
            hasUpper: /[A-Z]/.test(pass),
            hasLower: /[a-z]/.test(pass),
            matches: pass === confirmPassword
        });
    };

    const handlePasswordChange = (e) => {
        const newPassword = e.target.value;
        setPassword(newPassword);
        validatePassword(newPassword);
    };

    const handleConfirmPasswordChange = (e) => {
        const newConfirmPassword = e.target.value;
        setConfirmPassword(newConfirmPassword);
        setValidations(prev => ({
            ...prev,
            matches: password === newConfirmPassword
        }));
    };

    const handleProceedToSignIn = () => {
        setShowSuccessModal(false);
        navigate("/signin");
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        console.log('Attempting to submit password with token:', token);
        console.log('Password validation state:', validations);

        // Validate all requirements
        const isValid = Object.values(validations).every(v => v);
        
        if (!isValid) {
            console.log('Validation failed:', validations);
            notification.error({
                message: t("Validation Error"),
                description: t("Please ensure all password requirements are met"),
                duration: 3
            });
            return;
        }

        if (!token) {
            console.error('Token is missing');
            notification.error({
                message: t("Error"),
                description: t("Authentication token is missing. Please try again."),
                duration: 3
            });
            setErrorMessageModal(true);
            return;
        }

        try {
            console.log('Sending password update request to backend...');
            const response = await axios.post(
                `${import.meta.env.VITE_APP_API_BASE_URL}/set-password`,
                {
                    token: token,
                    password: password,
                    confirmPassword: confirmPassword
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                    }
                }
            );

            console.log('Backend response:', response.data);

            if(response.data.includes("User with this email already exists") && response.data.includes("error_code:409")) {
                notification.error({
                    message: t("Error"),
                    description: t("User already exists. Please Sign in."),
                    duration: 3
                });
                setErrorMessageModal(true);
                throw new Error("User already exists");
            }

            if (response.data && response.status === 200 && response.data.includes("User created")) {
                notification.success({
                    message: t("Success"),
                    description: t("Password set successfully!"),
                    duration: 3
                });
                setShowSuccessModal(true);
                setErrorMessageModal(false);
            }

        } catch (error) {
                console.error('Error setting password:', error);
                console.error('Error response:', error.response?.data);
                setErrorMessageModal(true);
        }
    };

    const SuccessModal = () => {
        return (
            <Modal
                open={true}
                title={t("Success")}
                onOk={handleProceedToSignIn}
                onCancel={handleProceedToSignIn}
                okText={t("Proceed to Sign In")}
                cancelText={null}
                centered
            >
                <div style={{ textAlign: "center" }}>
                    <FaCheckCircle style={{ fontSize: "50px", color: "#28a745", marginBottom: "15px" }} />
                    <p className="success-message">
                        {t("User successfully created! Your password has been set.")}
                    </p>
                </div>
            </Modal>
        );
    };

    const ErrorMessageModal = () => {
        return (
            <Modal
                open={true}
                title={t("Error")}
                onOk={() => setErrorMessageModal(false)}
                onCancel={() => setErrorMessageModal(false)}
                centered
            >
                <div style={{ textAlign: "center" }}>
                    <ExclamationCircleOutlined style={{ fontSize: "50px", color: "#dc3545", marginBottom: "15px" }} />
                    <p className="error-message">
                        {t("User already exists. Please Sign in.")}
                    </p>
                </div>
            </Modal>
        );
    };


    return (
        <>
            <Header />
            <div className="page-wrapper">
                <div className="registration-container">
                    <div className="registration-row" dir={i18n.language === "ar" ? "rtl" : "ltr"}>
                        <div 
                            className="left-section" 
                            style={{ backgroundImage: `url(${backgroundImage})` }}
                        >
                            <div className="overlay-text">
                                <h2>{t("Welcome!")}</h2>
                            </div>
                        </div>

                        <div className="right-section">
                            <h2 className="signup-heading">{t("Set Your Password")}</h2>

                            <div className="signup-form">
                                <div className="password-requirements">
                                    <div className={`requirement ${validations.minLength ? 'valid' : ''}`}>
                                        <span>✓</span> {t("At least 8 characters")}
                                    </div>
                                    <div className={`requirement ${validations.hasUpper ? 'valid' : ''}`}>
                                        <span>✓</span> {t("At least one uppercase letter")}
                                    </div>
                                    <div className={`requirement ${validations.hasLower ? 'valid' : ''}`}>
                                        <span>✓</span> {t("At least one lowercase letter")}
                                    </div>
                                    <div className={`requirement ${validations.hasNumber ? 'valid' : ''}`}>
                                        <span>✓</span> {t("At least one number")}
                                    </div>
                                    <div className={`requirement ${validations.hasSpecial ? 'valid' : ''}`}>
                                        <span>✓</span> {t("At least one special character")}
                                    </div>
                                </div>

                                <form onSubmit={handleSubmit}>
                                    <div className="form-group">
                                        <label>{t("Password")}</label>
                                        <div className="password-input-container">
                                            <input
                                                type={showPassword ? "text" : "password"}
                                                placeholder={t("Enter Password")}
                                                value={password}
                                                onChange={handlePasswordChange}
                                                className="form-control"
                                            />
                                            <button 
                                                type="button" 
                                                className="password-toggle-btn"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? <FaEyeSlash /> : <FaEye />}
                                            </button>
                                        </div>
                                    </div>

                                    <div className="form-group">
                                        <label>{t("Confirm Password")}</label>
                                        <div className="password-input-container">
                                            <input
                                                type={showConfirmPassword ? "text" : "password"}
                                                placeholder={t("Confirm Password")}
                                                value={confirmPassword}
                                                onChange={handleConfirmPasswordChange}
                                                className="form-control"
                                            />
                                            <button 
                                                type="button" 
                                                className="password-toggle-btn"
                                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                            >
                                                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                                            </button>
                                        </div>
                                    </div>
                                    
                                    {confirmPassword && (
                                        <div className={`requirement ${validations.matches ? 'valid' : ''}`}>
                                            <span>✓</span> {t("Passwords match")}
                                        </div>
                                    )}

                                    <div className="form-buttons">
                                        
                                        <Form.Item>
                                    <Space>
                                        <Button type="primary" htmlType="submit" 
                                        disabled={!Object.values(validations).every(v => v)}>
                                            {t("Set Password")}
                                        </Button>
                                        <Button htmlType="reset">{t("Reset")}</Button>
                                    </Space>
                                </Form.Item>
                           

                                    </div>

                                    <div className="signin-link">
                                        <span onClick={() => navigate("/signin")}>{t("Back To Login")}</span>
                                    </div>

                                    <div className="signin-link">
                                        {t("Don't have an account?")} {" "}
                                        <span onClick={() => navigate("/signup")}>{t("Sign up")}</span>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {showSuccessModal && <SuccessModal />}
            {errorMessageModal && <ErrorMessageModal />}
            <Footer />
        </>
    );
};

export default SetPassword;