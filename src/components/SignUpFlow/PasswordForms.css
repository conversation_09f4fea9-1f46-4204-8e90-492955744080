/* Combined CSS for Password-related pages (ForgetPassword, SetPassword) */

/* Core page layout */
.page-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem;
    background-color: #f0f2f5;
}

.registration-container {
    width: 100%;
    max-width: 1200px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Segoe UI', sans-serif;
}

.registration-row {
    width: 100%;
    display: flex;
}

/* Left side (image section) */
.left-section {
    width: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 60px 30px;
    color: white;
    position: relative;
    min-height: 450px;
}

/* Add overlay background to improve text readability */
.left-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
    z-index: 1;
}

.overlay-text {
    position: relative;
    z-index: 2; /* Place text above the overlay */
    padding: 20px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.overlay-text h2 {
    font-size: 28px;
    margin-bottom: 20px;
}

.overlay-text p {
    font-size: 14px;
    line-height: 1.6;
}

/* Right side (form section) */
.right-section {
    width: 50%;
    background-color: #ffffff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.signup-heading {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.signup-form {
    width: 100%;
    max-width: 400px;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #9333ea;
    border-radius: 6px;
    height: 40px;
    font-size: 14px;
}

.password-input-container {
    position: relative;
    width: 100%;
}

.password-input-container input {
    width: 100%;
    padding-right: 40px; /* Space for the eye icon */
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;           /* Center vertically */
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 16px;
    padding: 0;
    width: 24px;      /* Fixed width for the button */
    height: 24px;     /* Fixed height for the button */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure the icon itself is centered */
.password-toggle-btn svg {
    width: 16px;
    height: 16px;
    display: block;
}

.password-toggle-btn:hover {
    color: #333;
}

/* Button styles */
.form-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 16px;
    margin: 24px auto;
}

.submit-btn,
.reset-btn {
    width: 100%;
    max-width: 300px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.submit-btn {
    background-color: #9333ea;
    color: white;
    border: none;
}

.submit-btn:hover {
    background-color: #7e22ce;
}

.submit-btn:disabled {
    background-color: #d8b4fe;
    cursor: not-allowed;
}

.reset-btn {
    background-color: white;
    border: 1px solid #22c55e;
    color: #22c55e;
}

.reset-btn:hover {
    background-color: #f0fdf4;
}

/* Links and text */
.signin-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: #666;
}

.signin-link span {
    color: #9333ea;
    cursor: pointer;
    font-weight: 500;
}

.signin-link span:hover {
    text-decoration: underline;
}

/* Password requirements and instructions */
.password-requirements {
    margin: 0 0 20px 0;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.requirement {
    display: flex;
    align-items: center;
    margin: 8px 0;
    color: #6c757d;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.requirement span {
    margin-right: 8px;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.requirement.valid {
    color: #28a745;
}

.requirement.valid span {
    opacity: 1;
}

.password-instruction {
    margin-bottom: 20px;
    color: #666;
    text-align: center;
}

.reset-success {
    text-align: center;
}

.reset-success p {
    margin-bottom: 16px;
    color: #333;
}

/* Media Queries for Responsiveness */
@media (max-width: 1024px) {
    .page-wrapper {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .registration-row {
        flex-direction: column;
    }

    .left-section,
    .right-section {
        width: 100%;
    }

    .left-section {
        min-height: 250px;
        padding: 30px 20px;
    }

    .right-section {
        padding: 30px 20px;
    }

    .overlay-text h2 {
        font-size: 24px;
        margin-bottom: 15px;
    }

    .signup-heading {
        font-size: 22px;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .page-wrapper {
        padding: 0.5rem;
        min-height: 70vh;
    }

    .registration-container {
        box-shadow: none;
    }

    .left-section {
        min-height: 200px;
    }

    .signup-form {
        padding: 1rem 0.5rem;
        box-shadow: none;
    }

    .form-buttons {
        gap: 12px;
    }

    .submit-btn,
    .reset-btn {
        padding: 10px 16px;
        font-size: 14px;
    }
}