.page-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
  background-color: #f0f2f5;
}

.forget-container {
  width: 100%;
  max-width: 1200px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  font-family: 'Segoe UI', sans-serif;
}

.forget-left {
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 30px 20px;
  color: white;
  text-align: center;
  position: relative;
  min-height: 300px;
  background-color: #9333ea; /* Fallback color */
}

/* Add overlay background to improve text readability */
.forget-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
  z-index: 1;
}

@media (min-width: 768px) {
  .forget-left {
      width: 50%;
      padding: 60px 30px;
  }
}

.forget-welcome-box {
  position: relative;
  z-index: 2; /* Place text above the overlay */
  padding: 20px;
  width: 100%;
  max-width: 400px; /* Limit text width */
  text-align: center;
}

.forget-welcome-box h2 {
  font-size: 28px;
  margin-bottom: 20px;
}

.forget-right {
  flex: 1;
  background-color: #ffffff;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.forget-heading {
  font-size: 26px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  color: #333;
}

.forget-subtext {
  text-align: center;
  margin-bottom: 20px;
  color: #666;
}

.forget-form {
  width: 100%;
  max-width: 450px;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.forget-form label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.password-input-container {
  position: relative;
  margin-bottom: 20px;
}

.password-input-container input {
  width: 100%;
  padding: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  height: 40px;
  padding-right: 40px; /* Space for the eye icon */
}

.password-toggle-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  font-size: 16px;
}

.form-buttons {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  justify-content: center;
}

.submit-btn, .reset-btn {
  padding: 10px 20px;
  border-radius: 6px;
  height: 40px;
  min-width: 120px;
  cursor: pointer;
  font-weight: 500;
}

.submit-btn {
  background-color: #9333ea;
  color: white;
  border: none;
}

.submit-btn:hover {
  background-color: #7e22ce;
}

.reset-btn {
  background-color: white;
  color: #333;
  border: 1px solid #d9d9d9;
}

.reset-btn:hover {
  border-color: #9333ea;
  color: #9333ea;
}

.extra-links {
  text-align: center;
  margin: 20px 0;
  color: #666;
}

.extra-links span {
  color: #1890ff;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.3s;
}

.extra-links span:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.signup-link {
  text-align: center;
  margin-top: 25px;
  font-size: 14px;
  color: #666;
}

.signup-link span {
  color: #1890ff;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.3s;
}

.signup-link span:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* RTL Support */
[dir="rtl"] .forget-container {
  flex-direction: row-reverse;
}

[dir="rtl"] .password-toggle-btn {
  right: auto;
  left: 10px;
}

/* Media Queries for Responsiveness */
@media (max-width: 1024px) {
  .page-wrapper {
      padding: 1rem;
  }
}

@media (max-width: 768px) {
  .forget-container {
      flex-direction: column;
  }

  .forget-left,
  .forget-right {
      width: 100%;
      padding: 30px 20px;
  }

  .forget-left {
      min-height: 200px;
  }

  .forget-form {
      padding: 1.5rem;
      box-shadow: none;
  }

  .form-buttons {
      flex-direction: column;
      align-items: center;
      gap: 20px;
  }

  .submit-btn, .reset-btn {
      width: 80%;
      max-width: 300px;
  }
}

@media (max-width: 480px) {
  .page-wrapper {
      padding: 0.5rem;
      min-height: 70vh;
  }

  .forget-container {
      box-shadow: none;
  }

  .forget-left {
      min-height: 150px;
      padding: 15px;
  }

  .forget-right {
      padding: 20px 15px;
  }
  
  .forget-form {
      padding: 1rem 0.5rem;
  }
  
  .forget-heading {
      font-size: 22px;
      margin-bottom: 15px;
  }
  
  .password-input-container input {
      height: 36px;
  }
  
  .form-buttons {
      flex-direction: column;
      gap: 10px;
  }
  
  .submit-btn, .reset-btn {
      width: 100%;
  }
}