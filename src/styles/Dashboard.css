.create-order-button {
    background-color: #9e3ca2;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 1rem;
    /* display: flex; */
    /* align-items: flex-end; */
    float: right;
    
    /* justify-content: center; */
    width: 200px;
    /* margin-left: 90%; */
}

/* Dashboard Layout */
.dashboard-container {
  padding: 24px;
  background-color: #f8fafc;
}

/* Stats Cards */
.ant-card.stats-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  overflow: hidden;
}

.ant-card.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.ant-card.stats-card .ant-card-body {
  padding: 20px;
}

/* Chart Card */
.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 100%;
}

/* Notifications Card */
.notifications-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 100%;
}

.notifications-card .ant-card-body {
  padding: 0;
}

.notifications-container {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
}

/* Activity Table */
.activity-card {
  margin-top: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.activity-card .ant-card-body {
  padding: 0;
}

.activity-table {
  margin-bottom: 0;
}

.view-all-btn {
  text-align: center;
  padding: 16px;
  border-top: 1px solid #f1f5f9;
}

/* Stats Card Content */
.stats-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin-right: 8px;
}

.stats-change {
  font-size: 13px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.stats-subtitle {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .stats-card {
    margin-bottom: 16px;
  }
}