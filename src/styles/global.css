:root {
  /* Colors */
  --primary-color: #9e3ca2;
  --primary-light: #b96bbd;
  --primary-dark: #7e2a82;
  --secondary-color: #6c5ce7;
  --success-color: #00b894;
  --warning-color: #fdcb6e;
  --error-color: #ff7675;
  --text-primary: #2d3436;
  --text-secondary: #636e72;
  --background: #f8f9fa;
  --card-bg: #ffffff;
  --border-color: #dfe6e9;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
}

/* Base styles */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--background);
  line-height: 1.5;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Cards */
.ant-card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-md);
}

.ant-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Buttons */
.ant-btn {
  border-radius: var(--border-radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
}

.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Forms */
.ant-form-item-label > label {
  font-weight: 500;
  color: var(--text-secondary);
}

.ant-input,
.ant-input-password,
.ant-select-selector,
.ant-picker {
  border-radius: var(--border-radius-md) !important;
}

/* Layout */
.ant-layout {
  background: var(--background);
}

.ant-layout-header {
  background: var(--card-bg);
  padding: 0 var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 10;
}

/* Responsive utilities */
@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}
