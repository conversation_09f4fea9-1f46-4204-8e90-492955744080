import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.jsx';
import i18n from './i18n.js';
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import themeConfig from './theme/themeConfig';
import './assets/fonts/fonts.css';
import { Provider } from 'react-redux';
import { store } from './redux/Store/store';
// Import dayjs configuration
import './utils/dayjsConfig';

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          <ConfigProvider theme={themeConfig}>
            <App />
          </ConfigProvider>
        </I18nextProvider>
      </BrowserRouter>
    </Provider>
  </StrictMode>
);
