/* Arabic font */
/* Option 1: Local files (uncomment when files are available) */
/*
@font-face {
  font-family: 'Cairo';
  src: url('../Cairo-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Cairo';
  src: url('../Cairo-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
*/

/* Option 2: Google Fonts CDN (add the link in index.html) */
/*
   Add to index.html:
   <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
*/

/* Inter font family */
@font-face {
  font-family: 'Inter';
  src: url('../Inter-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../Inter-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../Inter-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../Inter-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../Inter-ExtraBold.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../Inter-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
}

/* For backward compatibility */
@font-face {
  font-family: 'Inter-Black';
  src: url('../Inter-Black.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter-Bold';
  src: url('../Inter-Bold.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter-ExtraBold';
  src: url('../Inter-ExtraBold.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

