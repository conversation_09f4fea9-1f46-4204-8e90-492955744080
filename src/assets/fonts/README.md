# Font Setup

This project uses the following fonts:

1. **Inter** - For English and general UI
   - Available in multiple weights (Regular, Medium, SemiBold, Bold, ExtraBold, Black)
   - Used as the primary font for the application

2. **Cairo** - For Arabic text
   - Used when the application is in RTL mode

## Usage

The fonts are defined in `fonts.css` and imported in `main.jsx`.

To use the Inter font in your components:
```css
font-family: 'Inter', sans-serif;
```

To specify font weight:
```css
font-family: 'Inter', sans-serif;
font-weight: 400; /* Regular */
font-weight: 500; /* Medium */
font-weight: 600; /* SemiBold */
font-weight: 700; /* Bold */
font-weight: 800; /* ExtraBold */
font-weight: 900; /* Black */
```

For Cairo font (Arabic):
```css
font-family: 'Cairo', sans-serif;
```

## Note
The Cairo font files (Cairo-Regular.ttf and Cairo-Bold.ttf) need to be downloaded and placed in the assets directory.
