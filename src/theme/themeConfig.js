// Ant Design theme configuration
const themeConfig = {
  token: {
    // Colors from the UI design
    colorPrimary: '#9e3ca2', // purple color
    colorSecondary: '#51ae52', // green color
    colorSuccess: '#51ae52',
    colorInfo: '#9e3ca2',

    // Font settings
    fontFamily: 'Inter',
    fontSize: 14,

    // Border radius
    borderRadius: 5,

    // Other customizations
    colorBgContainer: '#ffffff',
    colorTextBase: '#000000',
  },
  components: {
    Button: {
      colorPrimary: '#9e3ca2',
      colorPrimaryHover: '#3d8a3e',
      borderRadius: 5,
      controlHeight: 40,
    },
    Steps: {
      colorPrimary: '#9e3ca2',
      controlItemBgActive: '#f0e6f0',
    },
    Input: {
      colorBorder: '#d9d9d9',
      borderRadius: 5,
    },
    Upload: {
      colorPrimary: '#9e3ca2',
    },
  },
};

export default themeConfig;
