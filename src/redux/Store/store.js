import { configureStore } from "@reduxjs/toolkit";
import languageReducer from "../Slice/languageSlice";
import authReducer from "../Slice/authSlice";

// Custom middleware for debugging
const loggerMiddleware = store => next => action => {
  console.log('Dispatching action:', action);
  const result = next(action);
  console.log('Next state:', store.getState());
  return result;
};

// Create a function to check localStorage on startup
const checkLocalStorage = () => {
  console.log("Checking localStorage on startup:");
  console.log("- accessToken:", localStorage.getItem("accessToken") ? "exists" : "missing");
  console.log("- refreshToken:", localStorage.getItem("refreshToken") ? "exists" : "missing");

  const userJson = localStorage.getItem("user");
  console.log("- user JSON:", userJson);

  try {
    if (userJson) {
      const userData = JSON.parse(userJson);
      console.log("- parsed user data:", userData);
    } else {
      console.log("- user data is null or empty");
    }
  } catch (error) {
    console.error("- error parsing user data:", error);
  }
};

// Check localStorage when this file loads
checkLocalStorage();

export const store = configureStore({
  reducer: {
    language: languageReducer,
    auth: authReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(loggerMiddleware)
});