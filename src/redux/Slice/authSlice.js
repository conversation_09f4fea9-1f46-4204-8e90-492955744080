import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    accessToken: localStorage.getItem("accessToken") || null,
    refreshToken: localStorage.getItem("refreshToken") || null,
    user: JSON.parse(localStorage.getItem("user")) || null,
    isAuthenticated: !!localStorage.getItem("accessToken"),
    profileComplete: localStorage.getItem("profileComplete") === "true" || false,
    isRefreshing: false,
    refreshError: null
};

const authSlice = createSlice({
    name: "auth",
    initialState,
    reducers: {
        setTokens: (state, action) => {
            const { accessToken, refreshToken, user } = action.payload;
            state.accessToken = accessToken;
            state.refreshToken = refreshToken;
            state.user = user;
            state.isAuthenticated = true;
            state.refreshError = null;
            localStorage.setItem("accessToken", accessToken);
            localStorage.setItem("refreshToken", refreshToken);
            localStorage.setItem("user", JSON.stringify(user));
        },
        clearAuth: (state) => {
            state.accessToken = null;
            state.refreshToken = null;
            state.user = null;
            state.isAuthenticated = false;
            state.profileComplete = false;
            state.isRefreshing = false;
            state.refreshError = null;
            localStorage.removeItem("accessToken");
            localStorage.removeItem("refreshToken");
            localStorage.removeItem("user");
            localStorage.removeItem("profileComplete");
        },
        setProfileComplete: (state, action) => {
            state.profileComplete = action.payload;
            localStorage.setItem("profileComplete", action.payload);
        },
        setRefreshing: (state, action) => {
            state.isRefreshing = action.payload;
            if (action.payload) {
                state.refreshError = null;
            }
        },
        setRefreshError: (state, action) => {
            state.refreshError = action.payload;
            state.isRefreshing = false;
        },
        refreshTokenSuccess: (state, action) => {
            const { accessToken, refreshToken, user } = action.payload;
            state.accessToken = accessToken;
            if (refreshToken) {
                state.refreshToken = refreshToken;
            }
            if (user) {
                state.user = user;
            }
            state.isAuthenticated = true;
            state.isRefreshing = false;
            state.refreshError = null;
            localStorage.setItem("accessToken", accessToken);
            if (refreshToken) {
                localStorage.setItem("refreshToken", refreshToken);
            }
            if (user) {
                localStorage.setItem("user", JSON.stringify(user));
            }
        }
    }
});

export const {
    setTokens,
    clearAuth,
    setProfileComplete,
    setRefreshing,
    setRefreshError,
    refreshTokenSuccess
} = authSlice.actions;
export default authSlice.reducer;