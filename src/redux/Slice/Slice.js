// import { createSlice } from "@reduxjs/toolkit";
// import i18n from "../../i18n";
// import i18next from "i18next";

// const initialState = {
//   language: localStorage.getItem("i18nextLng") || "ar",
//   auth: {
//     accessToken: null,
//     refreshToken: null,
//     user: null,
//     isAuthenticated: false
//   }
// };

// const appSlice = createSlice({
//   name: "app",
//   initialState,
//   reducers: {
//     setLanguage: (state, action) => {
//       const lang = action.payload;
//       state.language = lang;
//       localStorage.setItem("i18nextLng", lang);
//       i18n.changeLanguage(lang).then(() => {
//         const dir = i18n.t("_dir");
//         document.documentElement.dir = dir || "rtl";
//         document.body.style.direction = dir || "rtl";
//       });
//     },
//     setTokens: (state, action) => {
//       const { accessToken, refreshToken, user } = action.payload;
//       state.auth.accessToken = accessToken;
//       state.auth.refreshToken = refreshToken;
//       state.auth.user = user;
//       state.auth.isAuthenticated = true;
//       // Store tokens in localStorage for persistence
//       localStorage.setItem("accessToken", accessToken);
//       localStorage.setItem("refreshToken", refreshToken);
//       localStorage.setItem("user", JSON.stringify(user));
//     },
//     clearAuth: (state) => {
//       state.auth = {
//         accessToken: null,
//         refreshToken: null,
//         user: null,
//         isAuthenticated: false
//       };
//       // Clear tokens from localStorage
//       localStorage.removeItem("accessToken");
//       localStorage.removeItem("refreshToken");
//       localStorage.removeItem("user");
//     }
//   },
// });

// export const { setLanguage, setTokens, clearAuth } = appSlice.actions;
// export default appSlice.reducer;