import { createSlice } from "@reduxjs/toolkit";
import i18n from "../../i18n";

const initialState = {
    language:  localStorage.getItem("i18nextLng") || "ar"
};

const languageSlice = createSlice({
    name: "language",
    initialState,
    reducers: {
        setLanguage: (state, action) => {
            const lang = action.payload;
            state.language = lang;
            localStorage.setItem("i18nextLng", lang);
            i18n.changeLanguage(lang).then(() => {
                const dir = i18n.t("_dir");
                document.documentElement.dir = dir || "rtl";
                document.body.style.direction = dir || "rtl";
            });
        }
    }
});

export const { setLanguage } = languageSlice.actions;
export default languageSlice.reducer;