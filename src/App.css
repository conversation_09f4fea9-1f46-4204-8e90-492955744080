@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

/* General Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: #ffffff;
  box-sizing: border-box;
  text-align: center;
  direction: ltr;
}

/* RTL language font */
[dir="rtl"] body {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  text-align: left;
}

.subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 30px;
}

[dir="rtl"] .title,
.subtitle {
  margin-right: 40px;
}

[dir="ltr"] .title,
.subtitle {
  margin-left: 40px;
}


[dir="rtl"] .explore-btn {
  margin-left: 40px;
}

.explore-btn:hover {
  background-color: #9e3ca2;
  color: white;
}

/* rtl.css */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

.rtl {
  direction: rtl;
}

/* Layout adjustments */
[dir="rtl"] .nav-bar {
  flex-direction: row-reverse;
}

[dir="rtl"] .navMenu {
  margin-right: auto;
  margin-left: 0;
}

/* Form elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
}

/* Dropdowns */
[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
  text-align: right;
}